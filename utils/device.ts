import * as Device from 'expo-device'
import { Dimensions } from 'react-native'

/**
 * Get device screen dimensions
 */
export const getScreenDimensions = () => {
  const { width, height } = Dimensions.get('window')
  return { width, height }
}

/**
 * Check if the device is a tablet based on screen size and device type
 */
export const isTablet = (checkDimension: boolean = false): boolean => {
  if (checkDimension) {
    const { width, height } = getScreenDimensions()
    return width >= 600 || height >= 600
  }
  return Device.deviceType === Device.DeviceType.TABLET
}

/**
 * Get responsive tab bar height based on device type
 */
export const getTabBarHeight = (): number => {
  return isTablet() ? 72 : 56
}

/**
 * Get responsive spacing multiplier based on device type
 */
export const getSpacingMultiplier = (): number => {
  return isTablet() ? 1.2 : 1
}
