import { Media } from '@/types/media.type'

/**
 * Get the preferred URL of a Media or string.
 * By default, tries `url` first, then `thumbnailURL`.
 */
export const getMediaUrl = (
  media?: string | Media | null | undefined,
  preferThumbnail = false,
): string | undefined => {
  if (!media) return undefined
  if (typeof media === 'string') return media

  // order: url first unless preferThumbnail is true
  return preferThumbnail
    ? media.thumbnailURL || media.url || undefined
    : media.url || media.thumbnailURL || undefined
}

/**
 * Get width and height from Media object
 */
export const getMediaSizes = (
  media?: string | Media | null | undefined,
): Media['sizes'] | undefined => {
  if (!media) return undefined
  if (typeof media === 'string') return undefined

  return media.sizes
}

/**
 * Return both the resolved URL and original Media object
 */
export const getMediaInfo = (
  media?: string | Media | null,
  preferThumbnail = false,
): { url?: string; media?: Media | string | null } => {
  return {
    url: getMediaUrl(media, preferThumbnail),
    media,
  }
}
