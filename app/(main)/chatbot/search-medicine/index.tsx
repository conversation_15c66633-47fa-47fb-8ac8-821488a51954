import { BaseWebView } from '@/components/BaseWebView/BaseWebView'
import { StackScreenBase } from '@/components/StackBase/StackScreenBase'
import { TutorialManualScreen } from '@/features/chats/screens/TutorialManualScreen/TutorialManualScreen'
import { withAuthentication } from '@/hoc/withAuthentication'
import { WEBVIEW_APP_ROUTES } from '@/routes/webviewRoutes'
import React from 'react'
import { useTranslation } from 'react-i18next'
import { SafeAreaView } from 'react-native-safe-area-context'

function SearchMedicineScreen() {
  const { t } = useTranslation()

  const [showTutorial, setShowTutorial] = React.useState(true)

  return (
    <>
      {showTutorial ? (
        <TutorialManualScreen
          onOpenChatBot={() => {
            setShowTutorial(false)
          }}
        />
      ) : (
        <>
          <StackScreenBase
            options={{
              headerTitle: t('MES-642'),
            }}
          />
          <SafeAreaView className="flex-1 bg-white" edges={['left', 'right', 'bottom']}>
            <BaseWebView
              source={{
                uri: WEBVIEW_APP_ROUTES.CHAT_BOT?.children?.CHAT_BOT_SEARCH_MEDICINE?.path,
              }}
            />
          </SafeAreaView>
        </>
      )}
    </>
  )
}

export default withAuthentication(SearchMedicineScreen)
