import { ChatAgentScreen } from '@/features/chats/screens/ChatAgentScreen/ChatAgentScreen'
import { withAuthentication } from '@/hoc/withAuthentication'
import React from 'react'
import { SafeAreaView } from 'react-native-safe-area-context'
function ChatAgentAppScreen() {
  return (
    <SafeAreaView className="bg-white" style={{ flex: 1 }} edges={['left', 'top', 'right']}>
      <ChatAgentScreen />
    </SafeAreaView>
  )
}
export default withAuthentication(ChatAgentAppScreen)
