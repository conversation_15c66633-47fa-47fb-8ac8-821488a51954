import { HapticTab } from '@/components/HapticTab'
import colors, { primary } from '@/styles/_colors'
import { getDynamicTabBarHeight } from '@/styles/_variables'
import { Tabs } from 'expo-router'
import React from 'react'
import { Platform } from 'react-native'
function ChatAgentLayout() {
  return (
    <Tabs
      screenOptions={{
        headerShown: false,
        tabBarActiveTintColor: primary[500],
        tabBarInactiveTintColor: '#8B8C99',
        tabBarStyle: {
          ...Platform.select({
            ios: {
              shadowColor: '#000',
              shadowOffset: { width: 0, height: -1.2 },
              shadowOpacity: 0.1,
              shadowRadius: 1.2,
              borderTopWidth: 0,
            },
            android: {
              elevation: 8,
              borderTopWidth: 0.5,
              borderTopColor: colors.divider?.default || '#E5E5E5',
            },
          }),
          height: getDynamicTabBarHeight(),
          paddingBottom: Platform.OS === 'ios' ? 20 : 10,
          backgroundColor: 'white',
        },
        tabBarButton: (props) => <HapticTab {...props} />,
      }}
    >
      {/* Tab Chat thường */}
      <Tabs.Screen
        name="index"
        options={{
          title: 'Chat 234',
          tabBarIcon: ({ focused, color }) => <></>,
        }}
      />

      {/* Tab Chat Agent */}
      <Tabs.Screen
        name="agent"
        options={{
          title: 'AI Agent 23',
          tabBarIcon: ({ focused, color }) => <></>,
        }}
      />
    </Tabs>
  )
}

export default ChatAgentLayout
