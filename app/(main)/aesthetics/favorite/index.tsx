import { StackScreenBase } from '@/components/StackBase/StackScreenBase'
import { FavoriteAestheticsScreen } from '@/features/aesthetic/screens/FavoriteAestheticsScreen/FavoriteAestheticsScreen'
import { withAuthentication } from '@/hoc/withAuthentication'
import { useTranslation } from 'react-i18next'

function FavoriteAestheticsScreenApp() {
  const { t } = useTranslation()
  return (
    <>
      <StackScreenBase
        options={{
          headerTitle: t('MES-1011'),
          headerShown: true,
        }}
      />
      <FavoriteAestheticsScreen />
    </>
  )
}
export default withAuthentication(FavoriteAestheticsScreenApp)
