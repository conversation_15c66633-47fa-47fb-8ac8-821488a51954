import { StackScreenBase } from '@/components/StackBase/StackScreenBase'
import { AestheticDetailsScreen } from '@/features/aesthetic/screens/AestheticDetailsScreen/AestheticDetailsScreen'
import { useLocalSearchParams } from 'expo-router'
import React from 'react'
import { useTranslation } from 'react-i18next'
export default function PostDetailScreen() {
  const { t } = useTranslation()
  const { id } = useLocalSearchParams()

  return (
    <>
      <StackScreenBase
        options={{
          headerTitle: t('MES-1010'),
          headerShown: false,
        }}
      />
      <AestheticDetailsScreen id={id as string}></AestheticDetailsScreen>
    </>
  )
}
