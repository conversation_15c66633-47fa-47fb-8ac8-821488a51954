import { StackScreenBase } from '@/components/StackBase/StackScreenBase'
import { SearchAestheticScreen } from '@/features/aesthetic/screens/SearchAestheticScreen/SearchAestheticScreen'
import { useLocalSearchParams } from 'expo-router'
import { useTranslation } from 'react-i18next'

export default function SearchAestheticScreenApp() {
  const { t } = useTranslation()
  const { customTitle } = useLocalSearchParams()
  return (
    <>
      <StackScreenBase
        options={{
          headerTitle: customTitle ? (customTitle as string) : t('MES-66'),
        }}
      />
      <SearchAestheticScreen />
    </>
  )
}
