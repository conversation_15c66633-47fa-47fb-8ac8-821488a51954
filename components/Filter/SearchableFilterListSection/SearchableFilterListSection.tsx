import { BaseFilterListSection } from '@/components/Filter/BaseFilterListSection/BaseFilterListSection'
import { Text } from '@/components/ui/Text/Text'
import { useTranslation } from 'react-i18next'
import { TouchableOpacity } from 'react-native'
import Svg, { Path } from 'react-native-svg'

interface SearchableFilterListSectionProps<T extends Record<string, any>> {
  title: string
  data: T[]
  onSelectFilter?: (item: T) => void
  activeFilters?: string[]
  idKey: keyof T
  labelKey: keyof T
  isLoading?: boolean
  numColumns?: number
  shouldShowViewAllButton?: boolean
  onViewAll?: () => void
  limit?: number
  children?: React.ReactNode
}

/**
 * Enhanced filter list section with "View All" button support
 * Use this component when you want to show a limited list with option to view all
 */
export const SearchableFilterListSection = <T extends Record<string, any>>({
  title,
  data,
  onSelectFilter,
  activeFilters = [],
  idKey,
  labelKey,
  isLoading = false,
  numColumns = 2,
  shouldShowViewAllButton = false,
  onViewAll,
  children,
}: SearchableFilterListSectionProps<T>) => {
  const { t } = useTranslation()

  return (
    <BaseFilterListSection
      title={title}
      data={data}
      onSelectFilter={onSelectFilter}
      activeFilters={activeFilters}
      idKey={idKey}
      labelKey={labelKey}
      isLoading={isLoading}
      numColumns={numColumns}
    >
      {shouldShowViewAllButton && onViewAll && (
        <TouchableOpacity
          className="mx-auto mt-3 flex flex-row items-center gap-x-1"
          onPress={onViewAll}
        >
          <Text size="body6" variant="primary">
            {t('MES-476')}
          </Text>
          <Svg width={18} height={18} viewBox="0 0 18 18" fill="none">
            <Path
              d="M7.06836 13.9498L11.1434 9.8748C11.6246 9.39355 11.6246 8.60605 11.1434 8.1248L7.06836 4.0498"
              stroke="#1157C8"
              strokeWidth={1.5}
              strokeMiterlimit={10}
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </Svg>
        </TouchableOpacity>
      )}
      {children}
    </BaseFilterListSection>
  )
}
