import ArrowDownIcon from '@/assets/icons/arrow-down-icon.svg'
import InfoIcon from '@/assets/icons/info-circle-icon.svg'
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/Accordion/Accordion'
import { Text } from '@/components/ui/Text/Text'
import { StyledExpoImage } from '@/libs/styled'
import { Media } from '@/types/media.type'
import { cn } from '@/utils/cn'
import { isValidElement, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { View } from 'react-native'
interface BaseFilterNotesBoxProps {
  title?: string
  notes?: {
    title: string
    icon?: string | Media | React.ReactNode
    note: string
  }[]
  isLoading?: boolean
}
export const BaseFilterNotesBox = ({
  title,
  notes,
  isLoading = false,
}: BaseFilterNotesBoxProps) => {
  const { t } = useTranslation()
  const [activeAccordion, setActiveAccordion] = useState('1')
  return (
    <View className="mt-3">
      <Accordion
        variant="unfilled"
        defaultValue={['1'] as string[]}
        className="w-full rounded-lg bg-custom-informative-100"
        type="single"
        onValueChange={(value) => {
          setActiveAccordion(value[0])
        }}
      >
        <AccordionItem value="1" className={cn('w-full rounded-b-lg ')}>
          <AccordionTrigger className="w-full">
            <View className="w-full flex-row items-center justify-between">
              <View className="flex-row items-center gap-x-2">
                <InfoIcon width={18} height={18} />
                <Text size="body6" variant="primary">
                  {title ? title : t('MES-137')}
                </Text>
              </View>
              <View className={cn('transition-all', activeAccordion === '1' ? 'rotate-180' : '')}>
                <ArrowDownIcon width={18} height={18} />
              </View>
            </View>
          </AccordionTrigger>
          <AccordionContent className="rounded-b-lg ">
            {isLoading ? (
              <></>
            ) : (
              <View className="flex flex-col gap-y-2">
                {notes?.map((item, index) => {
                  const { title, icon, note } = item

                  // Helper function to get icon source URL
                  const getIconSource = (): string | undefined => {
                    if (!icon) return undefined
                    if (typeof icon === 'string') return icon
                    if (isValidElement(icon)) return undefined // ReactNode, handled separately
                    const iconMedia = icon as Media
                    return iconMedia?.thumbnailURL || iconMedia?.url || undefined
                  }

                  const iconSource = getIconSource()
                  const isReactNode = isValidElement(icon)

                  return (
                    <View key={index + title} className="flex flex-col gap-[6px]">
                      <View className="flex-row items-center gap-x-2">
                        <View className="aspect-square h-[20px] w-[20px] overflow-hidden">
                          {isReactNode ? (
                            icon
                          ) : iconSource ? (
                            <StyledExpoImage
                              source={{ uri: iconSource }}
                              className="h-full w-full"
                              contentFit="cover"
                            />
                          ) : null}
                        </View>
                        <View className="flex-1 flex-row items-center gap-x-2">
                          <Text size="body6">{title}</Text>
                        </View>
                      </View>
                      {note && <Text size="body7">{note}</Text>}
                    </View>
                  )
                })}
              </View>
            )}
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </View>
  )
}
