import ArrowLeftIcon from '@/assets/icons/arrow-left-black.svg'
import ClearInputIcon from '@/assets/icons/clear-input-icon.svg'
import SearchInputIcon from '@/assets/icons/search-input-icon.svg'
import { BaseFilterListSection } from '@/components/Filter/BaseFilterListSection/BaseFilterListSection'
import { Text } from '@/components/ui/Text/Text'
import { TextInput } from '@/components/ui/TextInput/TextInput'
import colors from '@/styles/_colors'
import { cn } from '@/utils/cn'
import { BottomSheetScrollView } from '@gorhom/bottom-sheet'
import { useCallback, useEffect, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { ColorValue, TouchableOpacity, View } from 'react-native'
import Animated, {
  FadeInDown,
  useSharedValue,
  withDelay,
  withTiming,
} from 'react-native-reanimated'

interface BaseSearchableFilterSectionProps<T extends Record<string, any>> {
  title: string
  backTitle?: string
  footerHeight: number
  data: T[]
  onBack: () => void
  onSelectFilter: (item: T) => void
  activeFilterIds: string[]
  idKey: keyof T
  labelKey: keyof T
  isLoading?: boolean
  emptyStateText?: string
  customFilterFn?: (item: T, searchValue: string) => boolean
  numColumns?: number
}

export const BaseSearchableFilterSection = <T extends Record<string, any>>({
  title,
  backTitle,
  footerHeight,
  data,
  onBack,
  onSelectFilter,
  activeFilterIds,
  idKey,
  labelKey,
  isLoading = false,
  emptyStateText,
  customFilterFn,
  numColumns = 2,
}: BaseSearchableFilterSectionProps<T>) => {
  const { t } = useTranslation()
  const [searchInputValue, setSearchInputValue] = useState('')

  // Animation values
  const opacity = useSharedValue(0)
  const translateX = useSharedValue(100)

  // Filter data based on search input
  const filteredData = useMemo(() => {
    if (!searchInputValue.trim()) {
      return data
    }

    const searchLower = searchInputValue.toLowerCase().trim()

    if (customFilterFn) {
      return data.filter((item) => customFilterFn(item, searchLower))
    }

    // Default filter: search in label
    return data.filter((item) => String(item[labelKey]).toLowerCase().includes(searchLower))
  }, [data, searchInputValue, labelKey, customFilterFn])

  // Handle search input change
  const handleSearchInputChange = useCallback((text: string) => {
    setSearchInputValue(text)
  }, [])

  // Start animations when component mounts
  useEffect(() => {
    opacity.value = withDelay(100, withTiming(1, { duration: 300 }))
    translateX.value = withDelay(100, withTiming(0, { duration: 300 }))

    // Cleanup function for exit animation
    return () => {
      opacity.value = withTiming(0, { duration: 200 })
      translateX.value = withTiming(100, { duration: 200 })
    }
  }, [opacity, translateX])

  // Handle smooth exit animation
  const handleBackPress = () => {
    // Clear search input
    setSearchInputValue('')

    opacity.value = withTiming(0, { duration: 200 })
    translateX.value = withTiming(100, { duration: 200 })

    // Close after animation completes
    setTimeout(() => {
      onBack()
    }, 200)
  }

  return (
    <Animated.View className="flex flex-1 flex-col gap-y-4 bg-white px-4">
      <Animated.View entering={FadeInDown.duration(400).delay(200)}>
        <TouchableOpacity onPress={handleBackPress} className="flex flex-row items-center gap-x-1">
          <ArrowLeftIcon stroke="#000" />
          <Text size="body10">{backTitle || title}</Text>
        </TouchableOpacity>
      </Animated.View>

      <Animated.View entering={FadeInDown.duration(400).delay(200)}>
        <View
          className={cn(
            'flex-row items-center gap-x-2 overflow-hidden rounded-lg border border-custom-divider bg-white px-3',
          )}
          style={{
            borderColor: searchInputValue
              ? (colors.primary[500] as unknown as ColorValue)
              : (colors.divider['border'] as unknown as ColorValue),
          }}
        >
          <SearchInputIcon width={18} height={18} />
          <TextInput
            placeholder={t('MES-66')}
            placeholderTextColor="#8B8C99"
            value={searchInputValue}
            onChangeText={handleSearchInputChange}
            className="p-0"
            wrapperClassName={cn('flex-1 !px-0', '!border-0 !border-none border-transparent')}
          />
          {searchInputValue && (
            <TouchableOpacity onPress={() => setSearchInputValue('')}>
              <ClearInputIcon width={16} height={16} />
            </TouchableOpacity>
          )}
        </View>
      </Animated.View>

      <Animated.View entering={FadeInDown.duration(300).delay(300)}>
        <BottomSheetScrollView
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{
            paddingBottom: footerHeight + 20,
          }}
          style={{
            paddingBottom: footerHeight + 20,
          }}
        >
          {filteredData.length === 0 ? (
            <View className="flex items-center justify-center py-8">
              <Text size="body6" variant="subdued" className="text-center">
                {emptyStateText || t('MES-747')}
              </Text>
            </View>
          ) : (
            <BaseFilterListSection
              isAccordion={false}
              title={title}
              data={filteredData}
              onSelectFilter={onSelectFilter}
              activeFilters={activeFilterIds}
              idKey={idKey}
              labelKey={labelKey}
              isLoading={isLoading}
              numColumns={numColumns}
            />
          )}
        </BottomSheetScrollView>
      </Animated.View>
    </Animated.View>
  )
}
