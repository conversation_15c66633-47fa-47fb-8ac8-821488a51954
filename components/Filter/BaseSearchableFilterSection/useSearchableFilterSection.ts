import { useMemo, useState } from 'react'

interface UseSearchableFilterSectionOptions<T> {
  data: T[]
  limit?: number
}

interface UseSearchableFilterSectionReturn<T> {
  showAll: boolean
  setShowAll: (show: boolean) => void
  limitedData: T[]
  shouldShowViewAllButton: boolean
  fullData: T[]
}

/**
 * Hook to manage searchable filter section state and data
 *
 * @example
 * ```tsx
 * // 1. Use the hook with your data
 * const myFilter = useSearchableFilterSection({
 *   data: myDataArray,
 *   limit: 6, // optional, defaults to 6
 * })
 *
 * // 2. Use SearchableFilterListSection for the limited view
 * <SearchableFilterListSection
 *   title="My Filter"
 *   data={myFilter.limitedData}
 *   onSelectFilter={handleToggle}
 *   activeFilters={activeIds}
 *   idKey="id"
 *   labelKey="label"
 *   shouldShowViewAllButton={myFilter.shouldShowViewAllButton}
 *   onViewAll={() => myFilter.setShowAll(true)}
 * />
 *
 * // 3. Conditionally render the full searchable view
 * {myFilter.showAll ? (
 *   <BaseSearchableFilterSection
 *     title="My Filter"
 *     footerHeight={sheetBoxRef.current?.getFooterHeight() || 80}
 *     data={myFilter.fullData}
 *     onBack={() => myFilter.setShowAll(false)}
 *     onSelectFilter={handleToggle}
 *     activeFilterIds={activeIds}
 *     idKey="id"
 *     labelKey="label"
 *   />
 * ) : (
 *   // ... normal view
 * )}
 * ```
 *
 * @param options - Configuration options
 * @param options.data - Full array of data items
 * @param options.limit - Number of items to show initially (default: 6)
 * @returns Object containing state and data helpers
 */
export const useSearchableFilterSection = <T>({
  data,
  limit = 6,
}: UseSearchableFilterSectionOptions<T>): UseSearchableFilterSectionReturn<T> => {
  const [showAll, setShowAll] = useState(false)

  const limitedData = useMemo(() => data.slice(0, limit), [data, limit])

  const shouldShowViewAllButton = useMemo(() => data.length > limit, [data.length, limit])

  return {
    showAll,
    setShowAll,
    limitedData,
    shouldShowViewAllButton,
    fullData: data,
  }
}
