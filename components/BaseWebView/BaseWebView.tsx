import { IS_NATIVE_ENVIRONMENT, WEB_VIEW_LANG_KEY, X_NATIVE_APP } from '@/constants/global.constant'
import { MESSAGE_NATIVE } from '@/constants/message-native.constant'
import {
  ACCESS_TOKEN,
  SHOW_FURIGANA,
  X_ACCESS_TOKEN,
  X_LOGIN_SESSION,
} from '@/constants/storage-key.constant'
import { useAuthentication } from '@/hooks/auth/useAuthentication'
import { useAppLanguage } from '@/hooks/common/useAppLanguage'
import { APP_ROUTES, AppRoutesEnum } from '@/routes/appRoutes'
import { WEBVIEW_APP_ROUTES } from '@/routes/webviewRoutes'
import { secureStoreService } from '@/services/secure-store/secure-store.service'
import { useUIConfigStore } from '@/stores/UIConfigStore/UIConfigStore'
import { LinkProps, useRouter } from 'expo-router'
import { openBrowserAsync } from 'expo-web-browser'
import React, { useEffect, useRef, useState } from 'react'
import { WebView, WebViewProps } from 'react-native-webview'
import { WebViewMessageEvent, WebViewSource } from 'react-native-webview/lib/WebViewTypes'
import { useCurvedTabBarSafeSpacing } from '../AppTabs/CurvedTabBar'

export interface BaseWebViewProps extends WebViewProps {
  isTabScreen?: boolean
  setupFurigana?: boolean
}

export const BaseWebView = (props: BaseWebViewProps) => {
  const { source, style, isTabScreen, onMessage, setupFurigana = false, ...rest } = props
  const webViewRef = useRef<WebView>(null)
  const [accessToken, setAccessToken] = useState<string | null>(null)
  const [sessionToken, setSessionToken] = useState<string | null>(null)
  const [webViewKey, setWebViewKey] = useState(new Date().getTime())
  const { primaryLanguage } = useAppLanguage()
  const { showFurigana, setShowFurigana } = useUIConfigStore()
  const initialShowFurigana = useRef<boolean>(showFurigana)
  const { status } = useAuthentication()
  const handleUpdateSourceURL = (): WebViewSource => {
    const baseSource = source as WebViewSource

    if ('uri' in baseSource) {
      const redirectUrl = encodeURIComponent(baseSource.uri)

      const fullUri = `${process.env.EXPO_PUBLIC_WEBVIEW_URL}${WEBVIEW_APP_ROUTES.NATIVE.path}?${IS_NATIVE_ENVIRONMENT}=true&${WEB_VIEW_LANG_KEY}=${primaryLanguage}&redirectUrl=${`/${primaryLanguage}` + redirectUrl}${setupFurigana ? `&${SHOW_FURIGANA}=${initialShowFurigana.current ? 'true' : 'false'}` : ''}`

      return {
        ...baseSource,
        uri: fullUri,
        headers: {
          ...(baseSource.headers ?? {}),
          [X_ACCESS_TOKEN]: accessToken ?? '',
          [X_LOGIN_SESSION]: sessionToken ?? '',
          [X_NATIVE_APP]: 'true',
        },
      }
    }

    return baseSource
  }
  const router = useRouter()

  const handleBaseMessage = async (e: WebViewMessageEvent) => {
    const event = JSON.parse(e.nativeEvent.data)
    switch (event.action) {
      case MESSAGE_NATIVE.redirectLogin:
        router.push({
          pathname: APP_ROUTES.LOGIN.path,
        } as LinkProps['href'])
        break

      case MESSAGE_NATIVE.redirectRegister:
        router.push({
          pathname: APP_ROUTES.REGISTER.path,
        } as LinkProps['href'])
        break

      case MESSAGE_NATIVE.openPostDetail: {
        const { payload } = event
        router.push({
          pathname: APP_ROUTES.POSTS.path + '/[slug]',
          params: {
            slug: payload.slug,
            id: payload.id,
          },
        } as LinkProps['href'])
        break
      }
      case MESSAGE_NATIVE.redirectToSearchMedicineScreen: {
        router.push({
          pathname: APP_ROUTES.CHAT_BOT.children?.CHAT_BOT_SEARCH_MEDICINE.path,
        } as LinkProps['href'])
        break
      }
      case MESSAGE_NATIVE.redirectToProductDetailScreen: {
        const { payload } = event
        router.push({
          pathname: APP_ROUTES.PRODUCTS.children?.PRODUCTS_DETAIL.path + '/[slug]',
          params: {
            slug: payload.slug,
            s: payload.s,
          },
        } as LinkProps['href'])
        break
      }
      case MESSAGE_NATIVE.openExternalLink: {
        const { payload } = event
        if (payload.url) {
          await openBrowserAsync(payload.url)
        }
        break
      }
      case MESSAGE_NATIVE.backToPreviousScreen: {
        router.back()
        break
      }
      case MESSAGE_NATIVE.redirectToScreen: {
        const { payload } = event

        if (payload.screen) {
          router.push({
            pathname: payload.screen,
          } as LinkProps['href'])
        }
        break
      }
      case MESSAGE_NATIVE.toggleFurigana: {
        if (setupFurigana) {
          const { payload } = event

          setShowFurigana(payload.isActive)
        }
        break
      }
      case MESSAGE_NATIVE.openInAppBrowser: {
        const { payload } = event
        if (payload.url) {
          await openBrowserAsync(payload.url)
        }
        break
      }
      case MESSAGE_NATIVE.openCommentScreen: {
        const { payload } = event
        const { id, relationTo, customTitle } = payload || {}
        if (id && relationTo) {
          router.push({
            pathname: APP_ROUTES.COMMENTS.path,
            params: {
              id,
              relationTo,
              customTitle: customTitle || '',
            },
          } as LinkProps['href'])
        }
        break
      }
      case MESSAGE_NATIVE.openKeywordDetailsScreen: {
        const { payload } = event
        const id = payload.keywordId
        router.push({
          pathname:
            APP_ROUTES.MEDICAL_DICTIONARY?.children?.[
              AppRoutesEnum.MEDICAL_DICTIONARY_KEYWORD_DETAILS
            ]?.path,
          params: {
            id: id,
          },
        } as LinkProps['href'])
        break
      }
      case MESSAGE_NATIVE.openChatSupportScreen: {
        if (status === 'unauthorized') {
          router.push({
            pathname: APP_ROUTES.LOGIN.path,
            params: {
              redirect: APP_ROUTES.CHAT_BOT?.children?.CHAT_WOOT?.path,
            },
          } as LinkProps['href'])
        } else {
          router.push({
            pathname: APP_ROUTES.CHAT_BOT?.children?.CHAT_WOOT?.path,
          } as LinkProps['href'])
        }
        break
      }

      default:
        break
    }
  }

  const injectedMetaScript = `(function() {
      const meta = document.createElement('meta'); meta.setAttribute('content', 'width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no'); meta.setAttribute('name', 'viewport'); document.getElementsByTagName('head')[0].appendChild(meta);     
      window.${IS_NATIVE_ENVIRONMENT}  = true;
   
    })();`

  const mergeInjectedScript = `${injectedMetaScript}`

  const getToken = async () => {
    const [accessToken, sessionToken] = await Promise.all([
      secureStoreService.getItem(ACCESS_TOKEN),
      secureStoreService.getItem(X_LOGIN_SESSION),
    ])
    setAccessToken(accessToken)
    setSessionToken(sessionToken)
    setWebViewKey((prev) => prev + 1)
  }

  const getInjectedCSS = (paddingBottom: number) => {
    return `(function() {
      const style = document.createElement('style');
      style.innerHTML = \`
        html {
          padding-bottom: ${isTabScreen ? paddingBottom : 0}px !important;
        }
  
        /* ✅ Override shadcn / radix dialog on the body */
        body > [role="dialog"] {
          padding-bottom: ${isTabScreen ? paddingBottom : 0}px !important;
        }
  
        /* ✅ Apply bottom spacing to scroll-to-top button */
        #scroll-to-top-button {
          margin-bottom: ${paddingBottom}px !important;
        }
      \`;
  
      if (document.head) {
        document.head.appendChild(style);
      } else {
        document.addEventListener('DOMContentLoaded', function() {
          document.head.appendChild(style);
        });
      }
    })();`
  }

  useEffect(() => {
    getToken()
  }, [])

  const safeSpacing = useCurvedTabBarSafeSpacing()
  const injectedCSS = isTabScreen ? getInjectedCSS(safeSpacing) : ''
  const fullInjectedScript = `${mergeInjectedScript}${injectedCSS ? `;${injectedCSS}` : ''}`

  return (
    <>
      <WebView
        key={webViewKey}
        ref={webViewRef}
        source={handleUpdateSourceURL() as WebViewSource}
        sharedCookiesEnabled={true}
        injectedJavaScriptBeforeContentLoaded={fullInjectedScript}
        injectedJavaScript={fullInjectedScript}
        style={[
          {
            flex: 1,
            backgroundColor: 'white',
          },
          style,
        ]}
        onMessage={(e) => {
          handleBaseMessage(e)
          onMessage?.(e)
        }}
        showsHorizontalScrollIndicator={false}
        showsVerticalScrollIndicator={false}
        scalesPageToFit={false}
        bounces={false}
        scrollEnabled={true}
        contentInsetAdjustmentBehavior="automatic"
        containerStyle={{
          backgroundColor: 'white',

          // paddingBottom: isTabScreen ? safeSpacing : 0
        }}
        {...rest}
      />
    </>
  )
}
