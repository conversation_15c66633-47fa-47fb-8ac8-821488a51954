diff --git a/node_modules/react-native-screens/ios/RNSScreen.h b/node_modules/react-native-screens/ios/RNSScreen.h
index 8308560..395569c 100644
--- a/node_modules/react-native-screens/ios/RNSScreen.h
+++ b/node_modules/react-native-screens/ios/RNSScreen.h
@@ -50,6 +50,7 @@ namespace react = facebook::react;
 - (void)calculateAndNotifyHeaderHeightChangeIsModal:(BOOL)isModal;
 - (void)notifyFinishTransitioning;
 - (RNSScreenView *)screenView;
+@property (nonatomic, readonly) BOOL disappeared;
 #ifdef RCT_NEW_ARCH_ENABLED
 - (void)setViewToSnapshot;
 - (CGFloat)calculateHeaderHeightIsModal:(BOOL)isModal;
diff --git a/node_modules/react-native-screens/ios/RNSScreen.mm b/node_modules/react-native-screens/ios/RNSScreen.mm
index b62a2e2..8a0cd0f 100644
--- a/node_modules/react-native-screens/ios/RNSScreen.mm
+++ b/node_modules/react-native-screens/ios/RNSScreen.mm
@@ -1434,6 +1434,7 @@ Class<RCTComponentViewProtocol> RNSScreenCls(void)
   int _dismissCount;
   BOOL _isSwiping;
   BOOL _shouldNotify;
+  BOOL _disappeared; 
 }
 
 #pragma mark - Common
@@ -1443,6 +1444,7 @@ Class<RCTComponentViewProtocol> RNSScreenCls(void)
   if (self = [super init]) {
     self.view = view;
     _fakeView = [UIView new];
+    _disappeared = NO;  
     _shouldNotify = YES;
 #ifdef RCT_NEW_ARCH_ENABLED
     _initialView = (RNSScreenView *)view;
@@ -1454,7 +1456,9 @@ Class<RCTComponentViewProtocol> RNSScreenCls(void)
 // TODO: Find out why this is executed when screen is going out
 - (void)viewWillAppear:(BOOL)animated
 {
+  
   [super viewWillAppear:animated];
+   _disappeared = NO; 
   if (!_isSwiping) {
     [self.screenView notifyWillAppear];
     if (self.transitionCoordinator.isInteractive) {
@@ -1481,7 +1485,9 @@ Class<RCTComponentViewProtocol> RNSScreenCls(void)
 
 - (void)viewWillDisappear:(BOOL)animated
 {
+ 
   [super viewWillDisappear:animated]; 
+  _disappeared = NO; 
   // self.navigationController might be null when we are dismissing a modal
   if (!self.transitionCoordinator.isInteractive && self.navigationController != nil) {
     // user might have long pressed ios 14 back button item,
@@ -1518,7 +1524,9 @@ Class<RCTComponentViewProtocol> RNSScreenCls(void)
 
 - (void)viewDidAppear:(BOOL)animated
 {
+ 
   [super viewDidAppear:animated];
+   _disappeared = NO; 
   if (!_isSwiping || _shouldNotify) {
     // we are going forward or dismissing without swipe
     // or successfully swiped back
@@ -1534,7 +1542,9 @@ Class<RCTComponentViewProtocol> RNSScreenCls(void)
 
 - (void)viewDidDisappear:(BOOL)animated
 {
+
   [super viewDidDisappear:animated];
+    _disappeared = YES; 
   if (self.parentViewController == nil && self.presentingViewController == nil) {
     if (self.screenView.preventNativeDismiss) {
       // if we want to prevent the native dismiss, we do not send dismissal event,
diff --git a/node_modules/react-native-screens/ios/RNSScreenStack.mm b/node_modules/react-native-screens/ios/RNSScreenStack.mm
index 229dc58..a77100a 100644
--- a/node_modules/react-native-screens/ios/RNSScreenStack.mm
+++ b/node_modules/react-native-screens/ios/RNSScreenStack.mm
@@ -857,7 +857,9 @@ RNS_IGNORE_SUPER_CALL_END
       [newControllers removeLastObject];
 
       [_controller setViewControllers:newControllers animated:NO];
-      [_controller pushViewController:top animated:YES];
+  if (!((RNSScreen *)top).disappeared) {
+    [_controller pushViewController:top animated:YES];
+}
     } else {
       // don't really know what this case could be, but may need to handle it
       // somehow
