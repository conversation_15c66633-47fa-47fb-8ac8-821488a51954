#!/bin/bash

# 
# Temporary solution to fix swipe issue.
#
# https://github.com/software-mansion/react-native-screens/issues/2559
#

check='disappeared'

screen_header='./node_modules/react-native-screens/ios/RNSScreen.h'
screen_mm='./node_modules/react-native-screens/ios/RNSScreen.mm'
screen_stack_mm='./node_modules/react-native-screens/ios/RNSScreenStack.mm'

if ! grep -qw $check $screen_header; then
  sed -i '' 's/(RNSScreenView \*)screenView;/(RNSScreenView *)screenView;\n@property (nonatomic, readonly) BOOL disappeared;/g' $screen_header
  echo 'RNSScreen.h hooked'
else
  echo  'RNSScreen.h already hooked, skip...'
fi

if ! grep -qw _$check $screen_mm; then
  sed -i '' 's/\[super viewWillAppear:animated\];/[super viewWillAppear:animated];\n  _disappeared = NO;/g' $screen_mm
  sed -i '' 's/\[super viewDidAppear:animated\];/[super viewDidAppear:animated];\n  _disappeared = NO;/g' $screen_mm
  sed -i '' 's/\[super viewWillDisappear:animated\];/[super viewWillDisappear:animated];\n  _disappeared = NO;/g' $screen_mm
  sed -i '' 's/\[super viewDidDisappear:animated\];/[super viewDidDisappear:animated];\n  _disappeared = YES;/g' $screen_mm

  sed -i '' 's/_fakeView = \[UIView new\];/_fakeView = [UIView new];\n    _disappeared = NO;/g' $screen_mm

  echo 'RNSScreen.mm hooked'
else
  echo  'RNSScreen.mm already hooked, skip...'
fi


if ! grep -qw $check $screen_stack_mm; then
    sed -i '' 's/\[_controller pushViewController:top animated:YES\];/if(!((RNSScreen *)top).disappeared) {\n        [_controller pushViewController:top animated:YES];\n      }/g' $screen_stack_mm
    echo 'RNSScreenStack.mm hooked'
else
  echo  'RNSScreenStack.mm already hooked, skip...'
fi

echo 'hook finish'