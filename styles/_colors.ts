// Color palette definitions converted from SCSS
export const colors = {
  primary: {
    50: '#f1f6fe',
    80: '#d9e7fc',
    100: '#afcbf8',
    200: '#80adf4',
    300: '#518ef0',
    400: '#226fec',
    500: '#1157c8',
    600: '#0d439b',
    700: '#092f6c',
  },

  neutral: {
    50: '#f6f6f6',
    80: '#ebeaea',
    100: '#dbdee0',
    200: '#b6bcc1',
    300: '#929ba2',
    400: '#6e7983',
    500: '#495764',
    600: '#253645',
    white: '#ffffff',
  },

  success: {
    50: '#f6fcf4',
    100: '#eef9e8',
    200: '#dcf3d1',
    300: '#bae7a3',
    400: '#97dc76',
    500: '#52c41a',
    600: '#42aa10',
  },

  warning: {
    100: '#fffbeb',
    200: '#fef4cd',
    300: '#fde68a',
    400: '#fcd34d',
    500: '#fbbf24',
    600: '#f59e0b',
    700: '#db7500',
  },

  danger: {
    100: '#fde7e7',
    200: '#fee2e2',
    300: '#fecaca',
    400: '#fca5a5',
    500: '#f87171',
    600: '#ef4444',
    700: '#dc2626',
  },

  informative: {
    100: '#eff6ff',
    200: '#dbeafe',
    300: '#bfdbfe',
    400: '#93c5fd',
    500: '#60a5fa',
    600: '#3b82f6',
    700: '#2563eb',
  },

  purple: {
    50: '#e1dcef',
    100: '#f8e5ff',
    200: '#efc2ff',
    300: '#e28fff',
    400: '#d55cff',
    500: '#c729ff',
    600: '#b400f3',
    700: '#92a1c3',
    800: '#792ddc',
  },

  orange: {
    50: '#ffefe3',
    100: '#ffe1cc',
    200: '#ff8b6b',
    500: '#c4611a',
    600: '#f78928',
  },

  mint: {
    50: '#d9e6e8',
    100: '#ccffe',
    200: '#66d7d1',
    500: '#1ac48b',
    600: '#128760',
  },

  violet: {
    400: '#987aea',
  },

  yellow: {
    400: '#f8b60c',
  },

  green: {
    600: '#119c26',
    700: '#5aa336',
  },

  blue: {
    100: '#c1e1f5',
    200: '#bcf0fe',
    300: '#72bdfb',
    400: '#009ce0',
    500: '#3c8aff',
    600: '#30a5ff',
  },
  coralPink: {
    500: '#FC8192',
  },

  custom: {
    mint200: '#66d7d1',
    orange200: '#ff8b6b',
    cream200: '#fcdaa2',
    backgroundTooltip: 'rgba(0, 0, 0, 0.8)',
    popup: '#bfbfbf',
    backgroundDanger: '#fde7e7',
    colorDanger: '#dc2626',
    colorSuccess: '#42aa10',
    orange50: '#ffefe3',
    neutral80: '#ebeaea',
    green2: '#a0ca97',
    pink1: '#f98e8d',
    backgroundExamiantionForm: '#edf6f6',
  },

  text: {
    default: '#33343e',
    subdued: '#8b8c99',
    disabled: '#b6bcc1',
  },

  background: {
    disable: '#f9f9f9',
    hover: '#f9f9fc',
    titleTable: '#ececf6',
    pageContainer: '#f0f0f3',
    form: '#F8F8FC',
  },

  hover: {
    default: '#f9f9f9',
  },

  divider: {
    default: '#e6e6f3',
    border: '#ddddee',
    calendarBorder: '#e5e5e5',
  },

  // Additional colors from the original
  dark: {
    2: '#87898e',
  },

  light: {
    3: '#dfdfe6',
  },
  lavender: {
    400: '#9879DA',
  },
  cream: {
    50: '#FEF6E6',
  },
} as const

// Type definitions for better TypeScript support
export type ColorPalette = typeof colors
export type ColorCategory = keyof ColorPalette
export type ColorShade<T extends ColorCategory> = keyof ColorPalette[T]

// Utility functions for easier color access
export const getColor = <T extends ColorCategory>(category: T, shade: ColorShade<T>): string => {
  return colors[category][shade] as string
}

// Utility function to get primary color with fallback
export const getPrimaryColor = (shade: keyof typeof colors.primary = 500): string => {
  return colors.primary[shade]
}

// Utility function to get neutral color with fallback
export const getNeutralColor = (shade: keyof typeof colors.neutral = 500): string => {
  return colors.neutral[shade]
}

// Utility function for text colors
export const getTextColor = (type: keyof typeof colors.text = 'default'): string => {
  return colors.text[type]
}

// Utility function for background colors
export const getBackgroundColor = (
  type: keyof typeof colors.background = 'pageContainer',
): string => {
  return colors.background[type]
}

// Export individual color palettes for convenience
export const {
  primary,
  neutral,
  success,
  warning,
  danger,
  informative,
  purple,
  orange,
  mint,
  violet,
  yellow,
  green,
  blue,
  custom,
  text,
  background,
  hover,
  divider,
  dark,
  light,
  coralPink,
  lavender,
  cream,
} = colors

// Default export
export default colors
