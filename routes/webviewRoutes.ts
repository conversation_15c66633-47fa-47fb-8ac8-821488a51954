import { LocaleEnum } from '@/enums/locale.enum'

export enum WebViewAppRoutesEnum {
  USER = 'USER',
  MEDICAL_HANDBOOK = 'MEDICAL_HANDBOOK',
  SUBSCRIPTION_MODIFY = 'SUBSCRIPTION_MODIFY',
  HOME = 'HOME',
  SEARCH_SUMMARY = 'SEARCH_SUMMARY',
  LOGIN = 'LOGIN',
  SIGNUP = 'SIGNUP',
  VERIFY = 'VERIFY',
  RESET_PASSWORD = 'RESET_PASSWORD',
  RESEND_VERIFY_EMAIL = 'RESEND_VERIFY_EMAIL',
  TUTORIAL = 'TUTORIAL',
  EXAMINATION = 'EXAMINATION',
  POSTS = 'POSTS',
  PRODUCTS = 'PRODUCTS',
  PRODUCTS_V2 = 'PRODUCTS_V2',
  PRODUCTS_MEDICINES = 'PRODUCTS_MEDICINES',
  PRODUCTS_SUPPLEMENTS = 'PRODUCTS_SUPPLEMENTS',
  MEDICAL_DICTIONARY = 'MEDICAL_DICTIONARY',
  MEDICAL_DICTIONARY_SEARCH = 'MEDICAL_DICTIONARY_SEARCH',
  MEDICAL_HANDBOOK_FACULTIES = 'MEDICAL_HANDBOOK_FACULTIES',
  MEDICAL_HANDBOOK_BODY_PARTS = 'MEDICAL_HANDBOOK_BODY_PARTS',
  CHAT_BOT = 'CHAT_BOT',
  CHAT_BOT_SEARCH_MEDICINE = 'CHAT_BOT_SEARCH_MEDICINE',
  FAVORITE_MEDICINES = 'FAVORITE_MEDICINES',
  TRANSACTION_HISTORY = 'TRANSACTION_HISTORY',
  MANAGE_SUBSCRIPTIONS = 'MANAGE_SUBSCRIPTIONS',
  NATIVE = 'NATIVE',
  AESTHETICS = 'AESTHETICS',
}

export const WEBVIEW_APP_ROUTES: Record<
  string,
  {
    path: string
    authRequired: boolean | null
    children?: Record<string, any>
    name?: Record<string, string>
  }
> = {
  USER: {
    path: '/user',
    authRequired: true,
    name: {
      [LocaleEnum.VI]: 'Tài khoản',
      [LocaleEnum.JA]: 'ユーザー',
    },
    children: {
      FAVORITE_MEDICINES: {
        path: '/user/favorite-medicines',
        authRequired: true,
        name: {
          [LocaleEnum.VI]: 'Thuốc yêu thích',
          [LocaleEnum.JA]: 'お気に入りの薬',
        },
        children: {
          FAVORITE_MEDICINES_SEARCH: {
            path: '/user/favorite-medicines/search',
            authRequired: true,
            name: {
              [LocaleEnum.VI]: 'Tìm kiếm thuốc yêu thích',
              [LocaleEnum.JA]: 'お気に入りの薬の検索',
            },
          },
        },
      },
      TRANSACTION_HISTORY: {
        path: '/user/transaction-history',
        authRequired: true,
        name: {
          [LocaleEnum.VI]: 'Lịch sử giao dịch',
          [LocaleEnum.JA]: '取引履歴',
        },
      },
      MANAGE_SUBSCRIPTIONS: {
        path: '/user/manage-subscription',
        authRequired: true,
        name: {
          [LocaleEnum.VI]: 'Quản lý đăng ký',
          [LocaleEnum.JA]: 'サブスクリプションの管理',
        },
      },
    },
  },
  MEDICAL_HANDBOOK: {
    path: '/medical-handbook',
    authRequired: true,
    name: {
      [LocaleEnum.VI]: 'Sách Y Tế',
      [LocaleEnum.JA]: '医療ハンドブック',
    },
    children: {
      FACULTIES: {
        path: '/medical-handbook/faculties',
        authRequired: true,
        name: {
          [LocaleEnum.VI]: 'Khoa y tế',
          [LocaleEnum.JA]: '病院',
        },
        children: {
          FACULTIES_SEARCH: {
            path: '/medical-handbook/faculties/search-faculties',
            authRequired: true,
            name: {
              [LocaleEnum.VI]: 'Tìm kiếm khoa',
              [LocaleEnum.JA]: '病院の検索',
            },
          },
        },
      },
      BODY_PARTS: {
        path: '/medical-handbook/body-parts',
        authRequired: true,
        name: {
          [LocaleEnum.VI]: 'Bộ phận cơ thể',
          [LocaleEnum.JA]: '身体部位',
        },
      },
    },
  },
  SUBSCRIPTION_MODIFY: {
    path: '/subscription-modify',
    authRequired: true,
    name: {
      [LocaleEnum.VI]: 'Cập nhật gói Đăng ký',
      [LocaleEnum.JA]: 'サブスクリプションの変更',
    },
  },
  HOME: {
    path: '/',
    authRequired: null,
    name: {
      [LocaleEnum.VI]: 'Trang chủ',
      [LocaleEnum.JA]: 'ホーム',
    },
  },
  SEARCH_SUMMARY: {
    path: '/search-summary',
    authRequired: null,
    name: {
      [LocaleEnum.VI]: 'Tìm kiếm tổng hợp',
      [LocaleEnum.JA]: '検索の要約',
    },
  },
  LOGIN: {
    path: '/auth/login',
    authRequired: false,
    name: {
      [LocaleEnum.VI]: 'Đăng nhập',
      [LocaleEnum.JA]: 'ログイン',
    },
  },
  SIGNUP: {
    path: '/auth/signup',
    authRequired: false,
    name: {
      [LocaleEnum.VI]: 'Đăng ký',
      [LocaleEnum.JA]: 'サインアップ',
    },
  },
  VERIFY: {
    path: '/verify/',
    authRequired: false,
    name: {
      [LocaleEnum.VI]: 'Xác thực',
      [LocaleEnum.JA]: '確認',
    },
  },
  RESET_PASSWORD: {
    path: '/reset-password',
    authRequired: false,
    name: {
      [LocaleEnum.VI]: 'Đặt lại mật khẩu',
      [LocaleEnum.JA]: 'パスワードのリセット',
    },
  },
  RESEND_VERIFY_EMAIL: {
    path: '/resend-verify-email',
    authRequired: false,
    name: {
      [LocaleEnum.VI]: 'Gửi lại email xác thực',
      [LocaleEnum.JA]: '確認メールの再送信',
    },
  },
  TUTORIAL: {
    path: '/tutorial',
    authRequired: null,
    name: {
      [LocaleEnum.VI]: 'Hướng dẫn',
      [LocaleEnum.JA]: 'チュートリアル',
    },
  },
  EXAMINATION: {
    path: '/examination',
    authRequired: true,
    name: {
      [LocaleEnum.VI]: 'Phiếu khám',
      [LocaleEnum.JA]: '検査フォーム',
    },
    children: {
      EXAMINATION_FACULTIES_SEARCH: {
        path: '/examination/search-examination',
        authRequired: true,
        name: {
          [LocaleEnum.VI]: 'Tìm kiếm phiếu khám theo khoa',
          [LocaleEnum.JA]: '病院の検索',
        },
      },
    },
  },
  POSTS: {
    path: '/posts',
    authRequired: null,
    name: {
      [LocaleEnum.VI]: 'Tin tức',
      [LocaleEnum.JA]: '記事',
    },
    children: {
      POSTS_SEARCH: {
        path: '/posts/search',
        authRequired: null,
        name: {
          [LocaleEnum.VI]: 'Tìm kiếm tin tức',
          [LocaleEnum.JA]: '記事の検索',
        },
      },
    },
  },
  PRODUCTS: {
    path: '/products',
    authRequired: null,
    name: {
      [LocaleEnum.VI]: 'Sản phẩm',
      [LocaleEnum.JA]: '商品',
    },
    children: {
      BODY_PARTS: {
        path: '/products/medicine-body-parts',
        authRequired: null,
        name: {
          [LocaleEnum.VI]: 'Bộ phận cơ thể',
          [LocaleEnum.JA]: '身体部位',
        },
      },
      SUPPLEMENT_CATEGORIES: {
        path: '/products/supplement-categories',
        authRequired: null,
      },
      MEDICINES: {
        path: '/products/medicines',
        authRequired: null,
        name: {
          [LocaleEnum.VI]: 'Thuốc',
          [LocaleEnum.JA]: '薬',
        },
      },
      SUPPLEMENTS: {
        path: '/products/supplements',
        authRequired: null,
        name: {
          [LocaleEnum.VI]: 'Thực phẩm chức năng',
          [LocaleEnum.JA]: '健康食品',
        },
      },
      LIST: {
        path: '/products/list',
        authRequired: null,
        name: {
          [LocaleEnum.VI]: 'Danh sách sản phẩm',
          [LocaleEnum.JA]: '商品リスト',
        },
      },
    },
  },
  PRODUCTS_V2: {
    path: '/products-v2',
    authRequired: null,
    name: {
      [LocaleEnum.VI]: 'Sản phẩm',
      [LocaleEnum.JA]: '商品',
    },
  },
  MEDICAL_DICTIONARY: {
    path: '/medical-dictionary',
    authRequired: true,
    name: {
      [LocaleEnum.VI]: 'Từ điển y khoa',
      [LocaleEnum.JA]: '医療用語辞典',
    },
    children: {
      MEDICAL_DICTIONARY_SEARCH: {
        path: '/medical-dictionary/search-dictionary',
        authRequired: true,
        name: {
          [LocaleEnum.VI]: 'Tìm kiếm từ điển',
          [LocaleEnum.JA]: '医療用語辞典の検索',
        },
      },
    },
  },
  CHAT_BOT: {
    path: '/chat-bot',
    authRequired: null,
    name: {
      [LocaleEnum.VI]: 'Chat Bot',
      [LocaleEnum.JA]: 'チャットボット',
    },
    children: {
      CHAT_BOT_SEARCH_MEDICINE: {
        path: '/chat-bot/search-medicine',
        authRequired: null,
        name: {
          [LocaleEnum.VI]: 'Tìm kiếm thuốc bằng hình ảnh',
          [LocaleEnum.JA]: '画像から薬を検索',
        },
      },
    },
  },
  NATIVE: {
    path: '/native',
    authRequired: null,
    name: {
      [LocaleEnum.VI]: 'Native',
      [LocaleEnum.JA]: 'Native',
    },
  },
  AESTHETICS: {
    path: '/aesthetics',
    authRequired: null,
    name: {
      [LocaleEnum.VI]: 'Thẩm mỹ',
      [LocaleEnum.JA]: '美容',
    },
  },
}

export const PROTECTED_WEBVIEW_APP_ROUTES: Record<string, string> = Object.fromEntries(
  Object.entries(WEBVIEW_APP_ROUTES)
    .filter(([_, route]) => route.authRequired === true)
    .map(([key, route]) => [key, route.path]),
)

export const GUEST_ONLY_WEBVIEW_APP_ROUTES: Record<string, string> = Object.fromEntries(
  Object.entries(WEBVIEW_APP_ROUTES)
    .filter(([_, route]) => route.authRequired === false)
    .map(([key, route]) => [key, route.path]),
)
