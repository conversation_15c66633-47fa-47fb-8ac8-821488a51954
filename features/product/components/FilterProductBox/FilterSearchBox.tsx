import { useSheetActions } from '@/contexts/SheetContext/SheetContext'
import { useAppLanguage } from '@/hooks/common/useAppLanguage'
import { useCallback, useEffect, useMemo, useRef } from 'react'
import { useTranslation } from 'react-i18next'
import { View } from 'react-native'
import { useShallow } from 'zustand/react/shallow'

import {
  BaseFilterSheetBox,
  type BaseFilterSheetBoxRef,
} from '@/components/Filter/BaseFilterSheetBox/BaseFilterSheetBox'
import { BottomSheetScrollView } from '@gorhom/bottom-sheet'
import { PRODUCT_V2_TYPE_OPTIONS } from '../../constants'
import { ProductV2TypeEnum } from '../../enums'
import { useGetProductAgeGroups } from '../../hooks/query/product/useGetProductAgeGroups'
import { useGetProductCategories } from '../../hooks/query/product/useGetProductCategories'
import { useGetProductMedicineTypes } from '../../hooks/query/product/useGetProductMedicineTypes'
import {
  SearchProductFilterItem,
  SearchProductFilterType,
  useProductStore,
} from '../../stores/ProductStore'
import { ProductAgeGroup, ProductMedicineType } from '../../types'

import { BaseFilterListSection } from '@/components/Filter/BaseFilterListSection/BaseFilterListSection'
import { BaseSearchableFilterSection } from '@/components/Filter/BaseSearchableFilterSection/BaseSearchableFilterSection'
import { useSearchableFilterSection } from '@/components/Filter/BaseSearchableFilterSection/useSearchableFilterSection'
import { SearchableFilterListSection } from '@/components/Filter/SearchableFilterListSection/SearchableFilterListSection'
import { MedicineTypeNotes } from './MedicineTypeNotes'
import { SearchFilterFooter } from './SearchFilterFooter'

interface FilterSearchBoxProps {
  closeSheet?: () => void
  sharedFooterRef: React.RefObject<View | null>
  sharedSheetBoxRef?: React.RefObject<BaseFilterSheetBoxRef | null>
}

export const FilterSearchBox = ({
  closeSheet,
  sharedFooterRef,
  sharedSheetBoxRef,
}: FilterSearchBoxProps) => {
  const { primaryLanguage } = useAppLanguage()
  const { t } = useTranslation()
  const localSheetBoxRef = useRef<BaseFilterSheetBoxRef>(null)
  const sheetBoxRef = sharedSheetBoxRef || localSheetBoxRef

  const { productAgeGroups, isGetProductAgeGroupsLoading } = useGetProductAgeGroups({
    params: { locale: primaryLanguage, pagination: false },
    useQueryOptions: {
      staleTime: 5 * 60 * 1000,
    },
  })
  const { productMedicineTypes, isGetProductMedicineTypesLoading } = useGetProductMedicineTypes({
    params: { locale: primaryLanguage, pagination: false },
    useQueryOptions: {
      staleTime: 5 * 60 * 1000,
    },
  })

  const { productCategories, isGetProductCategoriesLoading } = useGetProductCategories({
    params: {
      locale: primaryLanguage,
      pagination: false,
      select: {
        id: true,
        title: true,
        type: true,
      },
    },
    useQueryOptions: {
      staleTime: 5 * 60 * 1000,
    },
  })

  const {
    toggleTempSearchFilter,
    initTempSearchFilters,
    tempSearchFilters,
    clearAllTempSearchFiltersMedicineType,
    filterCategoriesByProductType,
  } = useProductStore(
    useShallow((state) => ({
      toggleTempSearchFilter: state.toggleTempSearchFilter,
      initTempSearchFilters: state.initTempSearchFilters,
      tempSearchFilters: state.tempSearchFilters,
      clearAllTempSearchFiltersMedicineType: state.clearAllTempSearchFiltersMedicineType,
      filterCategoriesByProductType: state.filterCategoriesByProductType,
    })),
  )

  // Initialize temp search filters when component mounts
  useEffect(() => {
    initTempSearchFilters()
  }, [initTempSearchFilters])

  const activeAgeGroupIds = useProductStore(
    useShallow(
      (state) =>
        state.tempSearchFilters[SearchProductFilterType.AGE_GROUP]?.map((filter) => filter.id) ||
        [],
    ),
  )

  const activeMedicineTypeIds = useProductStore(
    useShallow(
      (state) =>
        state.tempSearchFilters[SearchProductFilterType.MEDICINE_TYPE]?.map(
          (filter) => filter.id,
        ) || [],
    ),
  )

  const activeProductTypeIds = useProductStore(
    useShallow(
      (state) =>
        state.tempSearchFilters[SearchProductFilterType.PRODUCT_TYPE]?.map((filter) => filter.id) ||
        [],
    ),
  )

  const activeCategoryIds = useProductStore(
    useShallow(
      (state) =>
        state.tempSearchFilters[SearchProductFilterType.CATEGORY]?.map((filter) => filter.id) || [],
    ),
  )

  const handleToggleAgeGroup = useCallback(
    (ageGroup: ProductAgeGroup) => {
      toggleTempSearchFilter(SearchProductFilterType.AGE_GROUP, {
        id: ageGroup.id,
        label: ageGroup.title,
        type: SearchProductFilterType.AGE_GROUP,
      })
    },
    [toggleTempSearchFilter],
  )

  const handleToggleMedicineType = useCallback(
    (medicineType: ProductMedicineType) => {
      toggleTempSearchFilter(SearchProductFilterType.MEDICINE_TYPE, {
        id: medicineType.id,
        label: medicineType.title,
        type: SearchProductFilterType.MEDICINE_TYPE,
      })
    },
    [toggleTempSearchFilter],
  )

  const handleToggleProductType = useCallback(
    (filterItem: SearchProductFilterItem) => {
      toggleTempSearchFilter(SearchProductFilterType.PRODUCT_TYPE, filterItem)

      // After toggling product type, filter categories to only keep those that match selected product types
      if (productCategories?.docs) {
        const categoryData = productCategories.docs.map((cat) => ({
          id: cat.id,
          type: cat.type,
        }))
        // Use setTimeout to ensure the toggle action completes first
        setTimeout(() => {
          filterCategoriesByProductType(categoryData)
        }, 0)
      }
    },
    [toggleTempSearchFilter, filterCategoriesByProductType, productCategories?.docs],
  )
  const handleToggleCategory = useCallback(
    (filterItem: SearchProductFilterItem) => {
      toggleTempSearchFilter(SearchProductFilterType.CATEGORY, filterItem)
    },
    [toggleTempSearchFilter],
  )

  const ageGroupData = useMemo(() => productAgeGroups?.docs || [], [productAgeGroups?.docs])

  const medicineTypeData = useMemo(
    () => productMedicineTypes?.docs || [],
    [productMedicineTypes?.docs],
  )
  const productTypeData = useMemo(
    () =>
      Object.values(PRODUCT_V2_TYPE_OPTIONS).map((item) => ({
        id: item.value,
        label: t(item.translationKey || ''),
        type: SearchProductFilterType.PRODUCT_TYPE,
      })),
    [t],
  )

  // Get all categories filtered by product type
  const allFilteredCategories = useMemo(() => {
    if (!productCategories?.docs) return []

    let filtered = productCategories.docs

    // Filter by active product type if any is selected
    if (activeProductTypeIds.length > 0) {
      filtered = filtered.filter((category) => activeProductTypeIds.includes(category.type))
    }

    // Transform to SearchProductFilterItem format
    return filtered.map((item) => ({
      id: item.id,
      label: item.title,
      type: SearchProductFilterType.CATEGORY,
    }))
  }, [productCategories?.docs, activeProductTypeIds])

  // Use reusable hook for categories
  const categoriesFilter = useSearchableFilterSection({
    data: allFilteredCategories,
    limit: 8,
  })

  // Custom filter function for categories that respects product type filtering
  const categoryCustomFilterFn = useCallback(
    (item: SearchProductFilterItem, searchValue: string) => {
      // The data is already filtered by product type, just search in label
      return item.label.toLowerCase().includes(searchValue)
    },
    [],
  )

  const shouldShowMedicineTypeFilter = useMemo(() => {
    // Show medicine type filter if product type includes medicine OR if no product type is selected
    const isFilterProductTypeContainMedicineType = tempSearchFilters?.[
      SearchProductFilterType.PRODUCT_TYPE
    ]?.some((filter) => filter.id === ProductV2TypeEnum.MEDICINE)
    const isFilterProductTypeEmpty =
      !tempSearchFilters?.[SearchProductFilterType.PRODUCT_TYPE] ||
      tempSearchFilters?.[SearchProductFilterType.PRODUCT_TYPE]?.length === 0

    return isFilterProductTypeContainMedicineType || isFilterProductTypeEmpty
  }, [tempSearchFilters])

  // Track active filters count to trigger footer height re-measurement
  const activeFiltersCount =
    activeAgeGroupIds.length +
    activeMedicineTypeIds.length +
    activeProductTypeIds.length +
    activeCategoryIds.length

  // Trigger manual footer measurement when filters change (no re-render!)
  useEffect(() => {
    // Small delay to allow footer DOM updates to complete
    const timer = setTimeout(() => {
      sheetBoxRef.current?.measureFooter()
    }, 100)

    return () => clearTimeout(timer)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeFiltersCount])

  useEffect(() => {
    // Clear medicine type filters when they shouldn't be shown
    if (!shouldShowMedicineTypeFilter) {
      clearAllTempSearchFiltersMedicineType()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [shouldShowMedicineTypeFilter])

  // Configuration for searchable filter sections
  const searchableFilters = useMemo(
    () => [
      {
        key: 'categories',
        filter: categoriesFilter,
        title: t('MES-128'),
        data: categoriesFilter.fullData,
        onSelectFilter: (item: SearchProductFilterItem) => {
          handleToggleCategory(item)
        },
        activeFilterIds: activeCategoryIds,
        isLoading: isGetProductCategoriesLoading,
        customFilterFn: categoryCustomFilterFn,
      },
    ],
    [
      categoriesFilter,
      t,
      handleToggleCategory,
      activeCategoryIds,
      isGetProductCategoriesLoading,
      categoryCustomFilterFn,
    ],
  )

  // Find which searchable filter is currently active
  const activeSearchableFilter = useMemo(
    () => searchableFilters.find((item) => item.filter.showAll),
    [searchableFilters],
  )

  // Helper to render the active searchable filter section
  const renderSearchableFilter = () => {
    if (!activeSearchableFilter) return null

    const { filter, title, data, onSelectFilter, activeFilterIds, isLoading, customFilterFn } =
      activeSearchableFilter

    return (
      <BaseSearchableFilterSection
        title={title}
        footerHeight={sheetBoxRef.current?.getFooterHeight() || 80}
        data={data}
        onBack={() => filter.setShowAll(false)}
        onSelectFilter={onSelectFilter}
        activeFilterIds={activeFilterIds}
        idKey="id"
        labelKey="label"
        isLoading={isLoading}
        customFilterFn={customFilterFn}
      />
    )
  }

  return (
    <BaseFilterSheetBox
      ref={sheetBoxRef}
      title={t('MES-481')}
      onClose={closeSheet}
      footerRef={sharedFooterRef}
      enableFooterHeightMeasurement={true}
      footerHeightPadding={40}
      useBottomSheetScrollView={true}
    >
      {!activeSearchableFilter ? (
        <BottomSheetScrollView className="relative" showsVerticalScrollIndicator={false}>
          <View className="flex flex-col gap-y-3 px-4">
            <BaseFilterListSection
              title={t('MES-711')}
              data={productTypeData}
              onSelectFilter={handleToggleProductType}
              activeFilters={activeProductTypeIds}
              idKey="id"
              labelKey="label"
            />
            <SearchableFilterListSection
              title={t('MES-128')}
              data={categoriesFilter.limitedData}
              onSelectFilter={handleToggleCategory}
              activeFilters={activeCategoryIds}
              idKey="id"
              labelKey="label"
              isLoading={isGetProductCategoriesLoading}
              shouldShowViewAllButton={categoriesFilter.shouldShowViewAllButton}
              onViewAll={() => categoriesFilter.setShowAll(true)}
            />
            <BaseFilterListSection
              title={t('MES-707')}
              data={ageGroupData}
              onSelectFilter={handleToggleAgeGroup}
              activeFilters={activeAgeGroupIds}
              idKey="id"
              labelKey="title"
              isLoading={isGetProductAgeGroupsLoading}
            />
            {shouldShowMedicineTypeFilter && (
              <View className="flex flex-col gap-y-2">
                <BaseFilterListSection
                  title={t('MES-708')}
                  data={medicineTypeData}
                  onSelectFilter={handleToggleMedicineType}
                  activeFilters={activeMedicineTypeIds}
                  idKey="id"
                  labelKey="title"
                  isLoading={isGetProductMedicineTypesLoading}
                >
                  <MedicineTypeNotes medicineType={medicineTypeData} />
                </BaseFilterListSection>
              </View>
            )}
          </View>
        </BottomSheetScrollView>
      ) : (
        renderSearchableFilter()
      )}
    </BaseFilterSheetBox>
  )
}

export const useOpenFilterSearchBox = () => {
  const { openCustomSheet, closeSheet } = useSheetActions()

  const sharedFooterRef = useRef<View>(null)
  const sharedSheetBoxRef = useRef<BaseFilterSheetBoxRef>(null)

  const handleOpenFilterSearchBox = useCallback(() => {
    openCustomSheet({
      children: ({ close }) => (
        <FilterSearchBox
          closeSheet={close}
          sharedFooterRef={sharedFooterRef}
          sharedSheetBoxRef={sharedSheetBoxRef}
        />
      ),
      baseProps: {
        snapPoints: ['85%', '100%'],
        enableHandlePanningGesture: true,
        enableDynamicSizing: false,
        enableOverDrag: false,
      },
      options: {
        footerComponent: (props) => {
          return <SearchFilterFooter ref={sharedFooterRef} closeSheet={closeSheet} {...props} />
        },
      },
    })
  }, [openCustomSheet, closeSheet])

  return { handleOpenFilterSearchBox }
}
