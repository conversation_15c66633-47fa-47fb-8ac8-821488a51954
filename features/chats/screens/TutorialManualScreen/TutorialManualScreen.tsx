import { router } from 'expo-router'
import {
  Dimensions,
  FlatList,
  NativeScrollEvent,
  NativeSyntheticEvent,
  Pressable,
  StyleSheet,
  Text,
  View,
} from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'

import CloseIcon from '@/assets/icons/close-icon.svg'
import { useEffect, useRef, useState } from 'react'

import HintTakePictureMedicine from '@/assets/take-picture/hint-take-picture-medicine.svg'
import ConditionTakePictureMedicine from '@/assets/take-picture/min-condition-take-picture.svg'
import { useTranslation } from 'react-i18next'

const { width } = Dimensions.get('window')

const slides = [
  {
    id: '1',
    image: HintTakePictureMedicine,
    title: 'MES-981',
    description: 'MES-982',
  },
  {
    id: '2',
    image: ConditionTakePictureMedicine,
    title: 'MES-981',
    description: 'MES-983',
  },
]

type TutorialManualScreenProps = {
  onOpenChatBot: () => void
}
export const TutorialManualScreen: React.FC<TutorialManualScreenProps> = ({ onOpenChatBot }) => {
  const { t } = useTranslation()

  const flatListRef = useRef<FlatList>(null)
  const [index, setIndex] = useState(0)

  const onClose = () => {
    if (router.canGoBack()) {
      router.back()
    } else {
      router.replace('/')
    }
  }

  // Auto-scroll mỗi 3 giây
  useEffect(() => {
    const interval = setInterval(() => {
      const nextIndex = (index + 1) % slides.length
      flatListRef.current?.scrollToIndex({ index: nextIndex, animated: true })
      setIndex(nextIndex)
    }, 3000)

    return () => clearInterval(interval)
  }, [index])

  const onScroll = (e: NativeSyntheticEvent<NativeScrollEvent>) => {
    const slideIndex = Math.round(e.nativeEvent.contentOffset.x / width)
    setIndex(slideIndex)
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.boxLogo}>
        <Pressable
          onPress={onClose}
          style={({ pressed }) => [styles.iconButton, { opacity: pressed ? 0.5 : 1 }]}
        >
          <CloseIcon width={24} height={24} />
        </Pressable>
      </View>

      <View>
        <FlatList
          data={slides}
          ref={flatListRef}
          horizontal
          pagingEnabled
          showsHorizontalScrollIndicator={false}
          onScroll={onScroll}
          scrollEventThrottle={16}
          renderItem={({ item }) => {
            const ImageComponent = item.image
            return (
              <View style={styles.slide}>
                <ImageComponent />

                <View style={styles.containerDescription}>
                  <Text className="typo-body-3" style={styles.title}>
                    {t(item.title)}
                  </Text>
                  <Text className="typo-body-7" style={styles.desc}>
                    {t(item.description)}
                  </Text>
                </View>
              </View>
            )
          }}
        />
      </View>

      <View style={styles.indicatorWrapper}>
        {slides.map((_, i) => (
          <View key={i} style={[styles.dot, { opacity: index === i ? 1 : 0.3 }]} />
        ))}
      </View>

      <View style={{ paddingHorizontal: 24 }}>
        <Pressable style={styles.boxButton} onPress={onOpenChatBot}>
          <Text className="typo-button-3" style={{ color: '#fff' }}>
            {t('MES-984')}
          </Text>
        </Pressable>
      </View>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'white',
    width: '100%',
  },

  boxLogo: {
    display: 'flex',
    justifyContent: 'flex-end',
    alignItems: 'flex-end',
    width: '100%',
    padding: 12,
  },
  iconButton: {
    padding: 8,
  },
  slide: {
    width,
    alignItems: 'center',
    paddingTop: 40,
  },
  image: {
    width: 220,
    height: 220,
    marginBottom: 20,
  },
  containerDescription: {
    paddingVertical: 24,
    marginTop: 40,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1A73E8',
    marginTop: 0,
    marginBottom: 8,
    textAlign: 'center',
  },
  desc: {
    fontSize: 14,
    color: '#555',
    textAlign: 'center',
    paddingHorizontal: 20,
  },
  indicatorWrapper: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 12,
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#1A73E8',
    marginHorizontal: 4,
  },

  boxButton: {
    width: '100%',
    padding: 12,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    color: '#fff',
    backgroundColor: '#1A73E8',
    borderRadius: 8,
    marginTop: 40,
    fontWeight: 600,
    fontSize: 14,
    lineHeight: 16,
  },
})
