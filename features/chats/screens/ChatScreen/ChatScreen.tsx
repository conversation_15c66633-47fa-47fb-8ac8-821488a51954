import CloseIcon from '@/assets/icons/close-icon.svg'
import { Text } from '@/components/ui/Text/Text'
import { Image } from 'expo-image'
import { useTranslation } from 'react-i18next'
import { TouchableOpacity, View } from 'react-native'

import HeadphoneIcon from '@/assets/icons/headphone.svg'
import TranslatorIcon from '@/assets/icons/translator-icon.svg'
import { APP_ROUTES } from '@/routes/appRoutes'
import { Link, LinkProps } from 'expo-router'

export const ChatScreen = () => {
  const { t } = useTranslation()
  return (
    <View
      className="flex-1"
      style={{
        paddingHorizontal: 16,
      }}
    >
      <View
        style={{
          paddingVertical: 16,
        }}
        className="flex-row items-center justify-between gap-3"
      >
        <Image source={require('@/assets/images/logo.png')} className="h-8 w-8 rounded-full" />

        <TouchableOpacity>
          <CloseIcon width={24} height={24} />
        </TouchableOpacity>
      </View>

      <View className="flex-1 gap-6" style={{ paddingVertical: 16 }}>
        <View className="gap-3">
          <Text size="heading7">{t('MES-1049')}</Text>

          <Text size="body7" variant="subdued">
            {t('MES-1050')}
          </Text>
        </View>

        <View className="flex-1 gap-3">
          <Link href={APP_ROUTES.CHAT.children?.CHAT_AGENT.path as LinkProps['href']} asChild>
            <TouchableOpacity className="gap-3 rounded-xl bg-[#F8F8FC] p-4">
              <HeadphoneIcon width={36} height={36} />
              <View className="gap-1">
                <Text size="body3">HICO Chat</Text>

                <Text size="body7" variant="subdued" className="truncate">
                  {t('MES-1051')}
                </Text>
              </View>
            </TouchableOpacity>
          </Link>

          <TouchableOpacity className="gap-3 rounded-xl bg-[#F8F8FC] p-4">
            <TranslatorIcon width={36} height={36} />
            <View className="gap-1">
              <Text size="body3">{t('MES-1052')}</Text>

              <Text size="body7" variant="subdued" className="truncate">
                {t('MES-1053')}
              </Text>
            </View>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  )
}
