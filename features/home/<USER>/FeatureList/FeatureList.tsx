import AestheticsIcon from '@/assets/icons/aesthetics-feature-icon.svg'
import CameraV2Icon from '@/assets/icons/camera-v2-icon.svg'
import ExaminationIcon from '@/assets/icons/examination-document.svg'
import FacultyPositionIconV2 from '@/assets/icons/faculty-position-pin-v2.svg'
import HeartBeatIcon from '@/assets/icons/heartbeat-icon.svg'
import MedicalDocumentsV2Icon from '@/assets/icons/medical-document-v2-icon.svg'
import { Text } from '@/components/ui/Text/Text'
import { useAuthentication } from '@/hooks/auth/useAuthentication'
import { APP_ROUTES } from '@/routes/appRoutes'
import * as Haptics from 'expo-haptics'
import { LinearGradient } from 'expo-linear-gradient'
import { Link, LinkProps } from 'expo-router'
import React, { useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { TouchableOpacity, View } from 'react-native'
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withRepeat,
  withSequence,
  withTiming,
} from 'react-native-reanimated'
import Svg, { Path } from 'react-native-svg'

const FEATURE_LIST = {
  MEDICAL_HANDBOOK: {
    title: 'MES-753',
    icon: FacultyPositionIconV2,
    url: APP_ROUTES?.MEDICAL_HANDBOOK?.path,
    isNew: false,
  },
  MEDICAL_DOCUMENTS: {
    title: 'MES-793',
    icon: MedicalDocumentsV2Icon,
    url: APP_ROUTES?.MEDICAL_DOCUMENTS?.path,
    isNew: false,
  },
  CHAT_BOT_SEARCH_MEDICINE: {
    title: 'MES-754',
    icon: CameraV2Icon,
    url: APP_ROUTES?.CHAT_BOT?.children?.CHAT_BOT_SEARCH_MEDICINE?.path,
    isNew: false,
  },
  EXAMINATION: {
    title: 'MES-755',
    icon: ExaminationIcon,
    url: APP_ROUTES?.EXAMINATION?.path,
    isNew: false,
  },

  AESTHETICS: {
    title: 'MES-957',
    icon: AestheticsIcon,
    url: APP_ROUTES?.AESTHETICS?.path,
    isNew: true,
  },
} as const

interface FeatureCardProps {
  feature: (typeof FEATURE_LIST)[keyof typeof FEATURE_LIST]
  href: LinkProps['href']
  colors: [string, string]
  gradientStart: { x: number; y: number }
  gradientEnd: { x: number; y: number }
}

const NewBadge = () => {
  const { t } = useTranslation()
  const scale = useSharedValue(1)
  const opacity = useSharedValue(0.8)

  useEffect(() => {
    scale.value = withRepeat(
      withSequence(withTiming(1.08, { duration: 800 }), withTiming(1, { duration: 800 })),
      -1,
      true,
    )

    opacity.value = withRepeat(
      withSequence(withTiming(1, { duration: 800 }), withTiming(0.8, { duration: 800 })),
      -1,
      true,
    )
  }, [scale, opacity])

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
    opacity: opacity.value,
  }))
  return (
    <Animated.View
      style={animatedStyle}
      className="absolute right-[10px] top-[10px] flex flex-row items-center gap-x-2 rounded-md bg-white px-3 py-1"
    >
      <View className="shrink-0">
        <Svg width={18} height={18} viewBox="0 0 18 18" fill="none">
          <Path
            d="M6.21273 12.0113C6.40153 12.1446 6.6271 12.2159 6.85821 12.2153C7.08932 12.2147 7.31454 12.1423 7.50266 12.008C7.69336 11.8688 7.838 11.6759 7.91728 11.4531L8.39618 9.98209C8.51907 9.61339 8.726 9.27831 9.00063 9.00331C9.27526 8.72831 9.61006 8.52093 9.97859 8.39753L11.4689 7.91542C11.6892 7.83565 11.8789 7.68875 12.0114 7.49543C12.1438 7.30211 12.2122 7.07211 12.207 6.83784C12.2018 6.60357 12.1232 6.37683 11.9824 6.18959C11.8415 6.00234 11.6454 5.86402 11.4217 5.79411L9.94859 5.31414C9.57969 5.19139 9.2444 4.98452 8.96921 4.70989C8.69401 4.43525 8.48647 4.10038 8.36297 3.73173L7.87871 2.24467C7.80073 2.02559 7.65641 1.83626 7.46582 1.70302C7.27524 1.56978 7.04786 1.49926 6.81532 1.50126C6.58279 1.50327 6.35666 1.5777 6.16839 1.7142C5.98013 1.8507 5.83909 2.04249 5.7649 2.26288L5.27529 3.7628C5.15238 4.12156 4.94968 4.44777 4.68245 4.71685C4.41522 4.98594 4.09042 5.19089 3.73252 5.31628L2.24332 5.79518C2.02433 5.87333 1.8351 6.01771 1.7019 6.2083C1.56871 6.39888 1.49815 6.62621 1.50004 6.85872C1.50192 7.09123 1.57615 7.31738 1.71242 7.50578C1.84869 7.69418 2.04023 7.83548 2.26046 7.91006L3.73252 8.38682C4.10329 8.50986 4.44001 8.7182 4.71559 8.99509C4.99117 9.27197 5.19792 9.60967 5.31921 9.98102L5.80347 11.4713C5.88061 11.6898 6.02417 11.8784 6.21273 12.0113ZM12.7877 16.3417C12.8973 16.4188 13.0233 16.4693 13.1558 16.4892C13.2883 16.5092 13.4236 16.498 13.551 16.4566C13.6784 16.4153 13.7944 16.3448 13.8899 16.2509C13.9854 16.1569 14.0578 16.042 14.1012 15.9153L14.3669 15.0989C14.4245 14.9298 14.5197 14.7759 14.6455 14.649C14.7719 14.5204 14.9262 14.4261 15.0954 14.3704L15.9225 14.1004C16.0932 14.0415 16.2409 13.9302 16.3446 13.7824C16.4483 13.6346 16.5026 13.4578 16.4999 13.2773C16.4972 13.0967 16.4375 12.9217 16.3294 12.7771C16.2213 12.6324 16.0703 12.5257 15.8979 12.4719L15.0794 12.2041C14.9101 12.1474 14.7563 12.0523 14.6299 11.9263C14.5036 11.8003 14.408 11.6468 14.3508 11.4777L14.0809 10.6495C14.0229 10.4792 13.9128 10.3314 13.7661 10.2272C13.6195 10.1229 13.4438 10.0675 13.2638 10.0686C13.0839 10.0698 12.9089 10.1276 12.7637 10.2338C12.6184 10.34 12.5102 10.4892 12.4545 10.6603L12.1899 11.4766C12.1352 11.6446 12.0424 11.7976 11.9189 11.9238C11.7953 12.05 11.6442 12.1459 11.4774 12.2041L10.6493 12.4741C10.4789 12.5318 10.331 12.6418 10.2265 12.7883C10.122 12.9348 10.0663 13.1104 10.0673 13.2903C10.0682 13.4703 10.1258 13.6453 10.2318 13.7907C10.3378 13.9361 10.4868 14.0445 10.6578 14.1004L11.4753 14.3651C11.6467 14.4229 11.801 14.5183 11.9274 14.6447C12.0549 14.7722 12.1492 14.9264 12.2038 15.0968L12.4749 15.926C12.5336 16.0937 12.6429 16.2389 12.7877 16.3417Z"
            fill="#DB7500"
          />
        </Svg>
      </View>

      <Text size="button5" variant="default" className="!text-custom-warning-700">
        {t('MES-1013')}
      </Text>
    </Animated.View>
  )
}
const FeatureCard = ({ feature, href, colors, gradientStart, gradientEnd }: FeatureCardProps) => {
  const { t } = useTranslation()

  const handlePress = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light)
  }

  return (
    <Link href={href} asChild onPress={handlePress}>
      <TouchableOpacity className="h-full flex-1">
        <LinearGradient
          colors={colors}
          start={gradientStart}
          end={gradientEnd}
          className="justify-center rounded-lg"
          style={{
            borderRadius: 12,
            flex: 1,
            height: '100%',
            padding: 12,
            justifyContent: 'center',
            position: 'relative',
          }}
        >
          {feature.isNew && <NewBadge />}
          <View className="flex-col justify-center gap-y-3">
            <feature.icon width={50} height={50} />
            <Text size="body10" variant="white">
              {t(feature.title)}
            </Text>
          </View>
        </LinearGradient>
      </TouchableOpacity>
    </Link>
  )
}

export const FeatureList = () => {
  const { user } = useAuthentication()
  const { t } = useTranslation()

  const getMedicalHandbookHref = (): LinkProps['href'] => {
    return user
      ? (FEATURE_LIST.MEDICAL_HANDBOOK.url as LinkProps['href'])
      : (APP_ROUTES?.LOGIN?.path as LinkProps['href'])
  }

  return (
    <View className="flex-col gap-4 px-4">
      <View className="flex-row items-center gap-2">
        <Text size="heading8" variant="primary">
          {t('MES-752')}
        </Text>
        <HeartBeatIcon width={24} height={24} />
      </View>

      <View className="flex-col gap-3">
        <View className="h-[130px] flex-row gap-3">
          <FeatureCard
            feature={FEATURE_LIST.AESTHETICS}
            href={FEATURE_LIST.AESTHETICS.url as LinkProps['href']}
            colors={['#F7971E', '#F9CF7A']}
            gradientStart={{ x: 0, y: 1 }}
            gradientEnd={{ x: 1, y: 0 }}
          />
          <FeatureCard
            feature={FEATURE_LIST.MEDICAL_HANDBOOK}
            href={getMedicalHandbookHref()}
            colors={['#4C9DFF', '#8DFFE4']}
            gradientStart={{ x: 0, y: 1 }}
            gradientEnd={{ x: 1, y: 0 }}
          />
        </View>
        <View className="h-[130px] flex-row gap-3">
          <FeatureCard
            feature={FEATURE_LIST.CHAT_BOT_SEARCH_MEDICINE}
            href={FEATURE_LIST.CHAT_BOT_SEARCH_MEDICINE.url as LinkProps['href']}
            colors={['#FF3A6E', '#FFCF82']}
            gradientStart={{ x: 0, y: 1 }}
            gradientEnd={{ x: 1, y: 0 }}
          />
          <FeatureCard
            feature={FEATURE_LIST.MEDICAL_DOCUMENTS}
            href={FEATURE_LIST.MEDICAL_DOCUMENTS.url as LinkProps['href']}
            colors={['#E166F9', '#BB6FFE']}
            gradientStart={{ x: 1, y: 0 }}
            gradientEnd={{ x: 0, y: 1 }}
          />
        </View>
        <View className="h-[130px] flex-row gap-3">
          <FeatureCard
            feature={FEATURE_LIST.EXAMINATION}
            href={FEATURE_LIST.EXAMINATION.url as LinkProps['href']}
            colors={['#8BC34A', '#CDDC39']}
            gradientStart={{ x: 0, y: 1 }}
            gradientEnd={{ x: 1, y: 0 }}
          />

          <View className="flex-1" />
        </View>
      </View>
    </View>
  )
}
