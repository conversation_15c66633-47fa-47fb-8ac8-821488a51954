import { API_ENDPOINTS } from '@/constants/endpoint.constant'
import { httpService } from '@/services/http/http.service'
import { PaginatedDocs } from '@/types/global.type'
import { Params } from '@/types/http.type'

import { AxiosRequestConfig } from 'axios'
import {
  AddFavoriteAestheticResponse,
  Aesthetic,
  AestheticTreatmentNeed,
  DeleteFavoriteAestheticResponse,
  FavoriteAesthetic,
} from '../types'

// SERVER / CLIENT
class AestheticService {
  private static instance: AestheticService

  private constructor() {}

  public static getInstance(): AestheticService {
    if (!AestheticService.instance) {
      AestheticService.instance = new AestheticService()
    }
    return AestheticService.instance
  }

  /**
   * Fetch aesthetic treatment needs using the HTTP service
   *
   *
   * @param params - Optional query parameters for filtering or paginating aesthetic treatment needs.
   * @param options - Optional fetch options such as headers, method, etc.
   * @returns A promise resolving to PaginatedDocs<AestheticTreatmentNeed> or null in case of an error.
   */
  public async getAestheticTreatmentNeeds({
    params = {},
    options = {},
  }: {
    params?: Params
    options?: AxiosRequestConfig
  } = {}): Promise<PaginatedDocs<AestheticTreatmentNeed> | null> {
    const data = await httpService.getWithMethodOverride<PaginatedDocs<AestheticTreatmentNeed>>(
      `/${API_ENDPOINTS.aesthetic_treatment_needs_api}`,
      {
        params,
        ...options,
      },
    )
    return data
  }
  /**
   * Fetch aesthetic  using the HTTP service
   *
   *
   * @param params - Optional query parameters for filtering or paginating aesthetics.
   * @param options - Optional fetch options such as headers, method, etc.
   * @returns A promise resolving to PaginatedDocs<Aesthetic> or null in case of an error.
   */
  public async getAesthetics({
    params = {},
    options = {},
  }: {
    params?: Params
    options?: AxiosRequestConfig
  } = {}): Promise<PaginatedDocs<Aesthetic> | null> {
    const data = await httpService.getWithMethodOverride<PaginatedDocs<Aesthetic>>(
      `/${API_ENDPOINTS.aesthetics_api}/list`,
      {
        params,
        ...options,
      },
    )
    return data
  }

  async getFavoriteAesthetics({
    params = {},
    options = {},
  }: {
    params?: Params
    options?: AxiosRequestConfig
  } = {}): Promise<PaginatedDocs<FavoriteAesthetic> | null> {
    const data = await httpService.get<PaginatedDocs<FavoriteAesthetic>>(
      `/${API_ENDPOINTS.favorite_aesthetics_api}/user`,
      { params, ...options },
    )
    return data
  }

  async addFavoriteAesthetic(id: string, options?: AxiosRequestConfig) {
    const data = await httpService.post<AddFavoriteAestheticResponse>(
      `/${API_ENDPOINTS.favorite_aesthetics_api}/add/${id}`,
      undefined,
      options,
    )
    return data
  }

  async deleteFavoriteAesthetic(id: string, options?: AxiosRequestConfig) {
    const data = await httpService.delete<DeleteFavoriteAestheticResponse>(
      `/${API_ENDPOINTS.favorite_aesthetics_api}/remove/${id}`,
      undefined,
      options,
    )
    return data
  }
}

export const aestheticService = AestheticService.getInstance()
