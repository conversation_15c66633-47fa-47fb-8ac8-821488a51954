import { Skeleton } from '@/components/ui/Skeleton/Skeleton'
import { Text } from '@/components/ui/Text/Text'
import { useAuthentication } from '@/hooks/auth/useAuthentication'
import { APP_ROUTES, AppRoutesEnum } from '@/routes/appRoutes'
import { PaginatedDocs } from '@/types/global.type'
import { Params } from '@/types/http.type'
import { useQueryClient } from '@tanstack/react-query'
import { Link, LinkProps } from 'expo-router'
import { useCallback, useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import { TouchableOpacity, View } from 'react-native'
import { aestheticQueryKeys } from '../../hooks/query/queryKeys'
import { useGetFavoriteAesthetics } from '../../hooks/query/useGetFavoriteAesthetics'
import { Aesthetic, FavoriteAesthetic } from '../../types'
import { AestheticItem } from '../AestheticItem/AestheticItem'

export const FavoriteAestheticsSection = () => {
  const { status, user } = useAuthentication()
  const queryClient = useQueryClient()
  const params: Params = useMemo(() => {
    return {
      limit: 5,
      locale: 'all',
    }
  }, [])
  const { favoriteAesthetics, isGetFavoriteAestheticsLoading, isGetFavoriteAestheticsError } =
    useGetFavoriteAesthetics({
      params: {
        limit: 5,
        locale: 'all',
      },
      useQueryOptions: {
        staleTime: 0,
      },
      overrideKey: [aestheticQueryKeys['favoriteAestheticsInMainScreen'].base(), params],
    })
  const { t } = useTranslation()

  // Handle removing favorite aesthetic
  const handleUpdatingFavoriteAesthetic = useCallback(
    ({ type, id }: { type: 'add' | 'delete'; id: string }) => {
      if (type === 'delete') {
        const queryKey = [aestheticQueryKeys['favoriteAestheticsInMainScreen'].base(), params]

        queryClient.setQueryData<PaginatedDocs<FavoriteAesthetic>>(queryKey, (oldData) => {
          if (!oldData) return oldData

          // Remove the favorite aesthetic from docs
          const filteredDocs = oldData.docs.filter((favoriteAesthetic) => {
            const aestheticId =
              typeof favoriteAesthetic.aesthetic === 'string'
                ? favoriteAesthetic.aesthetic
                : favoriteAesthetic.aesthetic.id
            return aestheticId !== id
          })

          return {
            ...oldData,
            docs: filteredDocs,
            totalDocs: Math.max(
              0,
              (oldData.totalDocs || 0) - (oldData.docs.length - filteredDocs.length),
            ),
          }
        })
      }
    },
    [queryClient, params],
  )
  if (
    !user ||
    status === 'unauthorized' ||
    isGetFavoriteAestheticsError ||
    !favoriteAesthetics?.docs.length
  )
    return null

  return (
    <View className="flex flex-col gap-y-3">
      <View className="mb-1 flex flex-row items-center justify-between gap-x-1">
        <View>
          <Text size="body3" variant="default">
            {t('MES-1011')} ({favoriteAesthetics?.totalDocs})
          </Text>
        </View>
        <Link
          href={
            APP_ROUTES.AESTHETICS?.children?.[AppRoutesEnum.FAVORITE_AESTHETICS]
              .path as LinkProps['href']
          }
          asChild
        >
          <TouchableOpacity>
            <Text size="link3" variant="primary">
              {t('MES-141')}
            </Text>
          </TouchableOpacity>
        </Link>
      </View>
      {isGetFavoriteAestheticsLoading ? (
        <>
          <View className="flex flex-col gap-y-4">
            {new Array(8).fill(0).map((_, index) => (
              <Skeleton key={index} className="h-20 w-full" />
            ))}
          </View>
        </>
      ) : (
        <>
          {favoriteAesthetics?.docs.map((favoriteAesthetic) => {
            const aesthetic = favoriteAesthetic?.aesthetic as Aesthetic
            return (
              <AestheticItem
                key={favoriteAesthetic.id}
                aesthetic={aesthetic}
                isShowFavoriteButton={true}
                onUpdatingFavoriteAesthetic={handleUpdatingFavoriteAesthetic}
              />
            )
          })}
        </>
      )}
    </View>
  )
}
