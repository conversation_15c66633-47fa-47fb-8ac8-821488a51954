import ArrowForwardIcon from '@/assets/icons/arrow-forward.svg'
import BodyFocusIcon from '@/assets/icons/body-focus.svg'
import { Text } from '@/components/ui/Text/Text'
import { APP_ROUTES, AppRoutesEnum } from '@/routes/appRoutes'
import { LinearGradient } from 'expo-linear-gradient'
import { Link, LinkProps } from 'expo-router'
import { useTranslation } from 'react-i18next'
import { TouchableOpacity, View } from 'react-native'
export const AestheticSmartFeatures = () => {
  const { t } = useTranslation()

  return (
    <View className="flex flex-col gap-y-4">
      <Text size="body3" variant="default">
        {t('MES-958')}
      </Text>
      <LinearGradient
        colors={['#FA61AC', '#FEBDCB']}
        start={{ x: 0, y: 1 }}
        end={{ x: 1, y: 0 }}
        className="flex  justify-end rounded-lg p-4"
        style={{
          borderRadius: 8,
          flex: 1,
          height: '100%',
          paddingVertical: 12,
          paddingHorizontal: 16,
        }}
      >
        <View className="flex flex-row items-center justify-between gap-x-6">
          <View className="flex flex-1 flex-col gap-y-2">
            <View>
              <Text size="body3" variant="white">
                {t('MES-959')}
              </Text>
            </View>
            <Link
              href={
                APP_ROUTES.AESTHETICS?.children?.[AppRoutesEnum.AESTHETICS_AREA_SELECTION_INTRO]
                  .path as LinkProps['href']
              }
              asChild
            >
              <TouchableOpacity className="flex flex-row items-center gap-x-2 self-start rounded-[6px] bg-white px-3 py-2 ">
                <Text size="button5" variant="primary">
                  {t('MES-960')}
                </Text>
                <ArrowForwardIcon width={16} height={16} />
              </TouchableOpacity>
            </Link>
          </View>
          <View>
            <BodyFocusIcon />
          </View>
        </View>
      </LinearGradient>
    </View>
  )
}
