import { Text } from '@/components/ui/Text/Text'
import { cn } from '@/utils/cn'
import { Link, LinkProps } from 'expo-router'
import React from 'react'
import { useTranslation } from 'react-i18next'
import { FlatList, TouchableOpacity, View } from 'react-native'
import { Aesthetic } from '../../types'
import { AestheticCard } from '../AestheticCard/AestheticCard'
import { AestheticHorizontalListLoading } from '../AestheticHorizontalListLoading/AestheticHorizontalListLoading'

interface AestheticHorizontalSectionProps {
  title: string
  aesthetics?: Aesthetic[]
  isLoading: boolean
  seeAllLink?: LinkProps['href']
  backgroundColor?: string
}

export const AestheticHorizontalSection = ({
  title,
  aesthetics,
  isLoading,
  seeAllLink,
  backgroundColor,
}: AestheticHorizontalSectionProps) => {
  const { t } = useTranslation()

  if (!isLoading && !aesthetics?.length) {
    return null
  }

  return (
    <View className="flex flex-col gap-y-3">
      <View className={cn('mb-1', seeAllLink ? 'flex flex-row items-center justify-between' : '')}>
        <Text size="body3" variant="default">
          {title}
        </Text>
        {seeAllLink && (
          <Link href={seeAllLink} asChild>
            <TouchableOpacity>
              <Text size="link3" variant="primary">
                {t('MES-141')}
              </Text>
            </TouchableOpacity>
          </Link>
        )}
      </View>

      {isLoading ? (
        <AestheticHorizontalListLoading />
      ) : (
        <FlatList
          horizontal
          data={aesthetics || []}
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={{
            gap: 12,
          }}
          renderItem={({ item }) => <AestheticCard item={item} backgroundColor={backgroundColor} />}
          keyExtractor={(item) => item.id}
        />
      )}
    </View>
  )
}
