import BackBodyIcon from '@/assets/icons/back-body-area.svg'
import CloseIcon from '@/assets/icons/close-icon.svg'
import FaceAreaInjectionIcon from '@/assets/icons/face-area-injection.svg'
import FaceAreaIcon from '@/assets/icons/face-area.svg'
import FrontBodyIcon from '@/assets/icons/front-body-area.svg'
import OldFaceAreaIcon from '@/assets/icons/old-face-area.svg'
import UpperFrontBodyIcon from '@/assets/icons/upper-front-body-area.svg'
import { Text } from '@/components/ui/Text/Text'
import { APP_ROUTES, AppRoutesEnum } from '@/routes/appRoutes'
import { coralPink, danger, lavender, neutral } from '@/styles/_colors'
import * as Haptics from 'expo-haptics'
import { LinearGradient } from 'expo-linear-gradient'
import { Link, LinkProps, useRouter } from 'expo-router'
import { useEffect, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { ScrollView, TouchableOpacity, useWindowDimensions, View } from 'react-native'
import Animated, {
  Easing,
  Extrapolation,
  interpolate,
  SharedValue,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  withTiming,
} from 'react-native-reanimated'
import Carousel, { ICarouselInstance, Pagination } from 'react-native-reanimated-carousel'
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context'
import Svg, { Path } from 'react-native-svg'
import { scheduleOnRN } from 'react-native-worklets'
import { AestheticBodyAreaEnum } from '../../enums'
const areaData = [
  {
    title: 'MES-893',
    description: 'MES-894',
    icon: [FaceAreaIcon, FaceAreaInjectionIcon, OldFaceAreaIcon],
    backgroundColor: coralPink['500'],
    screenGradientColors: ['#FFFFFF', '#F8CACA'],
    boxShadowColor: '#FAD6D6',
    buttonTextColor: danger['600'],
    bodyArea: AestheticBodyAreaEnum.HEAD,
  },
  {
    title: 'MES-895',
    description: 'MES-896',
    icon: [FrontBodyIcon, BackBodyIcon, UpperFrontBodyIcon],
    backgroundColor: lavender['400'],
    screenGradientColors: ['#FFFFFF', '#D5C6F4'],
    boxShadowColor: '#E7DFFA',
    buttonTextColor: '#7E57FF',
    bodyArea: AestheticBodyAreaEnum.FULL_BODY,
  },
]

const horizontalPadding = 16 // Padding on each side
const maxCarouselWidth = 540 // Max width for tablets/iPads to prevent overflow

export const AestheticsAreaSelectionIntro = () => {
  const { t } = useTranslation()
  const router = useRouter()
  const { width: screenWidth } = useWindowDimensions()
  const ref = useRef<ICarouselInstance>(null)
  const insets = useSafeAreaInsets()
  const progress = useSharedValue<number>(0)

  // Calculate carousel width with max constraint for tablets
  const carouselWidth = Math.min(screenWidth - horizontalPadding * 2, maxCarouselWidth)
  const onPressPagination = (index: number) => {
    ref.current?.scrollTo({
      /**
       * Calculate the difference between the current index and the target index
       * to ensure that the carousel scrolls to the nearest index
       */
      count: index - progress.value,
      animated: true,
    })
  }

  // Animate overlay opacity based on progress for smooth gradient transition
  const overlayOpacity = useAnimatedStyle(() => {
    'worklet'
    // Interpolate opacity from 0 to 1 as we move from first to second item
    const opacity = interpolate(progress.value, [0, 1], [0, 1], Extrapolation.CLAMP)
    return {
      opacity,
    }
  })

  return (
    <SafeAreaView className="flex-1" edges={['left', 'right']}>
      <View
        style={{
          borderRadius: 8,
          flex: 1,
          width: '100%',
          height: '100%',
          position: 'relative',
        }}
      >
        {/* Base gradient (first item) */}
        <LinearGradient
          colors={areaData[0].screenGradientColors as [string, string]}
          start={{ x: 0, y: 1 }}
          end={{ x: 1, y: 0 }}
          className="flex items-center justify-center rounded-lg"
          style={{
            borderRadius: 8,
            flex: 1,
            width: '100%',
            height: '100%',
            // paddingVertical: 12,
            // paddingHorizontal: 16,
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
          }}
        />
        {/* Overlay gradient (second item) with animated opacity */}
        <Animated.View
          style={[
            {
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              borderRadius: 8,
            },
            overlayOpacity,
          ]}
        >
          <LinearGradient
            colors={areaData[1].screenGradientColors as [string, string]}
            start={{ x: 0, y: 1 }}
            end={{ x: 1, y: 0 }}
            style={{
              borderRadius: 8,
              flex: 1,
              width: '100%',
              height: '100%',
              paddingVertical: 12,
              paddingHorizontal: 16,
            }}
          />
        </Animated.View>
        <View
          style={{
            height: insets.top,
            backgroundColor: 'transparent',
          }}
        ></View>
        {/* Content layer */}
        <View
          style={{
            flex: 1,
            width: '100%',
            height: '100%',
            // paddingVertical: 12,
            // paddingHorizontal: 16,
          }}
        >
          <ScrollView
            contentContainerStyle={{ paddingBottom: insets.bottom }}
            showsVerticalScrollIndicator={false}
          >
            <View className="mb-4 flex flex-row self-end">
              <TouchableOpacity
                onPress={() => router.back()}
                accessibilityRole="button"
                accessibilityLabel={'close'}
                hitSlop={10}
                className="mx-4"
              >
                <CloseIcon width={36} height={36} />
              </TouchableOpacity>
            </View>
            <View className="mx-4 mb-6 flex flex-col items-center justify-center gap-y-3 p-3">
              <Text size="heading7" variant="default" className="text-center">
                {t('MES-891')}
              </Text>
              <Text size="body7" variant="default" className="text-center">
                {t('MES-892')}
              </Text>
            </View>
            <View
              id="carousel-component"
              className="mt-[120px] px-4"
              style={{ alignItems: 'center' }}
            >
              <Carousel
                ref={ref}
                loop={false}
                width={carouselWidth}
                snapEnabled={true}
                pagingEnabled={true}
                autoPlay={false}
                data={areaData}
                height={320}
                style={{ overflow: 'visible', width: carouselWidth, maxWidth: '100%' }}
                mode="parallax"
                modeConfig={{
                  parallaxScrollingScale: 1,
                  parallaxScrollingOffset: 0,
                  parallaxAdjacentItemScale: 0.8,
                }}
                onProgressChange={progress}
                renderItem={({ item, index }) => (
                  <CarouselItem item={item} index={index} progress={progress} />
                )}
              />

              <Pagination.Custom<{ color: string }>
                progress={progress}
                data={areaData.map((item) => ({ color: item.backgroundColor }))}
                dotStyle={{
                  backgroundColor: neutral['200'],
                  borderRadius: 999,
                  width: 8,
                  height: 8,
                  opacity: 0.7,
                }}
                activeDotStyle={{
                  backgroundColor: neutral['300'],
                  borderRadius: 999,
                  width: 10,
                  height: 10,
                  opacity: 1,
                }}
                containerStyle={{
                  gap: 6,
                  marginTop: 24,
                  alignItems: 'center',
                }}
                horizontal
                onPress={onPressPagination}
              />

              <Text size="body9" variant="subdued" className="mt-3 text-center">
                {t('MES-969')}
              </Text>
            </View>
          </ScrollView>
        </View>

        <View
          style={{
            height: insets.bottom,
            backgroundColor: 'transparent',
          }}
        ></View>
      </View>
    </SafeAreaView>
  )
}
interface CarouselItemProps {
  item: (typeof areaData)[0]
  index: number
  progress: SharedValue<number>
  bodyArea?: AestheticBodyAreaEnum
}

const CarouselItem = ({ item, index, progress }: CarouselItemProps) => {
  const [currentIconIndex, setCurrentIconIndex] = useState(0)
  const iconOpacity = useSharedValue(1)
  const iconScale = useSharedValue(1)
  const { t } = useTranslation()
  const icons = item.icon
  const CurrentIconComponent = icons[currentIconIndex]

  useEffect(() => {
    const updateIconIndex = () => {
      setCurrentIconIndex((prev) => (prev + 1) % icons.length)
    }

    const interval = setInterval(() => {
      // Scale down + fade out with smooth easing
      iconOpacity.value = withTiming(
        0,
        {
          duration: 400,
          easing: Easing.out(Easing.cubic),
        },
        (finished) => {
          'worklet'
          if (finished) {
            // Change icon  when fade out completes (opacity = 0)
            scheduleOnRN(updateIconIndex)
          }
        },
      )
      iconScale.value = withTiming(0.7, {
        duration: 400,
        easing: Easing.out(Easing.cubic),
      })

      setTimeout(() => {
        // Reset values for entrance
        iconScale.value = 0.7

        //  Scale up + fade in with spring bounce
        iconOpacity.value = withTiming(1, {
          duration: 500,
          easing: Easing.out(Easing.cubic),
        })
        iconScale.value = withSpring(1, {
          damping: 10,
          stiffness: 120,
          mass: 0.8,
        })
      }, 400)
    }, 1500)

    return () => clearInterval(interval)
  }, [icons.length, iconOpacity, iconScale])

  const animatedStyle = useAnimatedStyle(() => {
    'worklet'
    const distance = Math.abs(progress.value - index)
    const opacity = interpolate(distance, [0, 1], [1, 0.3], Extrapolation.CLAMP)

    return {
      opacity,
    }
  })

  const iconAnimatedStyle = useAnimatedStyle(() => ({
    opacity: iconOpacity.value,
    transform: [{ scale: iconScale.value }],
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  }))

  return (
    <Animated.View
      className="mb-4 flex-1 items-center justify-between rounded-3xl p-6"
      style={[
        {
          backgroundColor: item.backgroundColor,
          width: '100%',
          maxWidth: '100%',
        },
        animatedStyle,
      ]}
    >
      <View className=" items-center">
        {/* <View
          className="h-[206px] w-[178px] items-center justify-center bg-white"
          style={{
            borderRadius: 103,
            marginTop: -120,
            boxShadow: `0px 4px 10px 2px ${item.boxShadowColor}`,
          }}
        >
          <Animated.View style={iconAnimatedStyle}>
            <CurrentIconComponent />
          </Animated.View>
        </View> */}
        <View
          style={{
            width: 227,
            height: 263,
            shadowColor: item.boxShadowColor,
            shadowOffset: { width: -2, height: 6 },
            shadowOpacity: 0.8,
            shadowRadius: 16,
            elevation: 8,
            position: 'relative',
            marginTop: -150,
          }}
        >
          <Svg width={227} height={263} viewBox="0 0 227 263" fill="none">
            <Path
              d="M199.3 127.3C199.3 184.185 161.244 230.3 114.3 230.3C67.3556 230.3 29.2998 184.185 29.2998 127.3C29.2998 70.4145 67.3556 24.2998 114.3 24.2998C161.244 24.2998 199.3 70.4145 199.3 127.3Z"
              fill="#F8FCFE"
            />
          </Svg>
          <View className="absolute inset-0 items-center justify-center">
            <Animated.View style={iconAnimatedStyle}>
              <CurrentIconComponent />
            </Animated.View>
          </View>
        </View>

        <View className=" flex flex-col gap-y-2 px-6">
          <Text size="heading8" variant="white" className=" text-center">
            {t(item.title)}
          </Text>
          <Text size="body7" variant="white" className="text-center ">
            {t(item.description)}
          </Text>
        </View>
      </View>
      <Link
        href={
          {
            pathname: APP_ROUTES.AESTHETICS?.children?.[AppRoutesEnum.AESTHETICS_AREA_SELECTION]
              .path as LinkProps['href'],
            params: {
              bodyArea: item.bodyArea || AestheticBodyAreaEnum.HEAD,
            },
          } as LinkProps['href']
        }
        asChild
      >
        <TouchableOpacity
          className="mt-4 flex w-[93px] flex-row items-center justify-center gap-1 rounded-full bg-custom-background-form px-3 py-1"
          onPress={() => {
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light)
          }}
        >
          <Text
            size="body6"
            variant="default"
            style={{
              color: item.buttonTextColor,
            }}
          >
            {t('MES-101')}
          </Text>
          <Svg width={14} height={14} viewBox="0 0 14 14" fill="none">
            <Path
              d="M4.9502 11.0666L8.57242 7.44438C9.0002 7.0166 9.0002 6.3166 8.57242 5.88882L4.9502 2.2666"
              stroke={item.buttonTextColor}
              strokeWidth={1.5}
              strokeMiterlimit={10}
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </Svg>
        </TouchableOpacity>
      </Link>
    </Animated.View>
  )
}
