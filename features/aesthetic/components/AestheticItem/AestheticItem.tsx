import { Text } from '@/components/ui/Text/Text'
import { LocaleEnum } from '@/enums/locale.enum'
import { useAppLanguage } from '@/hooks/common/useAppLanguage'
import { StyledExpoImage } from '@/libs/styled'
import { primary } from '@/styles/_colors'
import { LocalizeField } from '@/types/global.type'
import { Media } from '@/types/media.type'
import { getMediaInfo } from '@/utils/media-cms'
import React, { memo, useCallback, useEffect, useState } from 'react'
import { TouchableOpacity, View } from 'react-native'
import {
  AESTHETIC_EFFECTIVENESS_OPTIONS,
  AESTHETIC_INTERVENTION_LEVELS_OPTIONS,
} from '../../constants'
import { AestheticEffectivenessEnum, AestheticInterventionLevelsEnum } from '../../enums'

import ArchiveIcon from '@/assets/icons/archive-icon.svg'
import ArchiveTickIcon from '@/assets/icons/archive-tick-icon.svg'
import { APP_ROUTES, AppRoutesEnum } from '@/routes/appRoutes'
import { cn } from '@/utils/cn'
import * as Haptics from 'expo-haptics'
import { Link, LinkProps } from 'expo-router'
import { SvgUri } from 'react-native-svg'
import { useUpdateFavoriteAesthetic } from '../../hooks/query/useUpdateFavoriteAesthetic'
import { Aesthetic } from '../../types'
interface AestheticItemProps {
  aesthetic: Aesthetic
  onUpdateFavoriteAestheticSuccess?: ({ type, id }: { type: 'add' | 'delete'; id: string }) => void
  onUpdatingFavoriteAesthetic?: ({ type, id }: { type: 'add' | 'delete'; id: string }) => void
  isShowFavoriteButton?: boolean
}
export const AestheticItem = memo(
  ({
    aesthetic,
    onUpdateFavoriteAestheticSuccess,
    onUpdatingFavoriteAesthetic,
    isShowFavoriteButton = false,
  }: AestheticItemProps) => {
    const { effectiveness, id, interventionLevels, isFavorite } = aesthetic

    const { media: iconMedia } = getMediaInfo(aesthetic.icon as Media)
    const { mimeType: iconMimeType, url: iconUrl } = (iconMedia as Media) || {}
    const { media: imageMedia } = getMediaInfo(aesthetic.image as Media)
    const { mimeType: imageMimeType, url: imageUrl } = (imageMedia as Media) || {}
    const displaySource = imageUrl || iconUrl
    const displayMimeType = imageMimeType || iconMimeType
    const localizedName = aesthetic.name as unknown as LocalizeField<string>
    const { primaryLanguage, secondaryLanguage } = useAppLanguage()

    const [isFavoriteAesthetic, setIsFavoriteAesthetic] = useState<boolean>(Boolean(isFavorite))

    const { updateFavoriteAestheticMutation, isUpdateFavoriteAestheticPending } =
      useUpdateFavoriteAesthetic()

    const handleUpdateFavoriteAesthetic = useCallback(
      (id: string, isFavorite: boolean) => {
        const type = isFavorite ? 'delete' : 'add'
        // Optimistic update - update immediately
        setIsFavoriteAesthetic(type === 'add' ? true : false)
        onUpdatingFavoriteAesthetic?.({ type, id })
        updateFavoriteAestheticMutation(
          { id, type },
          {
            onSuccess: () => {
              setIsFavoriteAesthetic(type === 'add' ? true : false)
              onUpdateFavoriteAestheticSuccess?.({ type, id })
            },
            onError: () => {
              // Rollback on error
              setIsFavoriteAesthetic(isFavorite)
            },
          },
        )
      },
      [
        updateFavoriteAestheticMutation,
        onUpdateFavoriteAestheticSuccess,
        onUpdatingFavoriteAesthetic,
      ],
    )
    useEffect(() => {
      setIsFavoriteAesthetic(Boolean(isFavorite))
    }, [isFavorite])
    return (
      <Link
        href={
          {
            pathname:
              APP_ROUTES.AESTHETICS?.children?.[AppRoutesEnum.AESTHETICS_DETAILS]?.path + '/[id]',
            params: {
              id: aesthetic.id,
            },
          } as LinkProps['href']
        }
        asChild
      >
        <TouchableOpacity
          onPress={() => {
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light)
          }}
          className="flex flex-row items-center gap-x-3 rounded-lg bg-custom-background-form p-3"
        >
          {displaySource && (
            <>
              {displayMimeType === 'image/svg+xml' ? (
                <View className="h-[72px] w-[72px] flex-shrink-0 overflow-hidden rounded-[4px]">
                  <SvgUri width="100%" height="100%" uri={displaySource} />
                </View>
              ) : (
                <View className="h-[72px] w-[72px] flex-shrink-0 overflow-hidden rounded-[4px]">
                  <StyledExpoImage source={displaySource} className="h-full w-full" />
                </View>
              )}
            </>
          )}
          <View className="flex flex-1 flex-col justify-between gap-y-3 ">
            <View className="flex flex-col gap-y-1">
              <Text size="body6" variant="default" numberOfLines={2}>
                {localizedName[primaryLanguage as LocaleEnum]}
              </Text>
              {secondaryLanguage && (
                <Text size="body9" variant="subdued" numberOfLines={2}>
                  {localizedName[secondaryLanguage as LocaleEnum]}
                </Text>
              )}
            </View>
            <View className="flex flex-row flex-wrap items-center gap-x-2">
              {effectiveness && (
                <View
                  className="rounded-[4px] bg-primary-80 px-2 py-1"
                  style={{
                    backgroundColor: primary['80'],
                  }}
                >
                  <Text size="body8" variant="primary">
                    {
                      AESTHETIC_EFFECTIVENESS_OPTIONS[effectiveness as AestheticEffectivenessEnum]
                        ?.fixedLabel?.[primaryLanguage as LocaleEnum]
                    }
                  </Text>
                </View>
              )}
              {interventionLevels && (
                <View
                  className="rounded-[4px] bg-primary-80 px-2 py-1"
                  style={{
                    backgroundColor: primary['80'],
                  }}
                >
                  <Text size="body8" variant="primary">
                    {
                      AESTHETIC_INTERVENTION_LEVELS_OPTIONS[
                        interventionLevels as AestheticInterventionLevelsEnum
                      ]?.fixedLabel?.[primaryLanguage as LocaleEnum]
                    }
                  </Text>
                </View>
              )}
            </View>
          </View>

          {isShowFavoriteButton && (
            <TouchableOpacity
              disabled={isUpdateFavoriteAestheticPending}
              onPress={(e) => {
                e.stopPropagation()
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light)
                handleUpdateFavoriteAesthetic(id, isFavoriteAesthetic)
              }}
              className={cn('h-7 w-7', isUpdateFavoriteAestheticPending && 'opacity-50')}
            >
              {isFavoriteAesthetic ? (
                <ArchiveTickIcon width={24} height={24} />
              ) : (
                <ArchiveIcon width={24} height={24} />
              )}
            </TouchableOpacity>
          )}
        </TouchableOpacity>
      </Link>
    )
  },
)

AestheticItem.displayName = 'AestheticItem'
