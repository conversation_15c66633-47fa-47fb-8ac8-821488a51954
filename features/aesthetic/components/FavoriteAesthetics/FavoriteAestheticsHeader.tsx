import FilterIcon from '@/assets/icons/filter-icon.svg'
import { SearchInput, SearchInputRef } from '@/components/ui/SearchInput/SearchInput'
import { debounce } from 'lodash-es'
import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { Keyboard, TouchableOpacity, View } from 'react-native'
import { useShallow } from 'zustand/react/shallow'
import { useAestheticStoreContext } from '../../contexts/AestheticStoreContext'
import { useOpenFilterAestheticBox } from '../FilterAestheticBox/FilterAestheticBox'

export const FavoriteAestheticsHeader = () => {
  const { t } = useTranslation()
  const { useStore } = useAestheticStoreContext()
  const { hasActiveFilters } = useStore(
    useShallow((state) => ({
      hasActiveFilters: state.hasActiveFilters,
    })),
  )
  const [searchInputValue, setSearchInputValue] = useState('')
  const searchInputRef = useRef<SearchInputRef>(null)

  const { setSearchTextValue } = useStore(
    useShallow((state) => ({
      setSearchTextValue: state.setSearchTextValue,
    })),
  )

  // Create debounced search function
  const debouncedSearch = useMemo(
    () =>
      debounce((value: string) => {
        setSearchTextValue(value)
      }, 300),
    [setSearchTextValue],
  )

  // Cleanup debounced function on unmount
  useEffect(() => {
    return () => {
      debouncedSearch.cancel()
    }
  }, [debouncedSearch])

  const handleSearchInputChange = useCallback(
    (text: string) => {
      setSearchInputValue(text)
      debouncedSearch(text)
    },
    [debouncedSearch],
  )

  const handleClearSearchInput = useCallback(() => {
    setSearchInputValue('')
    debouncedSearch.cancel()
    setSearchTextValue('')
  }, [debouncedSearch, setSearchTextValue])

  const handleSubmitEditing = useCallback(() => {
    debouncedSearch.cancel()
    setSearchTextValue(searchInputValue)
  }, [debouncedSearch, setSearchTextValue, searchInputValue])

  const { handleOpenFilterAestheticBox } = useOpenFilterAestheticBox()

  return (
    <View className="flex flex-col gap-y-2 px-4">
      <View className="flex flex-row items-center gap-x-2">
        <SearchInput
          ref={searchInputRef}
          placeholder={t('MES-66')}
          value={searchInputValue}
          onChangeText={handleSearchInputChange}
          onClear={handleClearSearchInput}
          onSubmitEditing={handleSubmitEditing}
        />
        <TouchableOpacity
          hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
          onPress={() => {
            Keyboard.dismiss()
            handleOpenFilterAestheticBox()
          }}
          className="relative flex h-full flex-row items-center gap-x-2 rounded-lg border border-custom-divider-border p-3"
        >
          <FilterIcon width={18} height={18} />
          {hasActiveFilters && (
            <View className="absolute -right-1 -top-1 size-4 rounded-full bg-custom-danger-600/80" />
          )}
        </TouchableOpacity>
      </View>
    </View>
  )
}
