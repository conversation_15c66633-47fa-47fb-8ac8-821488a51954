import CloseIconDanger from '@/assets/icons/close-icon-danger.svg'
import EmptyBoxIcon from '@/assets/icons/empty-box.svg'
import { Skeleton } from '@/components/ui/Skeleton/Skeleton'
import { Text } from '@/components/ui/Text/Text'
import { primary } from '@/styles/_colors'
import { PaginatedDocs } from '@/types/global.type'
import { InfiniteData, useQueryClient } from '@tanstack/react-query'
import React, { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { ActivityIndicator, FlatList, RefreshControl, TouchableOpacity, View } from 'react-native'
import { useShallow } from 'zustand/react/shallow'

import { useSafeAreaInsets } from 'react-native-safe-area-context'
import { useAestheticStoreContext } from '../../contexts/AestheticStoreContext'
import { aestheticQueryKeys } from '../../hooks/query/queryKeys'
import { useGetInfiniteFavoriteAesthetics } from '../../hooks/query/useGetInfiniteFavoriteAesthetics'
import { SearchAestheticFilterItem, SearchAestheticFilterType } from '../../stores/AestheticStore'
import { Aesthetic, FavoriteAesthetic } from '../../types'
import { AestheticItem } from '../AestheticItem/AestheticItem'
import { SelectedFilterBadge } from '../FilterAestheticBox/SelectedFilterBadge'

export const FavoriteAestheticsList = () => {
  const { t } = useTranslation()
  const [refreshing, setRefreshing] = useState(false)
  const queryClient = useQueryClient()
  const { useStore } = useAestheticStoreContext()
  const {
    searchTextValue,
    searchAestheticFilters,
    toggleSearchAestheticFilter,
    clearAllSearchAestheticFilters,
    resetAllAestheticFilters,
  } = useStore(
    useShallow((state) => ({
      searchTextValue: state.searchAestheticFilters[SearchAestheticFilterType.SEARCH_TEXT],
      searchAestheticFilters: state.searchAestheticFilters,
      toggleSearchAestheticFilter: state.toggleSearchAestheticFilter,
      clearAllSearchAestheticFilters: state.clearAllSearchAestheticFilters,
      resetAllAestheticFilters: state.resetAllAestheticFilters,
    })),
  )

  // Build query params based on filters
  const queryParams = useMemo(() => {
    // Treatment need filter
    const treatmentNeedIds = searchAestheticFilters[SearchAestheticFilterType.TREATMENT_NEED]?.map(
      (f) => f.id,
    )

    // Intervention level filter
    const interventionLevelIds = searchAestheticFilters[
      SearchAestheticFilterType.INTERVENTION_LEVEL
    ]?.map((f) => f.id)

    // Recovery time filter
    const recoveryTimeIds = searchAestheticFilters[SearchAestheticFilterType.RECOVERY_TIME]?.map(
      (f) => f.id,
    )

    // Effectiveness filter
    const effectivenessIds = searchAestheticFilters[SearchAestheticFilterType.EFFECTIVENESS]?.map(
      (f) => f.id,
    )

    // Special types filter
    const specialTypesIds = searchAestheticFilters[SearchAestheticFilterType.SPECIAL_TYPES]?.map(
      (f) => f.id,
    )

    // Body area filter
    const bodyAreaIds = searchAestheticFilters[SearchAestheticFilterType.BODY_AREA]?.map(
      (f) => f.id,
    )

    // Body parts filter
    const bodyPartsIds = searchAestheticFilters[SearchAestheticFilterType.BODY_PARTS]?.map(
      (f) => f.id,
    )

    return {
      locale: 'all',
      limit: 20,
      searchQuery: searchTextValue,
      select: {
        name: 'true',
        icon: 'true',
        image: 'true',
        interventionLevels: 'true',
        effectiveness: 'true',
        recoveryTimes: 'true',
        specialTypes: 'true',
        bodyAreas: 'true',
        bodyParts: 'true',
        treatmentNeeds: 'true',
      },
      interventionLevels: interventionLevelIds || [],
      treatmentNeeds: treatmentNeedIds || [],
      recoveryTimes: recoveryTimeIds || [],
      effectiveness: effectivenessIds || [],
      specialTypes: specialTypesIds || [],
      bodyAreas: bodyAreaIds || [],
      bodyParts: bodyPartsIds || [],
    }
  }, [searchTextValue, searchAestheticFilters])

  const {
    favoriteAesthetics,
    isGetFavoriteAestheticsError,
    isGetFavoriteAestheticsFetching,
    isGetFavoriteAestheticsFetchingNextPage,
    fetchNextPage,
    hasNextPage,
    isRefetching,
    refetch,
  } = useGetInfiniteFavoriteAesthetics({
    params: queryParams,
    config: {
      staleTime: 0,
    },
  })

  // Handle pull-to-refresh
  const handleRefresh = useCallback(async () => {
    setRefreshing(true)
    try {
      await refetch()
    } finally {
      setRefreshing(false)
    }
  }, [refetch])

  // Flatten all aesthetics from all pages
  const allFavoriteAesthetics = useMemo(() => {
    if (!favoriteAesthetics?.pages) return []
    return favoriteAesthetics.pages.flatMap((page) => page?.docs || [])
  }, [favoriteAesthetics])

  const data = useMemo((): ListItem[] => {
    const items: ListItem[] = []

    // Show loading skeletons if initial loading or refreshing
    if (isGetFavoriteAestheticsFetching || isRefetching || refreshing) {
      items.push({ type: 'loading_skeleton' })
    } else {
      // Aesthetics as individual items
      allFavoriteAesthetics.forEach((favoriteAesthetic) => {
        items.push({ type: 'aesthetic', aesthetic: favoriteAesthetic?.aesthetic as Aesthetic })
      })

      // Loading indicator for pagination
      if (isGetFavoriteAestheticsFetchingNextPage) {
        items.push({ type: 'loading' })
      }
    }

    return items
  }, [
    allFavoriteAesthetics,
    isGetFavoriteAestheticsFetchingNextPage,
    isGetFavoriteAestheticsFetching,
    isRefetching,
    refreshing,
  ])

  const handleLoadMore = useCallback(() => {
    if (hasNextPage && !isGetFavoriteAestheticsFetchingNextPage) {
      fetchNextPage()
    }
  }, [hasNextPage, isGetFavoriteAestheticsFetchingNextPage, fetchNextPage])

  // Handle removing favorite aesthetic from the list using setQueryData
  const handleUpdatingFavoriteAesthetic = useCallback(
    ({ type, id }: { type: 'add' | 'delete'; id: string }) => {
      if (type === 'delete') {
        const queryKey = [aestheticQueryKeys['favoriteAesthetics'].base(), queryParams]

        queryClient.setQueryData<InfiniteData<PaginatedDocs<FavoriteAesthetic>>>(
          queryKey,
          (oldData) => {
            if (!oldData) return oldData

            // Remove the favorite aesthetic from all pages
            return {
              ...oldData,
              pages: oldData.pages.map((page) => {
                if (!page) return page

                const filteredDocs = page.docs.filter((favoriteAesthetic) => {
                  const aestheticId =
                    typeof favoriteAesthetic.aesthetic === 'string'
                      ? favoriteAesthetic.aesthetic
                      : favoriteAesthetic.aesthetic.id
                  return aestheticId !== id
                })

                return {
                  ...page,
                  docs: filteredDocs,
                  totalDocs: Math.max(
                    0,
                    (page.totalDocs || 0) - (page.docs.length - filteredDocs.length),
                  ),
                }
              }),
            }
          },
        )
      }
    },
    [queryClient, queryParams],
  )

  // Detect invalidation of favorite aesthetics in main screen
  const shouldInvalidateQueries = useRef<boolean>(false)
  const handleInvalidateQueries = useCallback(() => {
    if (shouldInvalidateQueries.current === false) {
      return
    }
    queryClient.invalidateQueries({
      queryKey: [aestheticQueryKeys['favoriteAestheticsInMainScreen'].base()],
      type: 'active',
      refetchType: 'active',
      exact: false,
    })
  }, [queryClient])

  useEffect(() => {
    return () => {
      handleInvalidateQueries()
    }
  }, [handleInvalidateQueries])

  const renderItem = useCallback(
    ({ item }: { item: ListItem }) => {
      switch (item.type) {
        case 'aesthetic':
          return (
            <AestheticItem
              aesthetic={item.aesthetic}
              isShowFavoriteButton={true}
              onUpdateFavoriteAestheticSuccess={({ type, id }) => {
                shouldInvalidateQueries.current = true
                handleUpdatingFavoriteAesthetic({ type, id })
              }}
            />
          )

        case 'loading':
          return (
            <View className="items-center py-2">
              <ActivityIndicator size="small" />
            </View>
          )

        case 'loading_skeleton':
          return (
            <View className="flex flex-col gap-y-4">
              {new Array(8).fill(0).map((_, index) => (
                <Skeleton key={index} className="h-20 w-full" />
              ))}
            </View>
          )

        default:
          return null
      }
    },
    [handleUpdatingFavoriteAesthetic],
  )

  const renderHeaderComponent = useCallback(() => {
    const appliedFilters = Object.entries(searchAestheticFilters)
      .filter(
        ([key, value]) =>
          key !== SearchAestheticFilterType.SEARCH_TEXT &&
          value &&
          Array.isArray(value) &&
          value.length > 0,
      )
      .flatMap(([_, value]) => value as SearchAestheticFilterItem[])

    return (
      <View className="mb-3 gap-y-3">
        {/* Active Filters */}
        {appliedFilters.length > 0 && (
          <View className="flex flex-col gap-y-2">
            <Text size="body7" variant="subdued">
              {t('MES-706')} ({appliedFilters.length}):
            </Text>
            <View className="flex flex-row flex-wrap gap-2">
              {appliedFilters.map((filter: SearchAestheticFilterItem) => (
                <SelectedFilterBadge
                  key={`${filter.type}-${filter.id}`}
                  filterItem={filter}
                  onClearFilter={(filterItem) => {
                    toggleSearchAestheticFilter(
                      filterItem.type as SearchAestheticFilterType,
                      filterItem as SearchAestheticFilterItem,
                    )
                  }}
                />
              ))}
              <TouchableOpacity
                onPress={() => clearAllSearchAestheticFilters()}
                className="flex flex-row items-center gap-x-1"
              >
                <Text size="body8" variant="error">
                  {t('MES-709')}
                </Text>
                <View className="-ml-1">
                  <CloseIconDanger width={20} height={20} />
                </View>
              </TouchableOpacity>
            </View>
          </View>
        )}
      </View>
    )
  }, [t, searchAestheticFilters, toggleSearchAestheticFilter, clearAllSearchAestheticFilters])

  const renderEmptyComponent = useCallback(() => {
    if (isGetFavoriteAestheticsError) {
      return (
        <View className="items-center py-8">
          <Text size="body6" className="text-red-500">
            {t('MES-197')}
          </Text>
        </View>
      )
    }

    return (
      <View className="flex flex-col items-center justify-center gap-y-2 py-8">
        <EmptyBoxIcon />
        <Text size="body6" variant="default">
          {t('MES-967')}
        </Text>
      </View>
    )
  }, [isGetFavoriteAestheticsError, t])

  const keyExtractor = useCallback((item: ListItem, index: number) => {
    if (item.type === 'aesthetic') {
      return `aesthetic-${item.aesthetic.id}-${index}`
    }
    return `${item.type}-${index}`
  }, [])

  useEffect(() => {
    return () => {
      resetAllAestheticFilters()
    }
  }, [resetAllAestheticFilters])
  const inset = useSafeAreaInsets()
  return (
    <FlatList
      data={data}
      renderItem={renderItem}
      keyExtractor={keyExtractor}
      ListHeaderComponent={renderHeaderComponent}
      onEndReached={handleLoadMore}
      onEndReachedThreshold={0.3}
      ListEmptyComponent={renderEmptyComponent}
      showsVerticalScrollIndicator={true}
      removeClippedSubviews={false}
      maxToRenderPerBatch={21}
      windowSize={21}
      initialNumToRender={21}
      contentContainerStyle={{ paddingBottom: inset.bottom + 12, paddingHorizontal: 16 }}
      ItemSeparatorComponent={Separator}
      refreshControl={
        <RefreshControl
          refreshing={refreshing}
          onRefresh={handleRefresh}
          colors={[primary['500']]}
          tintColor={primary['500']}
          progressBackgroundColor="#FFFFFF"
        />
      }
      className="flex-1"
    />
  )
}
type ListItem =
  | { type: 'aesthetic'; aesthetic: Aesthetic }
  | { type: 'loading' }
  | { type: 'loading_skeleton' }

const Separator = memo(() => {
  return <View className="h-2" />
})
Separator.displayName = 'Separator'
