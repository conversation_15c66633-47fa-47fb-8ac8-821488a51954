import { Skeleton } from '@/components/ui/Skeleton/Skeleton'
import { Text } from '@/components/ui/Text/Text'
import { useAppLanguage } from '@/hooks/common/useAppLanguage'
import { StyledExpoImage } from '@/libs/styled'
import { APP_ROUTES, AppRoutesEnum } from '@/routes/appRoutes'
import { Media } from '@/types/media.type'
import { getMediaInfo } from '@/utils/media-cms'
import * as Haptics from 'expo-haptics'
import { LinearGradient } from 'expo-linear-gradient'
import { Link, LinkProps } from 'expo-router'
import { useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import { TouchableOpacity, View } from 'react-native'
import { SvgUri } from 'react-native-svg'
import { useGetAestheticTreatmentNeeds } from '../../hooks/query/useGetAestheticTreatmentNeeds'

// Color gradients for each treatment need item
const gradientColors: [string, string][] = [
  ['#57B331', '#9CF179'],
  ['#E71538', '#FC87A1'],
  ['#976BFF', '#C8B1FE'],
  ['#FF9727', '#FAC439'],
  ['#F45FAE', '#F99BCD'],
]

export const AestheticTreatmentNeedsSection = () => {
  const { t } = useTranslation()
  const { primaryLanguage } = useAppLanguage()
  const { aestheticTreatmentNeeds, isGetAestheticTreatmentNeedsLoading } =
    useGetAestheticTreatmentNeeds({
      params: {
        limit: 5,
        locale: primaryLanguage,
      },
      useQueryOptions: {
        staleTime: 5 * 60 * 1000,
      },
    })

  const treatmentNeeds = useMemo(
    () => aestheticTreatmentNeeds?.docs?.slice(0, 5) || [],
    [aestheticTreatmentNeeds],
  )

  return (
    <View className="flex flex-col gap-y-3">
      <View className="mb-1">
        <Text size="body3" variant="default">
          {t('MES-961')}
        </Text>
      </View>
      {isGetAestheticTreatmentNeedsLoading ? (
        <AestheticTreatmentNeedsLoading />
      ) : (
        <View className="flex flex-row flex-wrap gap-3">
          {treatmentNeeds.map((item, index) => {
            const { media } = getMediaInfo(item.icon)
            const { mimeType, url } = (media as Media) || {}
            // console.log('iconSource', iconSource)
            const colors = gradientColors[index] || gradientColors[0]

            return (
              <Link
                key={item.id}
                href={{
                  pathname: APP_ROUTES.AESTHETICS?.children?.[AppRoutesEnum.AESTHETICS_SEARCH].path,
                  params: {
                    treatmentNeeds: item.id,
                    customTitle: item.name,
                  },
                }}
                asChild
              >
                <TouchableOpacity
                  className="flex-1"
                  style={{
                    minWidth: '47%',
                    height: 80,
                  }}
                  onPress={() => {
                    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light)
                  }}
                >
                  <LinearGradient
                    colors={colors}
                    start={{ x: 0, y: 1 }}
                    end={{ x: 1, y: 0 }}
                    className="flex items-center justify-center rounded-lg"
                    style={{
                      borderRadius: 8,
                      flex: 1,
                      width: '100%',
                      height: '100%',
                      paddingVertical: 12,
                      paddingHorizontal: 16,
                    }}
                  >
                    <View className="flex h-full flex-row items-center gap-x-4">
                      {url && (
                        <>
                          {mimeType === 'image/svg+xml' ? (
                            <SvgUri width="32" height="32" uri={url} />
                          ) : (
                            <View className="h-8 w-8 shrink-0">
                              <StyledExpoImage
                                source={url}
                                className="h-full w-full"
                                contentFit="contain"
                              />
                            </View>
                          )}
                        </>
                      )}
                      <View className="flex-1">
                        <Text size="body10" variant="white" numberOfLines={2}>
                          {item.name}
                        </Text>
                      </View>
                    </View>
                  </LinearGradient>
                </TouchableOpacity>
              </Link>
            )
          })}
          {/* View All Button */}
          <Link
            href={
              APP_ROUTES.AESTHETICS?.children?.[AppRoutesEnum.AESTHETICS_TREATMENT_NEEDS]
                .path as LinkProps['href']
            }
            asChild
          >
            <TouchableOpacity
              className="flex h-[80px] flex-1 justify-center rounded-lg border border-dashed border-primary bg-primary-50  "
              style={{
                minWidth: '47%',
              }}
              onPress={() => {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light)
              }}
            >
              <View className="flex items-center justify-center">
                <Text size="body10" variant="primary">
                  {t('MES-476')}
                </Text>
              </View>
            </TouchableOpacity>
          </Link>
        </View>
      )}
    </View>
  )
}

const AestheticTreatmentNeedsLoading = () => {
  return (
    <View className="flex flex-col gap-y-3">
      <View className="flex flex-col gap-y-3">
        {Array.from({ length: 3 }).map((_, index) => (
          <View key={index} className="flex flex-row flex-wrap gap-3">
            {Array.from({ length: 2 }).map((_, index) => (
              <View key={index} className="flex flex-1 flex-row gap-3">
                <View style={{ minWidth: '47%', height: 80, width: '100%' }}>
                  <Skeleton className="h-full w-full " />
                </View>
              </View>
            ))}
          </View>
        ))}
      </View>
    </View>
  )
}
