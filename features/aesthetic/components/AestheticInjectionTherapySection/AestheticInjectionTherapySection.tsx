import { useAppLanguage } from '@/hooks/common/useAppLanguage'
import { APP_ROUTES, AppRoutesEnum } from '@/routes/appRoutes'
import React from 'react'
import { useTranslation } from 'react-i18next'
import { AestheticSpecialTypesEnum } from '../../enums'
import { useGetAesthetics } from '../../hooks/query/useGetAesthetics'
import { AestheticHorizontalSection } from '../AestheticHorizontalSection/AestheticHorizontalSection'

export const AestheticInjectionTherapySection = () => {
  const { t } = useTranslation()
  const { primaryLanguage } = useAppLanguage()
  const { aesthetics, isGetAestheticsLoading } = useGetAesthetics({
    params: {
      locale: primaryLanguage,
      specialTypes: [AestheticSpecialTypesEnum.AESTHETIC_INJECTION_THERAPY],
      select: {
        name: 'true',
        icon: 'true',
      },
      limit: 8,
    },
    useQueryOptions: {
      staleTime: 0,
    },
  })

  return (
    <AestheticHorizontalSection
      title={t('MES-939')}
      aesthetics={aesthetics?.docs}
      isLoading={isGetAestheticsLoading}
      seeAllLink={{
        pathname: APP_ROUTES.AESTHETICS?.children?.[AppRoutesEnum.AESTHETICS_SEARCH]?.path,
        params: {
          specialTypes: AestheticSpecialTypesEnum.AESTHETIC_INJECTION_THERAPY,
          customTitle: t('MES-939'),
        },
      }}
    />
  )
}
