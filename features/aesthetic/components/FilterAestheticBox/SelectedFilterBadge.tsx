import CloseIcon from '@/assets/icons/close-icon.svg'
import { Text } from '@/components/ui/Text/Text'
import { TouchableOpacity } from 'react-native'
import { SearchAestheticFilterItem } from '../../stores/AestheticStore'

interface SelectedFilterBadgeProps {
  filterItem: SearchAestheticFilterItem
  onClearFilter?: (item: SearchAestheticFilterItem) => void
}

export const SelectedFilterBadge = ({ filterItem, onClearFilter }: SelectedFilterBadgeProps) => {
  return (
    <TouchableOpacity
      onPress={() => onClearFilter?.(filterItem)}
      className="flex flex-row items-center gap-x-1 self-start rounded border border-primary p-1"
    >
      <Text size="body7" variant="primary">
        {filterItem.label}
      </Text>
      <CloseIcon width={18} height={18} />
    </TouchableOpacity>
  )
}
