import {
  BaseFilterFooter,
  type BaseFilterItem,
} from '@/components/Filter/BaseFilterFooter/BaseFilterFooter'
import { BottomSheetFooter, BottomSheetFooterProps } from '@gorhom/bottom-sheet'
import * as Haptics from 'expo-haptics'
import { forwardRef, useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import { View } from 'react-native'
import { useShallow } from 'zustand/react/shallow'
import { SearchAestheticFilterItem, SearchAestheticFilterType } from '../../stores/AestheticStore'
import { useAestheticStoreContext } from '../../contexts/AestheticStoreContext'

interface SearchAestheticFilterFooterProps extends BottomSheetFooterProps {
  closeSheet?: () => void
}

export const SearchAestheticFilterFooter = forwardRef<View, SearchAestheticFilterFooterProps>(
  ({ closeSheet, ...props }, ref) => {
    return (
      <BottomSheetFooter {...props}>
        <SearchAestheticFilterFooterContent ref={ref} closeSheet={closeSheet} {...props} />
      </BottomSheetFooter>
    )
  },
)

SearchAestheticFilterFooter.displayName = 'SearchAestheticFilterFooter'

interface SearchAestheticFilterFooterContentProps extends BottomSheetFooterProps {
  closeSheet?: () => void
}

const SearchAestheticFilterFooterContent = forwardRef<
  View,
  SearchAestheticFilterFooterContentProps
>(({ closeSheet }, ref) => {
  const { t } = useTranslation()
  const { useStore } = useAestheticStoreContext()
  const {
    tempSearchFilters,
    applyTempSearchFilters,
    clearAllTempSearchFilters,
    toggleTempSearchFilter,
  } = useStore(
    useShallow((state) => ({
      tempSearchFilters: state.tempSearchFilters,
      applyTempSearchFilters: state.applyTempSearchFilters,
      clearAllTempSearchFilters: state.clearAllTempSearchFilters,
      toggleTempSearchFilter: state.toggleTempSearchFilter,
    })),
  )

  const handleClearFilter = (filterItem: BaseFilterItem) => {
    const searchFilterItem = filterItem as SearchAestheticFilterItem

    if (searchFilterItem.type === SearchAestheticFilterType.SEARCH_TEXT) {
      return
    }

    const currentFilters =
      (tempSearchFilters[searchFilterItem.type as keyof typeof tempSearchFilters] as
        | SearchAestheticFilterItem[]
        | null) || []
    const isSelected = currentFilters.some(
      (f: SearchAestheticFilterItem) => f.id === searchFilterItem.id,
    )

    if (isSelected) {
      toggleTempSearchFilter(searchFilterItem.type, searchFilterItem)
    }
  }

  const handleApplyFilters = () => {
    applyTempSearchFilters()
    closeSheet?.()
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light)
  }

  const handleResetFilters = () => {
    clearAllTempSearchFilters()
  }

  const activeFilters = useMemo(() => {
    const filters: SearchAestheticFilterItem[] = []

    // Add all filter types
    Object.values(SearchAestheticFilterType).forEach((filterType) => {
      if (filterType === SearchAestheticFilterType.SEARCH_TEXT) return

      const filterValue = tempSearchFilters[filterType as keyof typeof tempSearchFilters]
      if (filterValue && Array.isArray(filterValue)) {
        filters.push(...filterValue)
      }
    })

    return filters
  }, [tempSearchFilters])

  return (
    <BaseFilterFooter
      ref={ref}
      activeFilters={activeFilters}
      onClearFilter={handleClearFilter}
      maxDisplayCount={5}
      onApply={handleApplyFilters}
      onReset={handleResetFilters}
      applyText={t('MES-281')}
      resetText={t('MES-105')}
      selectedFiltersLabel={t('MES-706')}
      showLessText={t('MES-493')}
    />
  )
})

SearchAestheticFilterFooterContent.displayName = 'SearchAestheticFilterFooterContent'
