import { useSheetActions } from '@/contexts/SheetContext/SheetContext'
import { useAppLanguage } from '@/hooks/common/useAppLanguage'
import { createElement, useCallback, useEffect, useMemo, useRef } from 'react'
import { useTranslation } from 'react-i18next'
import { View } from 'react-native'
import { useShallow } from 'zustand/react/shallow'

import {
  BaseFilterSheetBox,
  type BaseFilterSheetBoxRef,
} from '@/components/Filter/BaseFilterSheetBox/BaseFilterSheetBox'
import { BottomSheetScrollView } from '@gorhom/bottom-sheet'
import {
  AESTHETIC_BODY_AREA_OPTIONS,
  AESTHETIC_BODY_PARTS_OPTIONS,
  AESTHETIC_EFFECTIVENESS_OPTIONS,
  AESTHETIC_INTERVENTION_LEVELS_OPTIONS,
  AESTHETIC_RECOVERY_TIME_OPTIONS,
} from '../../constants'
import {
  AestheticStoreProvider,
  useAestheticStoreContext,
} from '../../contexts/AestheticStoreContext'
import { useGetInfiniteAestheticTreatmentNeeds } from '../../hooks/query/useGetInfiniteAestheticTreatmentNeeds'
import { SearchAestheticFilterItem, SearchAestheticFilterType } from '../../stores/AestheticStore'

import { BaseFilterListSection } from '@/components/Filter/BaseFilterListSection/BaseFilterListSection'
import { BaseFilterNotesBox } from '@/components/Filter/BaseFilterNotesBox/BaseFilterNotesBox'
import { BaseSearchableFilterSection } from '@/components/Filter/BaseSearchableFilterSection/BaseSearchableFilterSection'
import { useSearchableFilterSection } from '@/components/Filter/BaseSearchableFilterSection/useSearchableFilterSection'
import { SearchableFilterListSection } from '@/components/Filter/SearchableFilterListSection/SearchableFilterListSection'
import { LocaleEnum } from '@/enums/locale.enum'
import { SearchAestheticFilterFooter } from './SearchAestheticFilterFooter'

const ALLOWED_FILTER_TYPES = [
  SearchAestheticFilterType.INTERVENTION_LEVEL,
  SearchAestheticFilterType.RECOVERY_TIME,
  SearchAestheticFilterType.EFFECTIVENESS,
  SearchAestheticFilterType.SPECIAL_TYPES,
  SearchAestheticFilterType.BODY_AREA,
  SearchAestheticFilterType.BODY_PARTS,
  SearchAestheticFilterType.TREATMENT_NEED,
]
interface FilterAestheticBoxProps {
  closeSheet?: () => void
  sharedFooterRef: React.RefObject<View | null>
  sharedSheetBoxRef?: React.RefObject<BaseFilterSheetBoxRef | null>
  allowedFilterTypes?: SearchAestheticFilterType[]
}

export const FilterAestheticBox = ({
  closeSheet,
  sharedFooterRef,
  sharedSheetBoxRef,
  allowedFilterTypes = ALLOWED_FILTER_TYPES,
}: FilterAestheticBoxProps) => {
  const { primaryLanguage } = useAppLanguage()
  const { t } = useTranslation()
  const localSheetBoxRef = useRef<BaseFilterSheetBoxRef>(null)
  const sheetBoxRef = sharedSheetBoxRef || localSheetBoxRef

  const { aestheticTreatmentNeeds, isGetAestheticTreatmentNeedsFetching } =
    useGetInfiniteAestheticTreatmentNeeds({
      params: { locale: primaryLanguage, pagination: false },
      config: {
        staleTime: 5 * 60 * 1000,
        enabled: Boolean(allowedFilterTypes.includes(SearchAestheticFilterType.TREATMENT_NEED)),
      },
    })

  const { useStore } = useAestheticStoreContext()
  const { toggleTempSearchFilter, initTempSearchFilters } = useStore(
    useShallow((state) => ({
      toggleTempSearchFilter: state.toggleTempSearchFilter,
      initTempSearchFilters: state.initTempSearchFilters,
      tempSearchFilters: state.tempSearchFilters,
    })),
  )

  // Initialize temp search filters when component mounts
  useEffect(() => {
    initTempSearchFilters()
  }, [initTempSearchFilters])

  const activeInterventionLevelIds = useStore(
    useShallow(
      (state) =>
        state.tempSearchFilters[SearchAestheticFilterType.INTERVENTION_LEVEL]?.map(
          (filter) => filter.id,
        ) || [],
    ),
  )

  const activeRecoveryTimeIds = useStore(
    useShallow(
      (state) =>
        state.tempSearchFilters[SearchAestheticFilterType.RECOVERY_TIME]?.map(
          (filter) => filter.id,
        ) || [],
    ),
  )

  const activeEffectivenessIds = useStore(
    useShallow(
      (state) =>
        state.tempSearchFilters[SearchAestheticFilterType.EFFECTIVENESS]?.map(
          (filter) => filter.id,
        ) || [],
    ),
  )

  const activeSpecialTypesIds = useStore(
    useShallow(
      (state) =>
        state.tempSearchFilters[SearchAestheticFilterType.SPECIAL_TYPES]?.map(
          (filter) => filter.id,
        ) || [],
    ),
  )

  const activeBodyAreaIds = useStore(
    useShallow(
      (state) =>
        state.tempSearchFilters[SearchAestheticFilterType.BODY_AREA]?.map((filter) => filter.id) ||
        [],
    ),
  )

  const activeBodyPartsIds = useStore(
    useShallow(
      (state) =>
        state.tempSearchFilters[SearchAestheticFilterType.BODY_PARTS]?.map((filter) => filter.id) ||
        [],
    ),
  )

  const activeTreatmentNeedIds = useStore(
    useShallow(
      (state) =>
        state.tempSearchFilters[SearchAestheticFilterType.TREATMENT_NEED]?.map(
          (filter) => filter.id,
        ) || [],
    ),
  )

  // Auto-remove body parts that don't belong to selected areas
  useEffect(() => {
    if (!activeBodyAreaIds || activeBodyAreaIds.length === 0) {
      return
    }

    const currentBodyParts =
      useStore.getState().tempSearchFilters[SearchAestheticFilterType.BODY_PARTS] || []

    if (currentBodyParts.length === 0) {
      return
    }

    // Get valid body part for selected areas
    const validBodyPartIds = Object.values(AESTHETIC_BODY_PARTS_OPTIONS)
      .filter((part) => activeBodyAreaIds.includes(part.area as string))
      .map((part) => part.value as string)

    const invalidBodyParts = currentBodyParts.filter((part) => !validBodyPartIds.includes(part.id))

    if (invalidBodyParts.length > 0) {
      invalidBodyParts.forEach((part) => {
        toggleTempSearchFilter(SearchAestheticFilterType.BODY_PARTS, part)
      })
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeBodyAreaIds, toggleTempSearchFilter])

  const handleToggleInterventionLevel = useCallback(
    (filterItem: SearchAestheticFilterItem) => {
      toggleTempSearchFilter(SearchAestheticFilterType.INTERVENTION_LEVEL, filterItem)
    },
    [toggleTempSearchFilter],
  )

  const handleToggleRecoveryTime = useCallback(
    (filterItem: SearchAestheticFilterItem) => {
      toggleTempSearchFilter(SearchAestheticFilterType.RECOVERY_TIME, filterItem)
    },
    [toggleTempSearchFilter],
  )

  const handleToggleEffectiveness = useCallback(
    (filterItem: SearchAestheticFilterItem) => {
      toggleTempSearchFilter(SearchAestheticFilterType.EFFECTIVENESS, filterItem)
    },
    [toggleTempSearchFilter],
  )

  // const handleToggleSpecialTypes = useCallback(
  //   (filterItem: SearchAestheticFilterItem) => {
  //     toggleTempSearchFilter(SearchAestheticFilterType.SPECIAL_TYPES, filterItem)
  //   },
  //   [toggleTempSearchFilter],
  // )

  const handleToggleBodyArea = useCallback(
    (filterItem: SearchAestheticFilterItem) => {
      toggleTempSearchFilter(SearchAestheticFilterType.BODY_AREA, filterItem)
    },
    [toggleTempSearchFilter],
  )

  const handleToggleBodyParts = useCallback(
    (filterItem: SearchAestheticFilterItem) => {
      toggleTempSearchFilter(SearchAestheticFilterType.BODY_PARTS, filterItem)
    },
    [toggleTempSearchFilter],
  )

  const handleToggleTreatmentNeed = useCallback(
    (filterItem: SearchAestheticFilterItem) => {
      toggleTempSearchFilter(SearchAestheticFilterType.TREATMENT_NEED, filterItem)
    },
    [toggleTempSearchFilter],
  )

  const interventionLevelData = useMemo(
    () =>
      Object.values(AESTHETIC_INTERVENTION_LEVELS_OPTIONS).map((item) => ({
        id: item.value,
        label: t(item.translationKey as string),
        type: SearchAestheticFilterType.INTERVENTION_LEVEL,
      })),
    [t],
  )

  const recoveryTimeData = useMemo(
    () =>
      Object.values(AESTHETIC_RECOVERY_TIME_OPTIONS).map((item) => ({
        id: item.value,
        label: t(item.translationKey as string),
        type: SearchAestheticFilterType.RECOVERY_TIME,
      })),
    [t],
  )

  const effectivenessData = useMemo(
    () =>
      Object.values(AESTHETIC_EFFECTIVENESS_OPTIONS).map((item) => ({
        id: item.value,
        label: t(item.translationKey as string),
        type: SearchAestheticFilterType.EFFECTIVENESS,
      })),
    [t],
  )

  // const specialTypesData = useMemo(
  //   () =>
  //     Object.values(AESTHETIC_SPECIAL_TYPES_OPTIONS).map((item) => ({
  //       id: item.value,
  //       label:
  //         primaryLanguage === LocaleEnum.VI ? item.fixedLabel.vi : item.fixedLabel.ja || item.label,
  //       type: SearchAestheticFilterType.SPECIAL_TYPES,
  //     })),
  //   [primaryLanguage],
  // )

  const bodyAreaData = useMemo(
    () =>
      Object.values(AESTHETIC_BODY_AREA_OPTIONS).map((item) => ({
        id: item.value,
        label:
          primaryLanguage === LocaleEnum.VI ? item.fixedLabel.vi : item.fixedLabel.ja || item.label,
        type: SearchAestheticFilterType.BODY_AREA,
      })),
    [primaryLanguage],
  )

  // Filter body parts based on selected body areas
  const bodyPartsData = useMemo(() => {
    const allBodyParts = Object.values(AESTHETIC_BODY_PARTS_OPTIONS).map((item) => ({
      id: item.value,
      label:
        primaryLanguage === LocaleEnum.VI ? item.fixedLabel.vi : item.fixedLabel.ja || item.label,
      type: SearchAestheticFilterType.BODY_PARTS,
      area: item.area,
    }))

    // If no body areas are selected, show all body parts
    if (!activeBodyAreaIds || activeBodyAreaIds.length === 0) {
      return allBodyParts.map(({ area: _area, ...rest }) => rest)
    }

    // Filter body parts that belong to selected areas
    return allBodyParts
      .filter((part) => activeBodyAreaIds.includes(part.area as string))
      .map(({ area: _area, ...rest }) => rest)
  }, [primaryLanguage, activeBodyAreaIds])

  const treatmentNeedData = useMemo(
    () =>
      aestheticTreatmentNeeds?.pages?.[0]?.docs?.map((item) => ({
        id: item.id,
        label: item.name,
        type: SearchAestheticFilterType.TREATMENT_NEED,
      })) || [],
    [aestheticTreatmentNeeds],
  )

  const treatmentNeedsFilter = useSearchableFilterSection({
    data: treatmentNeedData,
    limit: 6,
  })

  const bodyPartsFilter = useSearchableFilterSection({
    data: bodyPartsData,
    limit: 6,
  })

  // Configuration for all searchable filter sections
  const searchableFilters = useMemo(
    () => [
      {
        key: 'treatmentNeeds',
        filter: treatmentNeedsFilter,
        title: t('MES-961'),
        data: treatmentNeedsFilter.fullData,
        onSelectFilter: handleToggleTreatmentNeed,
        activeFilterIds: activeTreatmentNeedIds,
        isLoading: isGetAestheticTreatmentNeedsFetching,
      },
      {
        key: 'bodyParts',
        filter: bodyPartsFilter,
        title: t('MES-966'),
        data: bodyPartsFilter.fullData,
        onSelectFilter: handleToggleBodyParts,
        activeFilterIds: activeBodyPartsIds,
      },
    ],
    [
      treatmentNeedsFilter,
      bodyPartsFilter,
      t,
      handleToggleTreatmentNeed,
      handleToggleBodyParts,
      activeTreatmentNeedIds,
      activeBodyPartsIds,
      isGetAestheticTreatmentNeedsFetching,
    ],
  )

  // Find which searchable filter is currently active
  const activeSearchableFilter = useMemo(
    () => searchableFilters.find((item) => item.filter.showAll),
    [searchableFilters],
  )

  // Helper to render the active searchable filter section
  const renderSearchableFilter = () => {
    if (!activeSearchableFilter) return null

    const { filter, title, data, onSelectFilter, activeFilterIds, isLoading } =
      activeSearchableFilter

    return (
      <BaseSearchableFilterSection
        title={title}
        footerHeight={sheetBoxRef.current?.getFooterHeight() || 80}
        data={data}
        onBack={() => filter.setShowAll(false)}
        onSelectFilter={onSelectFilter}
        activeFilterIds={activeFilterIds}
        idKey="id"
        labelKey="label"
        isLoading={isLoading}
      />
    )
  }

  // Track active filters count to trigger footer height re-measurement
  const activeFiltersCount =
    activeInterventionLevelIds.length +
    activeRecoveryTimeIds.length +
    activeEffectivenessIds.length +
    activeSpecialTypesIds.length +
    activeBodyAreaIds.length +
    activeBodyPartsIds.length +
    activeTreatmentNeedIds.length

  // Trigger manual footer measurement when filters change
  useEffect(() => {
    const timer = setTimeout(() => {
      sheetBoxRef.current?.measureFooter()
    }, 100)

    return () => clearTimeout(timer)
  }, [activeFiltersCount, sheetBoxRef])

  const formattedInterventionLevelNotes = useMemo(() => {
    return Object.values(AESTHETIC_INTERVENTION_LEVELS_OPTIONS).map((item) => {
      const icon = item.notes.icon
      // Convert function component to React element if needed
      const iconElement =
        icon && typeof icon === 'function' ? createElement(icon, { width: 20, height: 20 }) : icon

      return {
        title: t(item.notes.title.translationKey),
        note: t(item.notes.note.translationKey),
        icon: iconElement,
      }
    })
  }, [t])

  return (
    <BaseFilterSheetBox
      ref={sheetBoxRef}
      title={t('MES-481')}
      onClose={closeSheet}
      footerRef={sharedFooterRef}
      enableFooterHeightMeasurement={true}
      footerHeightPadding={40}
      useBottomSheetScrollView={true}
    >
      {!activeSearchableFilter ? (
        <BottomSheetScrollView className="relative" showsVerticalScrollIndicator={false}>
          <View className="flex flex-col gap-y-3 px-4 pb-4">
            {allowedFilterTypes.includes(SearchAestheticFilterType.BODY_AREA) && (
              <BaseFilterListSection
                title={t('MES-965')}
                data={bodyAreaData}
                onSelectFilter={handleToggleBodyArea}
                activeFilters={activeBodyAreaIds}
                idKey="id"
                labelKey="label"
              />
            )}

            {allowedFilterTypes.includes(SearchAestheticFilterType.BODY_PARTS) && (
              <SearchableFilterListSection
                title={t('MES-966')}
                data={bodyPartsFilter.limitedData}
                onSelectFilter={handleToggleBodyParts}
                activeFilters={activeBodyPartsIds}
                idKey="id"
                labelKey="label"
                shouldShowViewAllButton={bodyPartsFilter.shouldShowViewAllButton}
                onViewAll={() => bodyPartsFilter.setShowAll(true)}
              />
            )}

            {allowedFilterTypes.includes(SearchAestheticFilterType.TREATMENT_NEED) && (
              <SearchableFilterListSection
                title={t('MES-961')}
                data={treatmentNeedsFilter.limitedData}
                onSelectFilter={handleToggleTreatmentNeed}
                activeFilters={activeTreatmentNeedIds}
                idKey="id"
                labelKey="label"
                isLoading={isGetAestheticTreatmentNeedsFetching}
                shouldShowViewAllButton={treatmentNeedsFilter.shouldShowViewAllButton}
                onViewAll={() => treatmentNeedsFilter.setShowAll(true)}
              />
            )}
            {allowedFilterTypes.includes(SearchAestheticFilterType.INTERVENTION_LEVEL) && (
              <BaseFilterListSection
                title={t('MES-962')}
                data={interventionLevelData}
                onSelectFilter={handleToggleInterventionLevel}
                activeFilters={activeInterventionLevelIds}
                idKey="id"
                labelKey="label"
              />
            )}
            {allowedFilterTypes.includes(SearchAestheticFilterType.RECOVERY_TIME) && (
              <BaseFilterListSection
                title={t('MES-963')}
                data={recoveryTimeData}
                onSelectFilter={handleToggleRecoveryTime}
                activeFilters={activeRecoveryTimeIds}
                idKey="id"
                labelKey="label"
              />
            )}
            {allowedFilterTypes.includes(SearchAestheticFilterType.EFFECTIVENESS) && (
              <BaseFilterListSection
                title={t('MES-964')}
                data={effectivenessData}
                onSelectFilter={handleToggleEffectiveness}
                activeFilters={activeEffectivenessIds}
                idKey="id"
                labelKey="label"
              />
            )}
            {/* {allowedFilterTypes.includes(SearchAestheticFilterType.SPECIAL_TYPES) && (
              <BaseFilterListSection
                title={t('MES-XXX')}
                data={specialTypesData}
                onSelectFilter={handleToggleSpecialTypes}
                activeFilters={activeSpecialTypesIds}
                idKey="id"
                labelKey="label"
              />
            )} */}

            <BaseFilterNotesBox title={t('MES-137')} notes={formattedInterventionLevelNotes} />
          </View>
        </BottomSheetScrollView>
      ) : (
        renderSearchableFilter()
      )}
    </BaseFilterSheetBox>
  )
}

export const useOpenFilterAestheticBox = () => {
  const { openCustomSheet, closeSheet } = useSheetActions()
  const { useStore } = useAestheticStoreContext()

  const sharedFooterRef = useRef<View>(null)
  const sharedSheetBoxRef = useRef<BaseFilterSheetBoxRef>(null)

  const handleOpenFilterAestheticBox = useCallback(
    (allowedFilterTypes?: SearchAestheticFilterType[]) => {
      openCustomSheet({
        children: ({ close }) => (
          <AestheticStoreProvider useStore={useStore}>
            <FilterAestheticBox
              closeSheet={close}
              sharedFooterRef={sharedFooterRef}
              sharedSheetBoxRef={sharedSheetBoxRef}
              allowedFilterTypes={allowedFilterTypes}
            />
          </AestheticStoreProvider>
        ),
        baseProps: {
          snapPoints: ['85%', '100%'],
          enableHandlePanningGesture: true,
          enableDynamicSizing: false,
          enableOverDrag: false,
        },
        options: {
          footerComponent: (props) => {
            return (
              <AestheticStoreProvider useStore={useStore}>
                <SearchAestheticFilterFooter
                  ref={sharedFooterRef}
                  closeSheet={closeSheet}
                  {...props}
                />
              </AestheticStoreProvider>
            )
          },
        },
      })
    },
    [openCustomSheet, closeSheet, useStore],
  )

  return { handleOpenFilterAestheticBox }
}
