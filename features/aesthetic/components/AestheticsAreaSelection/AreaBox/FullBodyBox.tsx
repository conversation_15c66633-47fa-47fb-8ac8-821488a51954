import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { StyleSheet, TouchableOpacity, View } from 'react-native'
import { Gesture, GestureDetector } from 'react-native-gesture-handler'
import Animated, {
  Easing,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated'
import Svg, { Path } from 'react-native-svg'

import { AestheticBodyPartsEnum, BodySideEnum } from '@/features/aesthetic/enums'
import * as Haptics from 'expo-haptics'
import { scheduleOnRN } from 'react-native-worklets'
import { BackFullBodyBox } from './BackFullBodyBox'
import { BaseBodyBoxRef } from './BaseBodyBox'
import { FrontFullBodyBox } from './FrontFullBodyBox'

interface FullBodyBoxProps {
  onSelectBodyPart?: (bodyPart: AestheticBodyPartsEnum | null) => void
  onBodySideChange?: (bodySide: BodySideEnum) => void
}

export interface FullBodyBoxRef {
  resetSelectedBodyPart: () => void
}

export const FullBodyBox = ({ onSelectBodyPart, onBodySideChange }: FullBodyBoxProps) => {
  const [bodySide, setBodySide] = useState<BodySideEnum>(BodySideEnum.FRONT)
  const [displayedSide, setDisplayedSide] = useState<BodySideEnum>(BodySideEnum.FRONT)
  const isAnimatingRef = useRef(false)
  const frontBodyBoxRef = useRef<BaseBodyBoxRef>(null)
  const backBodyBoxRef = useRef<BaseBodyBoxRef>(null)

  const frontOpacity = useSharedValue(1)
  const backOpacity = useSharedValue(0)

  useEffect(() => {
    onSelectBodyPart?.(null)
  }, [bodySide, onSelectBodyPart])

  useEffect(() => {
    if (bodySide === displayedSide) {
      isAnimatingRef.current = false
      return
    }

    isAnimatingRef.current = true

    if (bodySide === BodySideEnum.FRONT) {
      // Fade out back first
      if (displayedSide === BodySideEnum.BACK) {
        backOpacity.value = withTiming(0, {
          duration: 150,
          easing: Easing.in(Easing.ease),
        })
      }

      // After fade out, mount front and fade it in smoothly
      const timer1 = setTimeout(
        () => {
          setDisplayedSide(BodySideEnum.FRONT)
          frontOpacity.value = 0 // Start from 0 to allow BaseBodyBox animations
          // Fade in the container smoothly to reveal the animated content
          frontOpacity.value = withTiming(1, {
            duration: 300,
            easing: Easing.out(Easing.cubic),
          })
        },
        displayedSide === BodySideEnum.BACK ? 150 : 0,
      )

      const timer2 = setTimeout(() => {
        isAnimatingRef.current = false
      }, 400)

      return () => {
        clearTimeout(timer1)
        clearTimeout(timer2)
      }
    } else {
      // Fade out front first
      if (displayedSide === BodySideEnum.FRONT) {
        frontOpacity.value = withTiming(0, {
          duration: 150,
          easing: Easing.in(Easing.ease),
        })
      }

      // After fade out, mount back and fade it in smoothly
      const timer1 = setTimeout(
        () => {
          setDisplayedSide(BodySideEnum.BACK)
          backOpacity.value = 0 // Start from 0 to allow BaseBodyBox animations
          // Fade in the container smoothly to reveal the animated content
          backOpacity.value = withTiming(1, {
            duration: 300,
            easing: Easing.out(Easing.cubic),
          })
        },
        displayedSide === BodySideEnum.FRONT ? 150 : 0,
      )

      const timer2 = setTimeout(() => {
        isAnimatingRef.current = false
      }, 400)

      return () => {
        clearTimeout(timer1)
        clearTimeout(timer2)
      }
    }
  }, [bodySide, displayedSide, frontOpacity, backOpacity])

  const switchToBack = useCallback(() => {
    setBodySide((prev) => {
      if (prev === BodySideEnum.BACK) return prev
      return BodySideEnum.BACK
    })
    onBodySideChange?.(BodySideEnum.BACK)
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light)
    backBodyBoxRef.current?.resetSelectedBodyPart()
  }, [backBodyBoxRef, onBodySideChange])

  const switchToFront = useCallback(() => {
    setBodySide((prev) => {
      if (prev === BodySideEnum.FRONT) return prev
      return BodySideEnum.FRONT
    })
    onBodySideChange?.(BodySideEnum.FRONT)
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light)
    frontBodyBoxRef.current?.resetSelectedBodyPart()
  }, [frontBodyBoxRef, onBodySideChange])

  const toggleBodySide = useCallback(() => {
    // Allow toggle even during animation (will queue the change)
    setBodySide((prev) => {
      const newBodySide = prev === BodySideEnum.FRONT ? BodySideEnum.BACK : BodySideEnum.FRONT
      onBodySideChange?.(newBodySide)
      if (newBodySide === BodySideEnum.FRONT) {
        frontBodyBoxRef.current?.resetSelectedBodyPart()
      } else {
        backBodyBoxRef.current?.resetSelectedBodyPart()
      }
      return newBodySide
    })
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light)
  }, [frontBodyBoxRef, backBodyBoxRef, onBodySideChange])

  const panGesture = useMemo(
    () =>
      Gesture.Pan()
        .minDistance(30) // require real movement
        .failOffsetY([-10, 10]) // vertical movement fails (taps won't trigger)
        .activeOffsetX([-30, 30]) // ignore tiny horizontal motion
        .onEnd((event) => {
          const SWIPE_MIN_VELOCITY = 500
          const SWIPE_MIN_DISTANCE = 50

          const isSwipe =
            Math.abs(event.velocityX) > SWIPE_MIN_VELOCITY ||
            Math.abs(event.translationX) > SWIPE_MIN_DISTANCE

          if (!isSwipe) return

          if (event.translationX > 0) {
            scheduleOnRN(switchToFront)
          } else {
            scheduleOnRN(switchToBack)
          }
        }),
    [switchToFront, switchToBack],
  )

  // Animated styles for front view
  const frontAnimatedStyle = useAnimatedStyle(() => {
    return {
      opacity: frontOpacity.value,
    }
  })

  // Animated styles for back view
  const backAnimatedStyle = useAnimatedStyle(() => {
    return {
      opacity: backOpacity.value,
    }
  })

  return (
    <View>
      <GestureDetector gesture={panGesture}>
        <View style={styles.container}>
          {displayedSide === BodySideEnum.FRONT && (
            <Animated.View
              style={[
                displayedSide === BodySideEnum.FRONT
                  ? styles.bodyViewNormal
                  : styles.bodyViewAbsolute,
                frontAnimatedStyle,
              ]}
              pointerEvents={displayedSide === BodySideEnum.FRONT ? 'auto' : 'none'}
            >
              <FrontFullBodyBox ref={frontBodyBoxRef} onSelectBodyPart={onSelectBodyPart} />
            </Animated.View>
          )}

          {displayedSide === BodySideEnum.BACK && (
            <Animated.View
              style={[
                displayedSide === BodySideEnum.BACK
                  ? styles.bodyViewNormal
                  : styles.bodyViewAbsolute,
                backAnimatedStyle,
              ]}
              pointerEvents={displayedSide === BodySideEnum.BACK ? 'auto' : 'none'}
            >
              <BackFullBodyBox ref={backBodyBoxRef} onSelectBodyPart={onSelectBodyPart} />
            </Animated.View>
          )}
        </View>
      </GestureDetector>
      <TouchableOpacity
        className="flex flex-row items-center justify-center"
        style={{ gap: 100, marginTop: -40 }}
        onPress={toggleBodySide}
      >
        {/* Left arrow */}
        <Svg width={54} height={38} viewBox="0 0 54 38" fill="none">
          <Path
            opacity={bodySide === BodySideEnum.FRONT ? 1 : 0.3}
            fillRule="evenodd"
            clipRule="evenodd"
            d="M27.868 0C22.768 1.10638 12.9709 4.32097 11.0379 6.6377C9.10512 8.95445 7.97858 14.9355 20.2185 14.9355H30.9256V8.86133L53.8758 23.2334L30.9256 37.6055V31.5654C16.7251 31.682 -2.76428 26.0272 0.326942 11.6172C1.41741 6.53461 8.28487 0 27.868 0Z"
            fill={bodySide === BodySideEnum.FRONT ? '#BDBCBC' : '#999999'}
          />
        </Svg>

        {/* Right arrow */}
        <Svg width={54} height={38} viewBox="0 0 54 38" fill="none" transform="rotate(180 27 19)">
          <Path
            opacity={bodySide === BodySideEnum.BACK ? 1 : 0.3}
            fillRule="evenodd"
            clipRule="evenodd"
            d="M27.868 0C22.768 1.10638 12.9709 4.32097 11.0379 6.6377C9.10512 8.95445 7.97858 14.9355 20.2185 14.9355H30.9256V8.86133L53.8758 23.2334L30.9256 37.6055V31.5654C16.7251 31.682 -2.76428 26.0272 0.326942 11.6172C1.41741 6.53461 8.28487 0 27.868 0Z"
            fill={bodySide === BodySideEnum.BACK ? '#BDBCBC' : '#999999'}
          />
        </Svg>
      </TouchableOpacity>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    overflow: 'hidden',
  },
  bodyViewNormal: {
    width: '100%',
  },
  bodyViewAbsolute: {
    position: 'absolute',
    width: '100%',
    top: 0,
    left: 0,
  },
})
