import { Text } from '@/components/ui/Text/Text'
import { LocaleEnum } from '@/enums/locale.enum'
import { useAppLanguage } from '@/hooks/common/useAppLanguage'
import { coralPink } from '@/styles/_colors'
import { cn } from '@/utils/cn'
import { isTablet } from '@/utils/device'
import React, {
  createRef,
  memo,
  RefObject,
  useCallback,
  useEffect,
  useMemo,
  useReducer,
  useRef,
  useState,
} from 'react'

import { StyleSheet, TouchableOpacity, View } from 'react-native'
import { Gesture, GestureDetector } from 'react-native-gesture-handler'
import Animated, {
  Easing,
  useAnimatedProps,
  useAnimatedStyle,
  useSharedValue,
  withDelay,
  withSpring,
  withTiming,
} from 'react-native-reanimated'
import Svg, {
  Circle,
  ClipPath,
  Defs,
  FeDropShadow,
  FeGaussianBlur,
  Filter,
  G,
  Line,
  LinearGradient,
  Mask,
  Path,
  Rect,
  Stop,
  SvgProps,
} from 'react-native-svg'
import getBounds from 'svg-path-bounds'
import {
  AESTHETIC_BODY_PARTS_GROUP_OPTIONS,
  AESTHETIC_BODY_PARTS_OPTIONS,
} from '../../../constants'
import { AestheticBodyPartsEnum } from '../../../enums'
const SVG_VIEWBOX = { width: 454, height: 582 }

// Calculate bounding box from SVG path data using svg-path-bounds
const getPathBounds = (
  pathData: string,
): { x: number; y: number; width: number; height: number } => {
  try {
    const [left, top, right, bottom] = getBounds(pathData)
    return {
      x: left,
      y: top,
      width: right - left,
      height: bottom - top,
    }
  } catch {
    // Fallback to center if parsing fails
    return {
      x: SVG_VIEWBOX.width,
      y: SVG_VIEWBOX.height,
      width: 100,
      height: 100,
    }
  }
}

// Map body parts to SVG path data (only flat items)
const BODY_PART_PATHS: Partial<Record<AestheticBodyPartsEnum, string>> = {
  [AestheticBodyPartsEnum.HAIR]:
    'M226.58 131.957C226.58 131.957 200.286 100.02 165.159 117.991C130.032 136.064 129.416 89.6485 172.965 82.0495C216.515 74.4504 268.486 71.8832 292.932 90.6754C317.377 109.468 318.712 128.671 292.932 125.59C267.151 122.509 232.948 105.257 226.58 131.957Z',
  [AestheticBodyPartsEnum.SCAR]:
    'M188.254 178.454L186.144 180.55C186.047 180.647 185.931 180.696 185.797 180.696C185.663 180.696 185.547 180.647 185.45 180.55C185.353 180.452 185.304 180.337 185.304 180.203C185.304 180.069 185.353 179.953 185.45 179.856L187.546 177.746L185.475 177.21C185.35 177.164 185.25 177.083 185.177 176.966C185.104 176.848 185.09 176.726 185.135 176.6C185.18 176.474 185.261 176.373 185.379 176.295C185.497 176.217 185.619 176.2 185.744 176.246L187.796 176.783L187.241 174.725C187.196 174.599 187.212 174.478 187.29 174.36C187.367 174.242 187.469 174.16 187.594 174.116C187.72 174.07 187.842 174.084 187.96 174.158C188.078 174.231 188.159 174.33 188.204 174.456L188.766 176.527L193.036 172.256L190.985 171.7C190.859 171.655 190.757 171.577 190.68 171.466C190.603 171.354 190.586 171.229 190.63 171.091C190.675 170.965 190.757 170.862 190.875 170.783C190.993 170.703 191.115 170.686 191.241 170.731L193.287 171.287L192.731 169.234C192.686 169.108 192.704 168.988 192.783 168.872C192.862 168.756 192.965 168.675 193.091 168.631C193.217 168.586 193.339 168.602 193.456 168.68C193.573 168.757 193.655 168.858 193.7 168.984L194.256 171.036L198.527 166.766L196.456 166.204C196.33 166.158 196.231 166.077 196.158 165.959C196.085 165.841 196.071 165.72 196.116 165.594C196.161 165.468 196.242 165.367 196.36 165.289C196.478 165.211 196.6 165.194 196.725 165.24L198.783 165.796L198.241 163.744C198.196 163.618 198.213 163.497 198.292 163.379C198.372 163.261 198.475 163.179 198.6 163.134C198.726 163.089 198.848 163.103 198.966 163.177C199.083 163.25 199.165 163.349 199.21 163.475L199.746 165.546L201.856 163.45C201.953 163.352 202.069 163.304 202.203 163.304C202.337 163.304 202.453 163.352 202.55 163.45C202.647 163.547 202.696 163.663 202.696 163.797C202.696 163.931 202.647 164.046 202.55 164.144L200.454 166.254L202.525 166.791C202.651 166.835 202.75 166.917 202.823 167.035C202.896 167.153 202.911 167.274 202.866 167.4C202.821 167.526 202.739 167.627 202.621 167.705C202.503 167.782 202.381 167.798 202.256 167.754L200.204 167.217L200.759 169.275C200.804 169.401 200.788 169.523 200.71 169.641C200.633 169.758 200.531 169.839 200.406 169.885C200.28 169.929 200.158 169.915 200.041 169.842C199.923 169.768 199.841 169.669 199.796 169.544L199.235 167.473L194.964 171.744L197.016 172.3C197.141 172.344 197.243 172.423 197.32 172.535C197.398 172.646 197.415 172.771 197.37 172.91C197.325 173.035 197.243 173.137 197.125 173.217C197.007 173.296 196.885 173.313 196.76 173.269L194.714 172.714L195.269 174.766C195.314 174.891 195.296 175.012 195.217 175.128C195.138 175.244 195.035 175.324 194.909 175.369C194.783 175.413 194.661 175.397 194.544 175.32C194.427 175.242 194.345 175.141 194.3 175.016L193.744 172.964L189.473 177.234L191.544 177.796C191.67 177.841 191.769 177.923 191.842 178.041C191.915 178.158 191.93 178.28 191.885 178.406C191.84 178.531 191.758 178.633 191.641 178.711C191.523 178.788 191.401 178.804 191.275 178.76L189.217 178.204L189.76 180.256C189.805 180.381 189.787 180.503 189.708 180.621C189.628 180.739 189.525 180.82 189.4 180.866C189.274 180.91 189.152 180.896 189.034 180.823C188.917 180.749 188.835 180.65 188.79 180.525L188.254 178.454Z',
  [AestheticBodyPartsEnum.SKIN]:
    'M155.475 311.75 C157.532 311.75 159.2 313.418 159.2 315.474 C159.2 317.53 157.532 319.196 155.475 319.196 C153.417 319.196 151.75 317.529 151.75 315.474 C151.75 313.418 153.417 311.75 155.475 311.75Z',
  [AestheticBodyPartsEnum.EYEBROW]:
    'M323.335 240.295C321.075 236.906 319.021 231.875 307.62 227.87C297.965 224.481 282.558 225.816 272.904 227.356C268.795 228.075 262.324 229.513 257.497 230.95C255.545 231.566 245.685 234.339 250.204 237.933C252.053 239.474 256.059 238.755 261.297 237.42C269.925 235.263 278.963 233.723 290.159 233.312C296.63 233.107 302.792 233.415 309.366 235.263C317.583 237.522 323.437 240.398 323.437 240.398L323.335 240.295Z',
  [AestheticBodyPartsEnum.EYES]:
    'M140.406 269.152C158.484 254.159 178.82 257.034 195.459 271C195.459 271 184.778 286.095 166.906 284.042C146.364 281.68 140.406 269.152 140.406 269.152Z',
  [AestheticBodyPartsEnum.NOSE]:
    'M226.581 320.188C229.191 320.188 231.306 318.073 231.306 315.464C231.306 312.855 229.191 310.74 226.581 310.74C223.972 310.74 221.857 312.855 221.857 315.464C221.857 318.073 223.972 320.188 226.581 320.188Z',
  [AestheticBodyPartsEnum.LIPS]:
    'M193.919 363.522C193.919 363.522 209.736 358.284 216.31 352.123C216.31 352.123 220.11 353.047 226.581 353.047C233.051 353.047 236.852 352.123 236.852 352.123C243.425 358.284 259.243 363.522 259.243 363.522',
  [AestheticBodyPartsEnum.MOUTH]:
    'M259.762 363.399C259.762 363.399 247.128 385.682 227.1 385.682C207.071 385.682 194.438 363.399 194.438 363.399C194.438 363.399 210.255 358.161 216.829 352C216.829 352 220.629 352.924 227.1 352.924C233.57 352.924 237.371 352 237.371 352C243.944 358.161 259.762 363.399 259.762 363.399Z',
  [AestheticBodyPartsEnum.CHIN]:
    'M209.953 395.078C220.139 392.537 236.548 392.879 246.354 395.971C255.735 402.276 254.621 406.909 246.354 411.164C242.034 413.387 229.522 414.744 212.004 411.164C198.616 408.428 199.792 397.613 209.953 395.078Z',
  [AestheticBodyPartsEnum.JAW]:
    'M283.701 385.002C299.458 376.905 307.019 364.923 310.788 353.776C323.461 340.338 332.609 350.656 327.495 367.393C325.816 372.891 313.015 396.139 292.974 402.406C274.724 408.112 270.671 391.698 283.701 385.002Z',
  [AestheticBodyPartsEnum.NECK]:
    'M212.818 520.228C208.812 517.559 204.395 512.321 194.124 512.732C183.956 513.143 150.677 511.397 133.422 509.651',
  [AestheticBodyPartsEnum.EYELIDS]:
    'M318.632 257.143C315.022 253.244 299.594 246 284.629 246C269.665 246 255.734 256.429 251.632 261',
  [AestheticBodyPartsEnum.EYE_BAGS]:
    'M252.063 287.937C273.07 306.534 314.071 299.042 316.885 283.883C285.071 295.542 273.111 290.075 255.602 280.723C252.45 278.776 247.869 282.256 252.063 287.937Z',
}

const AnimatedG = Animated.createAnimatedComponent(G)
const AnimatedSvg = Animated.createAnimatedComponent(Svg)

interface AnimatedOptionItemProps {
  part: {
    value: AestheticBodyPartsEnum
    displayLabel: string
  }
  index: number
  totalCount: number
  side: 'left' | 'right'
  isSelected: boolean
  onPress: () => void
  buttonRef: RefObject<View | null>
  isGroup?: boolean
  isGroupSelected?: boolean
  spacing?: 'mb-2' | 'mb-3'
}

const AnimatedOptionItem = memo(
  ({
    part,
    index,
    totalCount,
    side,
    isSelected,
    onPress,
    buttonRef,
    isGroup = false,
    isGroupSelected = false,
    spacing = 'mb-3',
  }: AnimatedOptionItemProps) => {
    const baseXDistance = 90 - index * 10
    const minXDistance = 55
    const xDistance = Math.max(baseXDistance, minXDistance)

    // Dynamic Y-offset distribution based on total count
    // Distribute items evenly from top (-80) to bottom (80)
    const topPosition = -80
    const bottomPosition = 80
    const range = bottomPosition - topPosition

    // Calculate Y-offset: first item at top, last item at bottom, others evenly spaced
    let yOffset: number
    if (totalCount === 1) {
      yOffset = 0 // Center if only one item
    } else {
      // Distribute evenly: index 0 at top, index (totalCount-1) at bottom
      yOffset = topPosition + (index / (totalCount - 1)) * range
    }

    const scaleStart = 0.5 + index * 0.02

    const initialX = side === 'left' ? -xDistance : xDistance
    const initialY = yOffset
    const initialScale = scaleStart

    const itemOpacity = useSharedValue(0)
    const itemScale = useSharedValue(initialScale)
    const itemTranslateX = useSharedValue(initialX)
    const itemTranslateY = useSharedValue(initialY)

    useEffect(() => {
      const delay = 200 + index * 60

      itemOpacity.value = withDelay(
        delay,
        withSpring(1, {
          damping: 18,
          stiffness: 200,
          mass: 0.7,
        }),
      )
      itemScale.value = withDelay(
        delay,
        withSpring(1, {
          damping: 16,
          stiffness: 220,
          mass: 0.6,
        }),
      )
      itemTranslateX.value = withDelay(
        delay,
        withSpring(0, {
          damping: 20,
          stiffness: 180,
          mass: 0.8,
        }),
      )
      itemTranslateY.value = withDelay(
        delay,
        withSpring(0, {
          damping: 20,
          stiffness: 180,
          mass: 0.8,
        }),
      )
    }, [index, side, itemOpacity, itemScale, itemTranslateX, itemTranslateY])

    const itemAnimatedStyle = useAnimatedStyle(() => {
      return {
        opacity: itemOpacity.value,
        transform: [
          { translateX: itemTranslateX.value },
          { translateY: itemTranslateY.value },
          { scale: itemScale.value },
        ],
      }
    })

    const selected = isGroup ? isGroupSelected : isSelected

    return (
      <View ref={buttonRef}>
        <Animated.View style={itemAnimatedStyle}>
          <TouchableOpacity
            onPress={onPress}
            className={cn(
              `${spacing} w-fit rounded-lg border-[1.2px] border-custom-coral-pink-500 px-3 py-2`,
              side === 'left' ? 'self-start' : 'self-end',
              selected ? ' bg-custom-coral-pink-500' : ' bg-white',
            )}
          >
            <Text size="body6" variant={selected ? 'white' : 'default'}>
              {part.displayLabel}
            </Text>
          </TouchableOpacity>
        </Animated.View>
      </View>
    )
  },
)

AnimatedOptionItem.displayName = 'AnimatedOptionItem'

interface FrontFaceBoxProps extends SvgProps {
  onSelectBodyPart?: (bodyPart: AestheticBodyPartsEnum | null) => void
}

export const FrontFaceBox = memo(({ onSelectBodyPart, ...props }: FrontFaceBoxProps) => {
  const [internalSelectedBodyPart, setInternalSelectedBodyPart] =
    useState<AestheticBodyPartsEnum | null>(null)
  const [selectedGroup, setSelectedGroup] = useState<AestheticBodyPartsEnum | null>(null)

  // Use external prop if provided, otherwise use internal state
  const selectedBodyPart = internalSelectedBodyPart

  const [containerSize, setContainerSize] = useState({
    width: 0,
    height: 0,
  })

  // Zoom state for child items
  const scale = useSharedValue(1)
  const translateX = useSharedValue(0)
  const translateY = useSharedValue(0)
  const savedScale = useSharedValue(1)
  const savedTranslateX = useSharedValue(0)
  const savedTranslateY = useSharedValue(0)

  // Initial animation values
  const svgOpacity = useSharedValue(0)
  const leftListOpacity = useSharedValue(0)
  const rightListOpacity = useSharedValue(0)
  const leftListTranslateX = useSharedValue(-50)
  const rightListTranslateX = useSharedValue(50)
  const leftListTranslateY = useSharedValue(0)
  const rightListTranslateY = useSharedValue(0)

  // Store button positions (absolute coordinates relative to wrapper)
  const buttonRefs = useRef<
    Record<string, { x: number; y: number; width: number; height: number }>
  >({})
  const buttonElementRefs = useRef<Record<string, RefObject<View | null>>>({})
  const wrapperRef = useRef<View>(null)
  const leftContainerRef = useRef<View>(null)
  const rightContainerRef = useRef<View>(null)
  const leftContainerHeight = useRef<number>(0)
  const rightContainerHeight = useRef<number>(0)

  const { primaryLanguage } = useAppLanguage()

  // Get flat body parts (no children)
  const flatBodyParts = useMemo(() => {
    const allValues = [
      AestheticBodyPartsEnum.HAIR,
      AestheticBodyPartsEnum.SCAR,
      AestheticBodyPartsEnum.SKIN,
      AestheticBodyPartsEnum.EYEBROW,
      AestheticBodyPartsEnum.NOSE,
      AestheticBodyPartsEnum.LIPS,
      AestheticBodyPartsEnum.MOUTH,
      AestheticBodyPartsEnum.CHIN,
      AestheticBodyPartsEnum.JAW,
      AestheticBodyPartsEnum.NECK,
    ]

    return allValues
      .map(
        (value) => AESTHETIC_BODY_PARTS_OPTIONS[value as keyof typeof AESTHETIC_BODY_PARTS_OPTIONS],
      )
      .filter((part): part is NonNullable<typeof part> => part !== null && part !== undefined)
      .map((part) => ({
        ...part,
        displayLabel:
          primaryLanguage === LocaleEnum.VI
            ? part.fixedLabel[LocaleEnum.VI] || part.label
            : part.fixedLabel[LocaleEnum.JA] || part.label,
      }))
  }, [primaryLanguage])

  // Get group body parts (with children)
  const groupBodyParts = useMemo(() => {
    const allGroupValues = [
      AestheticBodyPartsEnum.CHIN_AND_JAW,
      AestheticBodyPartsEnum.MOUTH_AND_LIPS,
      AestheticBodyPartsEnum.EYES,
    ]

    return allGroupValues
      .map(
        (value) =>
          AESTHETIC_BODY_PARTS_GROUP_OPTIONS[
            value as keyof typeof AESTHETIC_BODY_PARTS_GROUP_OPTIONS
          ],
      )
      .filter((part): part is NonNullable<typeof part> => part !== null && part !== undefined)
      .map((part) => {
        return {
          ...part,
          displayLabel:
            primaryLanguage === LocaleEnum.VI
              ? part.fixedLabel[LocaleEnum.VI] || part.label
              : part.fixedLabel[LocaleEnum.JA] || part.label,
          // Process children if they exist
          children: part.children
            ? part.children.map((child) => ({
                ...child,
                displayLabel:
                  primaryLanguage === LocaleEnum.VI
                    ? child.fixedLabel[LocaleEnum.VI] || child.label
                    : child.fixedLabel[LocaleEnum.JA] || child.label,
              }))
            : undefined,
        }
      })
  }, [primaryLanguage])

  // Order: Left side (flat items first, then groups), Right side (EYES first, then flat items, then other groups)
  const headBodyParts = useMemo(() => {
    // Split flat list into left and right
    const leftFlat = flatBodyParts.filter((part) => {
      const leftValues = [
        AestheticBodyPartsEnum.HAIR,
        AestheticBodyPartsEnum.SCAR,
        AestheticBodyPartsEnum.SKIN,
      ]
      return leftValues.includes(part.value)
    })
    const rightFlat = flatBodyParts.filter((part) => {
      const rightValues = [AestheticBodyPartsEnum.EYEBROW, AestheticBodyPartsEnum.NOSE]
      return rightValues.includes(part.value)
    })

    // Split group list into left and right
    const leftGroups = groupBodyParts.filter((part) => {
      return part.value === AestheticBodyPartsEnum.CHIN_AND_JAW
    })
    const rightGroups = groupBodyParts.filter((part) => {
      return (
        part.value === AestheticBodyPartsEnum.MOUTH_AND_LIPS ||
        part.value === AestheticBodyPartsEnum.EYES
      )
    })

    const eyesGroup = rightGroups.find((part) => part.value === AestheticBodyPartsEnum.EYES)
    const otherRightGroups = rightGroups.filter(
      (part) => part.value !== AestheticBodyPartsEnum.EYES,
    )

    return {
      left: [...leftFlat, ...leftGroups],
      right: eyesGroup
        ? [eyesGroup, ...rightFlat, ...otherRightGroups]
        : [...rightFlat, ...otherRightGroups],
    }
  }, [flatBodyParts, groupBodyParts])

  // Calculate container height based on device type
  const containerHeight = useMemo(() => {
    return isTablet(false) ? 500 : 400
  }, [])

  // Calculate SVG scale to fit container
  const svgScale = useMemo(() => {
    if (containerSize.width === 0 || containerSize.height === 0) return 1
    const scaleX = containerSize.width / SVG_VIEWBOX.width
    const scaleY = containerSize.height / SVG_VIEWBOX.height
    return Math.min(scaleX, scaleY)
  }, [containerSize])

  // Check if a body part is a group (exists in group options)
  const isGroup = useCallback((bodyPart: AestheticBodyPartsEnum) => {
    return (
      bodyPart === AestheticBodyPartsEnum.CHIN_AND_JAW ||
      bodyPart === AestheticBodyPartsEnum.MOUTH_AND_LIPS ||
      bodyPart === AestheticBodyPartsEnum.EYES
    )
  }, [])

  // Check if a body part is a child of the selected group
  const isChildOfSelectedGroup = useCallback(
    (bodyPart: AestheticBodyPartsEnum) => {
      if (!selectedGroup) return false
      const groupPart =
        AESTHETIC_BODY_PARTS_GROUP_OPTIONS[
          selectedGroup as keyof typeof AESTHETIC_BODY_PARTS_GROUP_OPTIONS
        ]
      return groupPart?.children?.some((child) => child.value === bodyPart) ?? false
    },
    [selectedGroup],
  )

  // Auto-select first child when group is selected
  useEffect(() => {
    if (selectedGroup) {
      const groupPart =
        AESTHETIC_BODY_PARTS_GROUP_OPTIONS[
          selectedGroup as keyof typeof AESTHETIC_BODY_PARTS_GROUP_OPTIONS
        ]
      if (groupPart?.children && groupPart.children.length > 0) {
        // Auto-select first child
        const firstChildValue = groupPart.children[0].value
        setInternalSelectedBodyPart(firstChildValue)
        onSelectBodyPart?.(firstChildValue)
      }
    }
  }, [selectedGroup, onSelectBodyPart])

  // Auto-zoom and focus to selected child item (only when in group view)
  useEffect(() => {
    // Only zoom if we have a selected body part, it's not a group, and we're in group view
    if (selectedBodyPart && !isGroup(selectedBodyPart) && selectedGroup) {
      const pathData = BODY_PART_PATHS[selectedBodyPart]
      if (pathData && containerSize.width > 0 && containerSize.height > 0) {
        const bounds = getPathBounds(pathData)
        const centerX = bounds.x + bounds.width / 2
        const centerY = bounds.y + bounds.height / 2

        // Calculate effective scale: 0.4 zoom added to base scale of 1
        const zoomScale = 0.4
        const effectiveScale = 1 + zoomScale

        // Calculate target translation to center the path
        const targetX = (SVG_VIEWBOX.width / 2 - centerX) * svgScale * effectiveScale
        const targetY = (SVG_VIEWBOX.height / 2 - centerY) * svgScale * effectiveScale

        scale.value = withSpring(effectiveScale, {
          damping: 24,
          stiffness: 200,
        })
        translateX.value = withSpring(targetX, {
          damping: 24,
          stiffness: 200,
        })
        translateY.value = withSpring(targetY, {
          damping: 24,
          stiffness: 200,
        })

        savedScale.value = effectiveScale
        savedTranslateX.value = targetX
        savedTranslateY.value = targetY
      }
    } else if (!selectedBodyPart || !selectedGroup) {
      // Reset to original position when no child is selected or group is closed
      scale.value = withSpring(1, {
        damping: 60,
        stiffness: 400,
      })
      translateX.value = withSpring(0, {
        damping: 60,
        stiffness: 400,
      })
      translateY.value = withSpring(0, {
        damping: 60,
        stiffness: 400,
      })

      savedScale.value = 1
      savedTranslateX.value = 0
      savedTranslateY.value = 0
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedBodyPart, selectedGroup, svgScale, containerSize, isGroup])

  // Helper to determine which side a body part belongs to
  const getBodyPartSide = useCallback(
    (bodyPart: AestheticBodyPartsEnum): 'left' | 'right' | null => {
      const leftValues = [
        AestheticBodyPartsEnum.HAIR,
        AestheticBodyPartsEnum.SCAR,
        AestheticBodyPartsEnum.SKIN,
        AestheticBodyPartsEnum.CHIN_AND_JAW,
      ]
      const rightValues = [
        AestheticBodyPartsEnum.EYES,
        AestheticBodyPartsEnum.EYEBROW,
        AestheticBodyPartsEnum.NOSE,
        AestheticBodyPartsEnum.MOUTH_AND_LIPS,
      ]

      // Check if it's a child of a group
      if (selectedGroup) {
        const groupPart =
          AESTHETIC_BODY_PARTS_GROUP_OPTIONS[
            selectedGroup as keyof typeof AESTHETIC_BODY_PARTS_GROUP_OPTIONS
          ]
        if (groupPart?.children?.some((child) => child.value === bodyPart)) {
          // Return the side of the parent group
          return leftValues.includes(selectedGroup) ? 'left' : 'right'
        }
      }

      if (leftValues.includes(bodyPart)) return 'left'
      if (rightValues.includes(bodyPart)) return 'right'
      return null
    },
    [selectedGroup],
  )

  // Calculate line coordinates from button to path
  const getLineCoordinates = useCallback(
    (bodyPart: AestheticBodyPartsEnum) => {
      // Don't show line for groups
      if (isGroup(bodyPart)) return null

      const buttonPos = buttonRefs.current[bodyPart]
      if (!buttonPos) return null

      const pathData = BODY_PART_PATHS[bodyPart]
      if (!pathData) return null

      const bounds = getPathBounds(pathData)
      const pathCenterX = bounds.x + bounds.width / 2
      const pathCenterY = bounds.y + bounds.height / 2

      // Convert button position to SVG coordinates
      const buttonCenterX = buttonPos.x + buttonPos.width / 2
      let buttonCenterY = buttonPos.y + buttonPos.height / 2

      // Account for translateY: '-50%' transform
      // The transform moves the container up by 50% of its height
      const side = getBodyPartSide(bodyPart)
      if (side === 'left' && leftContainerHeight.current > 0) {
        buttonCenterY -= leftContainerHeight.current * 0.5
      } else if (side === 'right' && rightContainerHeight.current > 0) {
        buttonCenterY -= rightContainerHeight.current * 0.5
      }

      // Convert path center to screen coordinates
      const containerCenterX = containerSize.width / 2
      const containerCenterY = containerSize.height / 2
      const pathScreenX = containerCenterX + (pathCenterX - SVG_VIEWBOX.width / 2) * svgScale
      const pathScreenY = containerCenterY + (pathCenterY - SVG_VIEWBOX.height / 2) * svgScale

      return {
        x1: buttonCenterX,
        y1: buttonCenterY,
        x2: pathScreenX,
        y2: pathScreenY,
      }
    },
    [containerSize, svgScale, isGroup, getBodyPartSide],
  )

  const onLayout = (event: any) => {
    const { width, height } = event.nativeEvent.layout
    setContainerSize({ width, height })
  }

  // Pinch gesture handler for manual zoom (only when in group view)
  const pinchGesture = Gesture.Pinch()
    .enabled(!!selectedGroup)
    .onUpdate((e) => {
      scale.value = savedScale.value * e.scale
    })
    .onEnd(() => {
      savedScale.value = scale.value
      // Clamp scale between 1 and 3
      if (scale.value < 1) {
        scale.value = withSpring(1, {
          damping: 25,
          stiffness: 120,
        })
        savedScale.value = 1
      } else if (scale.value > 3) {
        scale.value = withSpring(3, {
          damping: 25,
          stiffness: 120,
        })
        savedScale.value = 3
      }
    })

  // Initial animation on mount
  useEffect(() => {
    // Animate SVG fade-in with smooth easing
    svgOpacity.value = withTiming(1, {
      duration: 800,
      easing: Easing.inOut(Easing.ease),
    })

    // Animate lists sliding in and fading in with spring for natural feel
    setTimeout(() => {
      // Left list animates first
      leftListTranslateX.value = withSpring(0, {
        damping: 35,
        stiffness: 150,
        mass: 1.1,
      })
      leftListOpacity.value = withTiming(1, {
        duration: 500,
        easing: Easing.out(Easing.cubic),
      })

      // Right list animates slightly after left for staggered effect
      setTimeout(() => {
        rightListTranslateX.value = withSpring(0, {
          damping: 35,
          stiffness: 150,
          mass: 1.1,
        })
        rightListOpacity.value = withTiming(1, {
          duration: 500,
          easing: Easing.out(Easing.cubic),
        })
      }, 50) // 50ms delay between left and right
    }, 200) // Start list animation 200ms after SVG animation starts
  }, [svgOpacity, leftListTranslateX, rightListTranslateX, leftListOpacity, rightListOpacity])

  // Animated style for SVG fade-in
  const svgAnimatedStyle = useAnimatedStyle(() => {
    return {
      opacity: svgOpacity.value,
    }
  })

  // Animated style for left list slide-in and fade-in
  const leftListAnimatedStyle = useAnimatedStyle(() => {
    return {
      opacity: leftListOpacity.value,
      transform: [
        { translateY: leftListTranslateY.value },
        { translateX: leftListTranslateX.value },
      ],
    }
  })

  // Animated style for right list slide-in and fade-in
  const rightListAnimatedStyle = useAnimatedStyle(() => {
    return {
      opacity: rightListOpacity.value,
      transform: [
        { translateY: rightListTranslateY.value },
        { translateX: rightListTranslateX.value },
      ],
    }
  })

  const [, forceUpdate] = useReducer((x) => x + 1, 0)

  const renderFocusBox = useCallback(() => {
    const pathData = BODY_PART_PATHS[selectedBodyPart as AestheticBodyPartsEnum]
    if (!pathData) return null

    const bounds = getPathBounds(pathData)
    const padding = 20

    // Get center
    const cx = bounds.x + bounds.width / 2
    const cy = bounds.y + bounds.height / 2

    // Compute radius
    const radius = Math.max(bounds.width, bounds.height) / 2 + padding

    return <Circle cx={cx} cy={cy} r={radius} fill="none" stroke={coralPink[500]} strokeWidth={3} />
  }, [selectedBodyPart])

  const renderOverlay = useCallback(() => {
    const pathData = BODY_PART_PATHS[selectedBodyPart as AestheticBodyPartsEnum]
    if (!pathData) return null

    const bounds = getPathBounds(pathData)
    const padding = 20

    // Get center
    const cx = bounds.x + bounds.width / 2
    const cy = bounds.y + bounds.height / 2

    // Compute radius
    const radius = Math.max(bounds.width, bounds.height) / 2 + padding

    const maskId = `overlayMask_${selectedBodyPart}`

    return (
      <>
        <Defs>
          <Mask id={maskId}>
            {/* White = visible, Black = hidden */}
            <Rect width={SVG_VIEWBOX.width} height={SVG_VIEWBOX.height} fill="white" />
            {/* Cut out the focus area */}
            <Circle cx={cx} cy={cy} r={radius} fill="black" />
          </Mask>
        </Defs>
        {/* Overlay that dims everything except the focus box area */}
        <Rect
          width={SVG_VIEWBOX.width}
          height={SVG_VIEWBOX.height}
          fill="rgba(255, 255, 255, 0.5)"
          mask={`url(#${maskId})`}
        />
      </>
    )
  }, [selectedBodyPart])

  // Render selection list for left or right side
  const renderSelectionList = useCallback(
    (side: 'left' | 'right', parts: typeof headBodyParts.left) => {
      // If a group is selected, only show its children
      if (selectedGroup) {
        const selectedPart = parts.find((part) => part.value === selectedGroup)
        if (
          selectedPart &&
          'children' in selectedPart &&
          selectedPart.children &&
          Array.isArray(selectedPart.children)
        ) {
          const children = selectedPart.children
          return (
            <React.Fragment>
              {/* Close button to exit group view */}
              <TouchableOpacity
                onPress={() => {
                  setSelectedGroup(null)
                  setInternalSelectedBodyPart(null)
                  onSelectBodyPart?.(null)
                }}
                className={cn(
                  'mb-6 h-8 w-8 items-center justify-center rounded-lg bg-custom-danger-100',
                  side === 'left' ? 'self-start' : 'self-end',
                )}
                hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
              >
                <Svg width={18} height={18} viewBox="0 0 18 18" fill="none" {...props}>
                  <Path
                    d="M4.50098 4.5L13.501 13.5"
                    stroke="#EF4444"
                    strokeWidth={1.5}
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                  <Path
                    d="M4.5 13.5L13.5 4.5"
                    stroke="#EF4444"
                    strokeWidth={1.5}
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </Svg>
              </TouchableOpacity>
              {children.map(
                (
                  child: {
                    value: AestheticBodyPartsEnum
                    label: string
                    displayLabel: string
                    fixedLabel: { [key in LocaleEnum]: string }
                  },
                  index: number,
                ) => {
                  return (
                    <AnimatedOptionItem
                      key={child.value}
                      part={child}
                      index={index}
                      totalCount={children.length}
                      side={side}
                      isSelected={internalSelectedBodyPart === child.value}
                      onPress={() => {
                        const newValue =
                          internalSelectedBodyPart === child.value ? null : child.value
                        setInternalSelectedBodyPart(newValue)
                        if (newValue) {
                          onSelectBodyPart?.(newValue)
                        } else {
                          onSelectBodyPart?.(null)
                        }
                      }}
                      buttonRef={buttonElementRefs.current[child.value]}
                      spacing="mb-2"
                    />
                  )
                },
              )}
            </React.Fragment>
          )
        }
        // If selected group is not on this side, hide everything
        return null
      }

      // No group selected - show all parts for this side
      return parts.map((part, index) => {
        const hasChildren = isGroup(part.value)

        return (
          <AnimatedOptionItem
            key={part.value}
            part={part}
            index={index}
            totalCount={parts.length}
            side={side}
            isSelected={hasChildren ? false : internalSelectedBodyPart === part.value}
            isGroup={hasChildren}
            isGroupSelected={hasChildren ? selectedGroup === part.value : false}
            onPress={() => {
              if (hasChildren) {
                // Group pressed - clear selected body part  to prevent flashing
                setInternalSelectedBodyPart(null)

                setSelectedGroup(selectedGroup === part.value ? null : part.value)
              } else {
                // Regular part pressed
                const newValue = internalSelectedBodyPart === part.value ? null : part.value
                setInternalSelectedBodyPart(newValue)
                setSelectedGroup(null)
                if (newValue) {
                  onSelectBodyPart?.(newValue)
                } else {
                  onSelectBodyPart?.(null)
                }
              }
            }}
            buttonRef={buttonElementRefs.current[part.value]}
          />
        )
      })
    },
    [
      selectedGroup,
      internalSelectedBodyPart,
      setInternalSelectedBodyPart,
      setSelectedGroup,
      isGroup,
      buttonElementRefs,
      props,
      headBodyParts,
      onSelectBodyPart,
    ],
  )

  // Initialize refs for buttons and  children
  useEffect(() => {
    const allParts = [...headBodyParts.left, ...headBodyParts.right]
    allParts.forEach((part) => {
      if (!buttonElementRefs.current[part.value]) {
        buttonElementRefs.current[part.value] = createRef<View>()
      }
      //  initialize refs for children
      if ('children' in part && part.children && Array.isArray(part.children)) {
        part.children.forEach((child: { value: AestheticBodyPartsEnum }) => {
          if (!buttonElementRefs.current[child.value]) {
            buttonElementRefs.current[child.value] = createRef<View>()
          }
        })
      }
    })
  }, [headBodyParts])

  // Measure button positions after layout
  useEffect(() => {
    if (!wrapperRef.current) return

    const allParts = [...headBodyParts.left, ...headBodyParts.right]
    allParts.forEach((part) => {
      // Measure parent button
      const buttonRef = buttonElementRefs.current[part.value]
      if (buttonRef?.current && wrapperRef.current) {
        buttonRef.current.measureLayout(
          wrapperRef.current as any,
          (x, y, width, height) => {
            buttonRefs.current[part.value] = { x, y, width, height }
            forceUpdate()
          },
          () => {
            // Fallback
          },
        )
      }

      // Measure children if group is selected
      if (
        selectedGroup === part.value &&
        'children' in part &&
        part.children &&
        Array.isArray(part.children)
      ) {
        part.children.forEach((child: { value: AestheticBodyPartsEnum }) => {
          const childButtonRef = buttonElementRefs.current[child.value]
          if (childButtonRef?.current && wrapperRef.current) {
            childButtonRef.current.measureLayout(
              wrapperRef.current as any,
              (x: number, y: number, width: number, height: number) => {
                buttonRefs.current[child.value] = { x, y, width, height }
                forceUpdate()
              },
              () => {
                // Fallback
              },
            )
          }
        })
      }
    })
  }, [headBodyParts, containerSize, selectedBodyPart, selectedGroup])

  //  animated props for SVG element
  const animatedProps = useAnimatedProps(() => {
    return {
      transform: [
        { translateX: translateX.value },
        { translateY: translateY.value },
        { scale: scale.value },
      ],
    }
  })
  return (
    <View ref={wrapperRef} style={styles.wrapper}>
      {selectedBodyPart &&
        !isGroup(selectedBodyPart) &&
        selectedGroup &&
        isChildOfSelectedGroup(selectedBodyPart) && (
          <View className="absolute inset-0 z-[-1] bg-[#FFFFFF80]" />
        )}
      <View
        style={[styles.container, { height: containerHeight, maxHeight: containerHeight }]}
        onLayout={onLayout}
      >
        <GestureDetector gesture={pinchGesture}>
          <Animated.View style={[styles.svgContainer]}>
            <AnimatedSvg
              animatedProps={[animatedProps, svgAnimatedStyle]}
              width={'100%'}
              height={'100%'}
              viewBox="0 0 454 582"
              fill="none"
              {...props}
            >
              <AnimatedG clipPath="url(#clip0_22239_222726)">
                <Path
                  d="M392.767 510.369C420.191 516.941 448.539 528.443 453.572 582.252H0C5.0328 528.443 33.4836 516.941 60.8046 510.369C61.8316 510.061 62.8586 509.753 63.9886 509.548C65.4266 509.24 66.9676 509.034 68.5086 508.726L68.8134 508.668C71.382 508.174 73.9551 507.68 76.6226 507.186C91.9266 504.311 106.203 501.641 118.015 499.279C140.714 494.76 160.332 482.746 164.954 473.401C177.177 448.55 170.911 396.691 170.911 396.691H282.661C282.661 396.691 276.395 448.447 288.618 473.401C293.137 482.848 312.858 494.76 335.557 499.279C346.501 501.468 359.561 503.921 373.593 506.556L376.949 507.186C379.62 507.699 382.393 508.213 385.064 508.726C386.498 508.932 388.035 509.239 389.572 509.546L389.583 509.548C390.61 509.753 391.74 510.061 392.767 510.369Z"
                  fill="#FFE1D6"
                />
                <Path
                  d="M392.767 510.369C420.191 516.941 448.539 528.443 453.572 582.252H0C5.0328 528.443 33.4836 516.941 60.8046 510.369C61.8316 510.061 62.8586 509.753 63.9886 509.548C65.4266 509.24 66.9676 509.034 68.5086 508.726L68.8134 508.668C71.382 508.174 73.9551 507.68 76.6226 507.186C91.9266 504.311 106.203 501.641 118.015 499.279C140.714 494.76 160.332 482.746 164.954 473.401C177.177 448.55 170.911 396.691 170.911 396.691H282.661C282.661 396.691 276.395 448.447 288.618 473.401C293.137 482.848 312.858 494.76 335.557 499.279C346.501 501.468 359.561 503.921 373.593 506.556L376.949 507.186C379.62 507.699 382.393 508.213 385.064 508.726C386.498 508.932 388.035 509.239 389.572 509.546L389.583 509.548C390.61 509.753 391.74 510.061 392.767 510.369Z"
                  stroke="#8E7D6E"
                  strokeWidth={2.61803}
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                <Path
                  d="M287.18 63.3597C287.899 60.4844 288.31 57.4037 288.31 54.2203C288.31 27.1102 271.157 0 226.58 0C182.004 0 164.851 27.1102 164.851 54.2203C164.851 81.3305 165.262 60.4844 165.981 63.3597C183.134 54.1176 203.162 49.0858 226.58 49.0858C249.999 49.0858 270.027 54.1176 287.18 63.3597Z"
                  fill="#A48E75"
                />
                <Path
                  d="M226.581 98.2735C256.675 98.2735 281.634 85.1291 287.18 63.3589C270.027 54.1168 249.999 49.085 226.581 49.085C203.162 49.085 183.134 54.1168 165.981 63.3589C171.425 85.1291 196.486 98.2735 226.581 98.2735Z"
                  fill="#C0B1A0"
                />
                <Path
                  d="M226.58 57.3018C117.091 57.3018 79.2929 132.574 84.4289 228.281C89.5639 322.55 148.109 399.259 226.58 399.259C305.052 399.259 363.7 322.55 368.732 228.281C373.868 132.574 336.07 57.3018 226.58 57.3018Z"
                  fill="#A48E75"
                />
                <Path
                  d="M340.487 260.114C375.717 251.796 363.392 327.17 331.448 327.17L340.487 260.114Z"
                  fill="#FFE1D6"
                  stroke="#8E7D6E"
                  strokeWidth={2.61803}
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                <Path
                  d="M112.777 260.114C77.5469 251.796 89.8718 327.17 121.815 327.17L112.777 260.114Z"
                  fill="#FFE1D6"
                  stroke="#8E7D6E"
                  strokeWidth={2.61803}
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                <Path
                  d="M226.581 429.142C241.166 429.142 258.216 425.445 287.488 399.259C318.815 371.122 329.086 349.352 334.735 320.496C339.049 298.725 346.547 225.713 346.547 225.713C349.526 185.664 340.692 100.842 226.581 100.842C112.469 100.842 103.533 185.664 106.614 225.713C106.614 225.713 114.112 298.623 118.426 320.496C124.075 349.352 134.346 371.122 165.673 399.259C194.843 425.445 211.996 429.142 226.581 429.142Z"
                  fill="#FFE1D6"
                  stroke="#8E7D6E"
                  strokeWidth={2.61803}
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                <Path
                  d="M188.254 178.454L186.144 180.55C186.047 180.647 185.931 180.696 185.797 180.696C185.663 180.696 185.547 180.647 185.45 180.55C185.353 180.452 185.304 180.337 185.304 180.203C185.304 180.069 185.353 179.953 185.45 179.856L187.546 177.746L185.475 177.21C185.35 177.164 185.25 177.083 185.177 176.966C185.104 176.848 185.09 176.726 185.135 176.6C185.18 176.474 185.261 176.373 185.379 176.295C185.497 176.217 185.619 176.2 185.744 176.246L187.796 176.783L187.241 174.725C187.196 174.599 187.212 174.478 187.29 174.36C187.367 174.242 187.469 174.16 187.594 174.116C187.72 174.07 187.842 174.084 187.96 174.158C188.078 174.231 188.159 174.33 188.204 174.456L188.766 176.527L193.036 172.256L190.985 171.7C190.859 171.655 190.757 171.577 190.68 171.466C190.603 171.354 190.586 171.229 190.63 171.091C190.675 170.965 190.757 170.862 190.875 170.783C190.993 170.703 191.115 170.686 191.241 170.731L193.287 171.287L192.731 169.234C192.686 169.108 192.704 168.988 192.783 168.872C192.862 168.756 192.965 168.675 193.091 168.631C193.217 168.586 193.339 168.602 193.456 168.68C193.573 168.757 193.655 168.858 193.7 168.984L194.256 171.036L198.527 166.766L196.456 166.204C196.33 166.158 196.231 166.077 196.158 165.959C196.085 165.841 196.071 165.72 196.116 165.594C196.161 165.468 196.242 165.367 196.36 165.289C196.478 165.211 196.6 165.194 196.725 165.24L198.783 165.796L198.241 163.744C198.196 163.618 198.213 163.497 198.292 163.379C198.372 163.261 198.475 163.179 198.6 163.134C198.726 163.089 198.848 163.103 198.966 163.177C199.083 163.25 199.165 163.349 199.21 163.475L199.746 165.546L201.856 163.45C201.953 163.352 202.069 163.304 202.203 163.304C202.337 163.304 202.453 163.352 202.55 163.45C202.647 163.547 202.696 163.663 202.696 163.797C202.696 163.931 202.647 164.046 202.55 164.144L200.454 166.254L202.525 166.791C202.651 166.835 202.75 166.917 202.823 167.035C202.896 167.153 202.911 167.274 202.866 167.4C202.821 167.526 202.739 167.627 202.621 167.705C202.503 167.782 202.381 167.798 202.256 167.754L200.204 167.217L200.759 169.275C200.804 169.401 200.788 169.523 200.71 169.641C200.633 169.758 200.531 169.839 200.406 169.885C200.28 169.929 200.158 169.915 200.041 169.842C199.923 169.768 199.841 169.669 199.796 169.544L199.235 167.473L194.964 171.744L197.016 172.3C197.141 172.344 197.243 172.423 197.32 172.535C197.398 172.646 197.415 172.771 197.37 172.91C197.325 173.035 197.243 173.137 197.125 173.217C197.007 173.296 196.885 173.313 196.76 173.269L194.714 172.714L195.269 174.766C195.314 174.891 195.296 175.012 195.217 175.128C195.138 175.244 195.035 175.324 194.909 175.369C194.783 175.413 194.661 175.397 194.544 175.32C194.427 175.242 194.345 175.141 194.3 175.016L193.744 172.964L189.473 177.234L191.544 177.796C191.67 177.841 191.769 177.923 191.842 178.041C191.915 178.158 191.93 178.28 191.885 178.406C191.84 178.531 191.758 178.633 191.641 178.711C191.523 178.788 191.401 178.804 191.275 178.76L189.217 178.204L189.76 180.256C189.805 180.381 189.787 180.503 189.708 180.621C189.628 180.739 189.525 180.82 189.4 180.866C189.274 180.91 189.152 180.896 189.034 180.823C188.917 180.749 188.835 180.65 188.79 180.525L188.254 178.454Z"
                  fill="#C0B1A0"
                />
                {selectedBodyPart === AestheticBodyPartsEnum.JAW && (
                  <Path
                    d="M283.701 385.002C299.458 376.905 307.019 364.923 310.788 353.776C323.461 340.338 332.609 350.656 327.495 367.393C325.816 372.891 313.015 396.139 292.974 402.406C274.724 408.112 270.671 391.698 283.701 385.002Z"
                    stroke="#F87171"
                    strokeWidth={3}
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeDasharray="6 6"
                  />
                )}
                {selectedBodyPart === AestheticBodyPartsEnum.CHIN && (
                  <Path
                    d="M209.953 395.078C220.139 392.537 236.548 392.879 246.354 395.971C255.735 402.276 254.621 406.909 246.354 411.164C242.034 413.387 229.522 414.744 212.004 411.164C198.616 408.428 199.792 397.613 209.953 395.078Z"
                    stroke="#F87171"
                    strokeWidth={3}
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeDasharray="6 6"
                  />
                )}

                {selectedBodyPart === AestheticBodyPartsEnum.EYE_BAGS && (
                  <Path
                    d="M252.063 287.937C273.07 306.534 314.071 299.042 316.885 283.883C285.071 295.542 273.111 290.075 255.602 280.723C252.45 278.776 247.869 282.256 252.063 287.937Z"
                    stroke="#F87171"
                    strokeWidth={3}
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeDasharray="6 6"
                  />
                )}

                <Path
                  d="M226.581 131.957C226.581 131.957 209.736 116.554 179.025 141.815C148.315 167.18 119.248 210.926 110.209 264.633C110.209 264.633 88.1258 223.557 102.506 171.801C116.988 120.148 157.97 99.4044 192.275 96.4264C226.581 93.3457 226.581 131.854 226.581 131.854V131.957Z"
                  fill="#A48E75"
                />
                <Path
                  d="M226.58 131.957C226.58 131.957 243.425 116.554 274.135 141.815C304.846 167.18 333.913 210.926 342.952 264.633C342.952 264.633 365.035 223.557 350.655 171.801C336.173 120.148 295.191 99.4044 260.886 96.4264C226.58 93.3457 226.58 131.854 226.58 131.854V131.957Z"
                  fill="#A48E75"
                />
                <Path
                  d="M226.58 131.957C226.58 131.957 200.286 100.02 165.159 117.991C130.032 136.064 129.416 89.6485 172.965 82.0495C216.515 74.4504 268.486 71.8832 292.932 90.6754C317.377 109.468 318.712 128.671 292.932 125.59C267.151 122.509 232.948 105.257 226.58 131.957Z"
                  fill="#A48E75"
                />
                <Path
                  d="M228.635 116.451C228.635 116.451 239.933 94.7832 264.276 107.106C264.276 107.106 253.183 95.2967 240.858 99.6097C228.43 103.923 228.738 116.451 228.738 116.451H228.635Z"
                  fill="#C0B1A0"
                />
                <Path
                  d="M228.635 116.452C228.635 116.452 224.321 102.384 208.812 101.357C195.767 100.535 188.988 108.032 188.988 108.032C188.988 108.032 211.482 99.0976 228.738 116.452H228.635Z"
                  fill="#C0B1A0"
                />
                <Path
                  d="M226.786 104.232C226.786 104.232 225.656 92.6276 215.796 90.2657C207.476 88.3146 202.135 93.6545 202.135 93.6545C202.135 93.6545 217.747 88.7254 226.786 104.232Z"
                  fill="#C0B1A0"
                />
                <Path
                  d="M259.346 363.522C259.346 363.522 246.712 385.805 226.684 385.805C206.655 385.805 194.022 363.522 194.022 363.522C194.022 363.522 209.839 358.284 216.413 352.123C216.413 352.123 220.213 353.047 226.684 353.047C233.154 353.047 236.955 352.123 236.955 352.123C243.528 358.284 259.346 363.522 259.346 363.522Z"
                  fill="#E6A6A4"
                />
                <Path
                  d="M193.919 363.522C193.919 363.522 209.736 358.284 216.31 352.123C216.31 352.123 220.11 353.047 226.581 353.047C233.051 353.047 236.852 352.123 236.852 352.123C243.425 358.284 259.243 363.522 259.243 363.522"
                  stroke="#C38380"
                  strokeWidth={2.61803}
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                <Path
                  d="M260.784 362.29C260.784 362.29 255.237 367.116 239.009 365.576C239.009 365.576 234.387 367.938 226.684 367.938C218.98 367.938 214.358 365.576 214.358 365.576C198.027 367.014 192.584 362.29 192.584 362.29"
                  stroke="#9C595A"
                  strokeWidth={2}
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                <Path
                  d="M210.147 380.569C214.769 383.547 220.316 385.807 226.684 385.807C233.052 385.807 238.495 383.65 243.22 380.569"
                  stroke="#C38380"
                  strokeWidth={2.61803}
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                <Path
                  d="M226.581 320.188C229.191 320.188 231.306 318.073 231.306 315.464C231.306 312.855 229.191 310.74 226.581 310.74C223.972 310.74 221.857 312.855 221.857 315.464C221.857 318.073 223.972 320.188 226.581 320.188Z"
                  fill="white"
                />
                <Path
                  d="M240.242 325.013C234.285 325.219 230.792 328.915 226.684 328.915C222.575 328.915 218.981 325.219 213.126 325.013C207.169 324.808 206.553 333.639 212.818 333.639H240.55C246.815 333.639 246.199 324.808 240.242 325.013Z"
                  fill="#D4B29B"
                />
                <Path
                  d="M212.715 327.479L220.008 331.073"
                  stroke="#8E7D6E"
                  strokeWidth={2.61803}
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                <Path
                  d="M240.55 327.479L233.257 331.073"
                  stroke="#8E7D6E"
                  strokeWidth={2.61803}
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                <Path
                  d="M215.283 328.71L209.531 331.072"
                  stroke="#8E7D6E"
                  strokeWidth={2.61803}
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                <Path
                  d="M237.982 328.71L243.734 331.072"
                  stroke="#8E7D6E"
                  strokeWidth={2.61803}
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                <Mask
                  id="mask0_22239_222726"
                  style={{
                    maskType: 'luminance',
                  }}
                  maskUnits="userSpaceOnUse"
                  x={249}
                  y={225}
                  width={75}
                  height={16}
                >
                  <Path
                    d="M323.335 240.295C321.075 236.906 319.021 231.875 307.62 227.87C297.965 224.481 282.558 225.816 272.904 227.356C268.795 228.075 262.324 229.513 257.497 230.95C255.545 231.566 245.685 234.339 250.204 237.933C252.053 239.474 256.059 238.755 261.297 237.42C269.925 235.263 278.963 233.723 290.159 233.312C296.63 233.107 302.792 233.415 309.366 235.263C317.583 237.522 323.437 240.398 323.437 240.398L323.335 240.295Z"
                    fill="url(#paint0_linear_22239_222726)"
                  />
                </Mask>
                <G mask="url(#mask0_22239_222726)">
                  <Path
                    d="M323.335 240.295C321.075 236.906 319.021 231.875 307.62 227.87C297.965 224.481 282.558 225.816 272.904 227.356C268.795 228.075 262.324 229.513 257.497 230.95C255.545 231.566 245.685 234.339 250.204 237.933C252.053 239.474 256.059 238.755 261.297 237.42C269.925 235.263 278.963 233.723 290.159 233.312C296.63 233.107 302.792 233.415 309.366 235.263C317.583 237.522 323.437 240.398 323.437 240.398L323.335 240.295Z"
                    fill="url(#paint1_linear_22239_222726)"
                  />
                </G>
                <Mask
                  id="mask1_22239_222726"
                  style={{
                    maskType: 'luminance',
                  }}
                  maskUnits="userSpaceOnUse"
                  x={129}
                  y={225}
                  width={76}
                  height={16}
                >
                  <Path
                    d="M129.93 240.295C132.19 236.906 134.244 231.875 145.645 227.87C155.3 224.481 170.706 225.816 180.361 227.356C184.47 228.075 190.94 229.513 195.768 230.95C197.719 231.566 207.579 234.339 203.06 237.933C201.109 239.474 197.206 238.755 191.967 237.42C183.34 235.263 174.301 233.723 163.106 233.312C156.635 233.107 150.472 233.415 143.899 235.263C135.682 237.522 129.827 240.398 129.827 240.398L129.93 240.295Z"
                    fill="url(#paint2_linear_22239_222726)"
                  />
                </Mask>
                <G mask="url(#mask1_22239_222726)">
                  <Path
                    d="M129.93 240.295C132.19 236.906 134.244 231.875 145.645 227.87C155.3 224.481 170.706 225.816 180.361 227.356C184.47 228.075 190.94 229.513 195.768 230.95C197.719 231.566 207.579 234.339 203.06 237.933C201.109 239.474 197.206 238.755 191.967 237.42C183.34 235.263 174.301 233.723 163.106 233.312C156.635 233.107 150.472 233.415 143.899 235.263C135.682 237.522 129.827 240.398 129.827 240.398L129.93 240.295Z"
                    fill="url(#paint3_linear_22239_222726)"
                  />
                </G>
                <Path
                  d="M312.755 269.152C294.678 254.159 274.341 257.034 257.702 271C257.702 271 268.384 286.095 286.256 284.042C306.798 281.68 312.755 269.152 312.755 269.152Z"
                  fill="white"
                />
                <Path
                  d="M273.52 260.422C270.439 262.476 270.541 266.687 270.644 269.973C270.644 273.669 271.671 277.674 274.65 280.139C278.45 283.322 285.434 283.425 289.748 281.577C296.219 278.804 297.246 270.383 295.705 263.092C294.576 257.958 288.413 256.828 284.099 257.444C279.477 258.061 276.704 258.266 273.52 260.422Z"
                  fill="#A88972"
                />
                <Path
                  d="M277.217 269.766C277.115 273.155 279.683 275.928 282.969 276.03C286.359 276.133 289.132 273.566 289.235 270.28C289.337 266.891 286.77 264.118 283.483 264.016C280.093 263.913 277.32 266.48 277.217 269.766Z"
                  fill="#786443"
                />
                <Path
                  d="M257.702 271C269.206 251.386 300.225 254.569 312.755 269.151"
                  stroke="#8E7D6E"
                  strokeWidth={5.23605}
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                <Path
                  d="M312.447 257.341C295.603 244.094 270.028 246.251 257.394 259.498C257.394 259.498 266.535 245.84 284.407 245.634C304.539 245.429 312.447 257.341 312.447 257.341Z"
                  fill="#D4B29B"
                />
                {selectedBodyPart === AestheticBodyPartsEnum.EYELIDS && (
                  <Path
                    d="M318.632 257.143C315.022 253.244 299.594 246 284.629 246C269.665 246 255.734 256.429 251.632 261"
                    stroke="#F87171"
                    strokeWidth={3}
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeDasharray="6 6"
                  />
                )}

                <Path
                  d="M309.879 266.995C314.399 270.178 320.561 267.508 323.129 262.99C323.129 262.99 314.809 267.2 303.922 260.73L309.879 266.995Z"
                  fill="#8E7D6E"
                />
                <Path
                  d="M140.406 269.152C158.484 254.159 178.82 257.034 195.459 271C195.459 271 184.778 286.095 166.906 284.042C146.364 281.68 140.406 269.152 140.406 269.152Z"
                  fill="white"
                />
                <Path
                  d="M179.642 260.422C182.724 262.476 182.621 266.687 182.518 269.973C182.518 273.669 181.491 277.674 178.513 280.139C174.712 283.322 167.728 283.425 163.414 281.577C156.943 278.804 155.916 270.383 157.457 263.092C158.587 257.958 164.749 256.828 169.063 257.444C173.685 258.061 176.458 258.266 179.642 260.422Z"
                  fill="#A88972"
                />
                <Path
                  d="M176.048 269.766C176.15 273.155 173.583 275.928 170.296 276.03C166.906 276.133 164.133 273.566 164.03 270.28C163.928 266.891 166.496 264.118 169.782 264.016C173.172 263.913 175.945 266.48 176.048 269.766Z"
                  fill="#786443"
                />
                <Path
                  d="M195.459 271C183.956 251.386 152.937 254.569 140.406 269.151"
                  stroke="#8E7D6E"
                  strokeWidth={5.23605}
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                <Path
                  d="M140.818 257.341C157.662 244.094 183.237 246.251 195.871 259.498C195.871 259.498 186.729 245.84 168.858 245.634C148.726 245.429 140.818 257.341 140.818 257.341Z"
                  fill="#D4B29B"
                />
                <Path
                  d="M143.385 266.995C138.866 270.178 132.703 267.508 130.135 262.99C130.135 262.99 138.455 267.2 149.342 260.73L143.385 266.995Z"
                  fill="#8E7D6E"
                />
                <Path
                  d="M212.818 520.228C208.812 517.559 204.395 512.321 194.124 512.732C183.956 513.143 150.677 511.397 133.422 509.651"
                  stroke="#D3BCA3"
                  strokeWidth={3}
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                <Path
                  d="M240.446 520.228C244.452 517.559 248.869 512.321 259.14 512.732C269.308 513.143 302.587 511.397 319.842 509.651"
                  stroke="#D3BCA3"
                  strokeWidth={3}
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                <Path
                  d="M259.762 363.399C259.762 363.399 247.128 385.682 227.1 385.682C207.071 385.682 194.438 363.399 194.438 363.399C194.438 363.399 210.255 358.161 216.829 352C216.829 352 220.629 352.924 227.1 352.924C233.57 352.924 237.371 352 237.371 352C243.944 358.161 259.762 363.399 259.762 363.399Z"
                  fill="#E6A6A4"
                />
                <Path
                  d="M196.548 363.377C200.948 363.777 211.715 361.21 216.548 359.877C226.148 363.877 234.548 361.544 237.548 359.877C245.148 362.677 254.715 363.377 258.548 363.377C252.148 363.377 242.882 366.044 239.048 367.377C229.848 372.177 220.882 370.044 217.548 368.377L196.548 363.377Z"
                  fill="#FCF3F3"
                />
                <Path
                  d="M194.335 363.399C194.335 363.399 210.152 358.161 216.726 352C216.726 352 220.526 352.924 226.997 352.924C233.467 352.924 237.268 352 237.268 352C243.841 358.161 259.659 363.399 259.659 363.399"
                  stroke="#C38380"
                  strokeWidth={2.61803}
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                <Path
                  d="M261.2 362.167C261.2 362.167 252.548 365.877 237.048 359.377C237.048 359.377 233.548 362.167 228.048 362.167C222.548 362.167 217.048 359.377 217.048 359.377C200.699 365.377 193 362.167 193 362.167"
                  stroke="#9C595A"
                  strokeWidth={2}
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                <Path
                  d="M261.249 363.931C261.249 363.931 248.048 362.877 236.548 368.377C231.289 370.893 222.048 370.377 217.548 368.377C209.048 365.377 193.048 363.931 193.048 363.931"
                  stroke="#9C595A"
                  strokeWidth={2}
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                <Path
                  d="M210.563 380.446C215.185 383.424 220.732 385.684 227.1 385.684C233.468 385.684 238.911 383.527 243.636 380.446"
                  stroke="#C38380"
                  strokeWidth={2.61803}
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                {selectedBodyPart === AestheticBodyPartsEnum.LIPS && (
                  <Path
                    d="M235.165 367.37C240.262 363.829 248.788 363.798 254.087 367.471C259.393 375.264 258.069 379.3 254.087 384.977C252.006 387.943 246.693 391.958 237.364 387.881C230.235 384.766 230.08 370.903 235.165 367.37Z"
                    stroke="white"
                    strokeWidth={3}
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeDasharray="6 6"
                  />
                )}

                {selectedBodyPart === AestheticBodyPartsEnum.MOUTH && (
                  <Path
                    d="M223.931 349.8C231.189 344.757 243.33 344.713 250.875 349.943C258.431 361.04 256.546 366.787 250.875 374.871C247.912 379.095 240.347 384.811 227.062 379.006C216.911 374.57 216.689 354.83 223.931 349.8Z"
                    stroke="white"
                    strokeWidth={3}
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeDasharray="6 6"
                  />
                )}

                <Path d="M155.475 311.75 C157.532 311.75 159.2 313.418 159.2 315.474 C159.2 317.53 157.532 319.196 155.475 319.196 C153.417 319.196 151.75 317.529 151.75 315.474 C151.75 313.418 153.417 311.75 155.475 311.75Z" />
              </AnimatedG>

              {/* Overlay - dims everything except focus box area */}
              {selectedBodyPart &&
                !isGroup(selectedBodyPart) &&
                selectedGroup &&
                isChildOfSelectedGroup(selectedBodyPart) &&
                renderOverlay()}
              {/* Focus box  */}
              {selectedBodyPart &&
                !isGroup(selectedBodyPart) &&
                selectedGroup &&
                isChildOfSelectedGroup(selectedBodyPart) &&
                renderFocusBox()}
              <Defs>
                <LinearGradient
                  id="paint0_linear_22239_222726"
                  x1={323.437}
                  y1={233.004}
                  x2={249.075}
                  y2={233.004}
                  gradientUnits="userSpaceOnUse"
                >
                  <Stop stopColor="white" />
                  <Stop offset={0.4} stopColor="#FDFDFD" />
                  <Stop offset={0.5} stopColor="#F6F6F6" />
                  <Stop offset={0.6} stopColor="#EAEAEA" />
                  <Stop offset={0.7} stopColor="#C3C3C3" />
                  <Stop offset={0.8} stopColor="#A8A8A8" />
                  <Stop offset={0.9} stopColor="#626262" />
                  <Stop offset={1} />
                </LinearGradient>
                <LinearGradient
                  id="paint1_linear_22239_222726"
                  x1={323.437}
                  y1={233.004}
                  x2={249.075}
                  y2={233.004}
                  gradientUnits="userSpaceOnUse"
                >
                  <Stop stopColor="#9F7E63" />
                  <Stop offset={0.9} stopColor="#9F7E63" />
                  <Stop offset={1} stopColor="#9F7E63" />
                </LinearGradient>
                <LinearGradient
                  id="paint2_linear_22239_222726"
                  x1={129.93}
                  y1={0.000310121}
                  x2={204.19}
                  y2={0.000310121}
                  gradientUnits="userSpaceOnUse"
                >
                  <Stop stopColor="white" />
                  <Stop offset={0.4} stopColor="#FDFDFD" />
                  <Stop offset={0.5} stopColor="#F6F6F6" />
                  <Stop offset={0.6} stopColor="#EAEAEA" />
                  <Stop offset={0.7} stopColor="#C3C3C3" />
                  <Stop offset={0.8} stopColor="#A8A8A8" />
                  <Stop offset={0.9} stopColor="#626262" />
                  <Stop offset={1} />
                </LinearGradient>
                <LinearGradient
                  id="paint3_linear_22239_222726"
                  x1={129.93}
                  y1={0.000310121}
                  x2={204.19}
                  y2={0.000310121}
                  gradientUnits="userSpaceOnUse"
                >
                  <Stop stopColor="#9F7E63" />
                  <Stop offset={0.9} stopColor="#9F7E63" />
                  <Stop offset={1} stopColor="#9F7E63" />
                </LinearGradient>
                <ClipPath id="clip0_22239_222726">
                  <Rect width={454} height={582} fill="white" />
                </ClipPath>
              </Defs>
            </AnimatedSvg>
          </Animated.View>
        </GestureDetector>
      </View>

      {/* Connection line  */}
      {selectedBodyPart &&
        (() => {
          if (selectedGroup) return null
          const coords = getLineCoordinates(selectedBodyPart)
          if (!coords) return null
          return (
            <Svg
              style={StyleSheet.absoluteFill}
              width={containerSize.width || '100%'}
              height={containerSize.height || '100%'}
              pointerEvents="none"
            >
              <Line
                x1={coords.x1}
                y1={coords.y1}
                x2={coords.x2}
                y2={coords.y2}
                stroke="#fdbfc8"
                strokeWidth={1.5}
                strokeDasharray="3 3"
              />
              <Defs>
                <Filter id="drop-shadow">
                  <FeDropShadow
                    dx="0"
                    dy="0"
                    stdDeviation="1"
                    floodColor="#EC043A"
                    floodOpacity={0.4}
                  />
                  <FeGaussianBlur stdDeviation="0.3" result="blurred" />
                </Filter>
              </Defs>

              <Circle
                cx={coords.x2}
                cy={coords.y2}
                r={6}
                fill="#fff"
                stroke="#fdbfc8"
                strokeWidth={2}
                filter="url(#drop-shadow)"
              />
            </Svg>
          )
        })()}

      {/* Body Parts Selection - Left side */}
      <Animated.View
        ref={leftContainerRef}
        className="absolute left-0 top-1/2 px-4 py-3"
        style={leftListAnimatedStyle}
        onLayout={(event) => {
          const { height } = event.nativeEvent.layout
          leftContainerHeight.current = height
          // Calculate translateY to center vertically: move up by half the height
          leftListTranslateY.value = -height / 2
          forceUpdate()
        }}
      >
        <View className="flex flex-col gap-2">
          {renderSelectionList('left', headBodyParts.left)}
        </View>
      </Animated.View>

      {/* Body Parts Selection - Right side */}
      <Animated.View
        ref={rightContainerRef}
        className="absolute right-0 top-1/2 px-4 py-3"
        style={rightListAnimatedStyle}
        onLayout={(event) => {
          const { height } = event.nativeEvent.layout
          rightContainerHeight.current = height
          // Calculate translateY to center vertically: move up by half the height
          rightListTranslateY.value = -height / 2
          forceUpdate()
        }}
      >
        <View className="flex flex-col justify-center gap-2">
          {renderSelectionList('right', headBodyParts.right)}
        </View>
      </Animated.View>
    </View>
  )
})

FrontFaceBox.displayName = 'FrontFaceBox'
const styles = StyleSheet.create({
  wrapper: {
    width: '100%',
    position: 'relative',
  },
  container: {
    width: '100%',
    height: 400,
    maxHeight: 400,
    overflow: 'hidden',
  },
  svgContainer: {
    width: '100%',
    height: '100%',
    overflow: 'hidden',
  },
})
