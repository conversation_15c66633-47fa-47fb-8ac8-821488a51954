import { Text } from '@/components/ui/Text/Text'
import { LocaleEnum } from '@/enums/locale.enum'
import { useAppLanguage } from '@/hooks/common/useAppLanguage'
import { lavender } from '@/styles/_colors'
import { cn } from '@/utils/cn'
import { isTablet } from '@/utils/device'
import React, {
  createRef,
  forwardRef,
  memo,
  RefObject,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useReducer,
  useRef,
  useState,
} from 'react'

import { StyleSheet, TouchableOpacity, View } from 'react-native'
import Animated, {
  Easing,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  withTiming,
} from 'react-native-reanimated'
import Svg, {
  Circle,
  Defs,
  FeDropShadow,
  FeGaussianBlur,
  Filter,
  Line,
  SvgProps,
} from 'react-native-svg'
import getBounds from 'svg-path-bounds'
import { AESTHETIC_BODY_PARTS_OPTIONS } from '../../../constants'
import { AestheticBodyAreaEnum, AestheticBodyPartsEnum, BodySideEnum } from '../../../enums'

// Calculate bounding box from SVG path data using svg-path-bounds
const getPathBounds = (
  pathData: string,
  svgViewBox: { width: number; height: number },
): { x: number; y: number; width: number; height: number } => {
  try {
    const [left, top, right, bottom] = getBounds(pathData)
    return {
      x: left,
      y: top,
      width: right - left,
      height: bottom - top,
    }
  } catch {
    // Fallback to center if parsing fails
    return {
      x: svgViewBox.width,
      y: svgViewBox.height,
      width: 100,
      height: 100,
    }
  }
}

interface BaseBodyBoxProps extends SvgProps {
  onSelectBodyPart?: (bodyPart: AestheticBodyPartsEnum | null) => void
  svgViewBox: { width: number; height: number }
  bodyPartPaths: Partial<Record<AestheticBodyPartsEnum, string>>
  bodySide: BodySideEnum
  leftBodyParts: AestheticBodyPartsEnum[]
  rightBodyParts: AestheticBodyPartsEnum[]
  renderSvgContent: (props: SvgProps) => React.ReactNode
}

export interface BaseBodyBoxRef {
  resetSelectedBodyPart: () => void
}

const BaseBodyBoxComponent = forwardRef<BaseBodyBoxRef, BaseBodyBoxProps>(
  (
    {
      onSelectBodyPart,
      svgViewBox,
      bodyPartPaths,
      bodySide,
      leftBodyParts,
      rightBodyParts,
      renderSvgContent,
      ...props
    },
    ref,
  ) => {
    const [internalSelectedBodyPart, setInternalSelectedBodyPart] =
      useState<AestheticBodyPartsEnum | null>(null)

    // Use external prop if provided, otherwise use internal state
    const selectedBodyPart = internalSelectedBodyPart

    useImperativeHandle(ref, () => ({
      resetSelectedBodyPart: () => {
        setInternalSelectedBodyPart(null)
        onSelectBodyPart?.(null)
      },
    }))

    const [containerSize, setContainerSize] = useState({
      width: 0,
      height: 0,
    })

    // Initial animation values
    const svgOpacity = useSharedValue(0)
    const leftListOpacity = useSharedValue(0)
    const rightListOpacity = useSharedValue(0)
    const leftListTranslateX = useSharedValue(-50)
    const rightListTranslateX = useSharedValue(50)
    const leftListTranslateY = useSharedValue(0)
    const rightListTranslateY = useSharedValue(0)

    // Store button positions (absolute coordinates relative to wrapper)
    const buttonRefs = useRef<
      Record<string, { x: number; y: number; width: number; height: number }>
    >({})
    const buttonElementRefs = useRef<Record<string, RefObject<View | null>>>({})
    const wrapperRef = useRef<View>(null)
    const leftContainerRef = useRef<View>(null)
    const rightContainerRef = useRef<View>(null)
    const leftContainerHeight = useRef<number>(0)
    const rightContainerHeight = useRef<number>(0)

    const { primaryLanguage } = useAppLanguage()

    // Get flat body parts (no children)
    const flatBodyParts = useMemo(() => {
      return Object.values(AESTHETIC_BODY_PARTS_OPTIONS)
        .filter(
          (part) =>
            part.area === AestheticBodyAreaEnum.FULL_BODY &&
            part.hasOwnProperty('side') &&
            part.side === bodySide,
        )
        .map((part) => ({
          ...part,
          displayLabel:
            primaryLanguage === LocaleEnum.VI
              ? part.fixedLabel[LocaleEnum.VI] || part.label
              : part.fixedLabel[LocaleEnum.JA] || part.label,
        }))
    }, [primaryLanguage, bodySide])

    // Split flat list into left and right
    const headBodyParts = useMemo(() => {
      const leftFlat = flatBodyParts.filter((part) => leftBodyParts.includes(part.value))
      const rightFlat = flatBodyParts.filter((part) => rightBodyParts.includes(part.value))

      return {
        left: [...leftFlat],
        right: [...rightFlat],
      }
    }, [flatBodyParts, leftBodyParts, rightBodyParts])

    // Calculate container height based on device type
    const containerHeight = useMemo(() => {
      return isTablet(false) ? 500 : 400
    }, [])

    // Calculate SVG scale to fit container
    const svgScale = useMemo(() => {
      if (containerSize.width === 0 || containerSize.height === 0) return 1
      const scaleX = containerSize.width / svgViewBox.width
      const scaleY = containerSize.height / svgViewBox.height
      return Math.min(scaleX, scaleY)
    }, [containerSize, svgViewBox])

    // Helper to determine which side a body part belongs to
    const getBodyPartSide = useCallback(
      (bodyPart: AestheticBodyPartsEnum): 'left' | 'right' | null => {
        if (leftBodyParts.includes(bodyPart)) return 'left'
        if (rightBodyParts.includes(bodyPart)) return 'right'
        return null
      },
      [leftBodyParts, rightBodyParts],
    )

    // Calculate line coordinates from button to path
    const getLineCoordinates = useCallback(
      (bodyPart: AestheticBodyPartsEnum) => {
        const buttonPos = buttonRefs.current[bodyPart]
        if (!buttonPos) return null

        const pathData = bodyPartPaths[bodyPart]
        if (!pathData) return null

        const bounds = getPathBounds(pathData, svgViewBox)
        const pathCenterX = bounds.x + bounds.width / 2
        const pathCenterY = bounds.y + bounds.height / 2

        // Convert button position to SVG coordinates
        const buttonCenterX = buttonPos.x + buttonPos.width / 2
        let buttonCenterY = buttonPos.y + buttonPos.height / 2

        // Account for translateY: '-50%' transform
        // The transform moves the container up by 50% of its height
        const side = getBodyPartSide(bodyPart)
        if (side === 'left' && leftContainerHeight.current > 0) {
          buttonCenterY -= leftContainerHeight.current * 0.5
        } else if (side === 'right' && rightContainerHeight.current > 0) {
          buttonCenterY -= rightContainerHeight.current * 0.5
        }

        // Convert path center to screen coordinates
        const containerCenterX = containerSize.width / 2
        const containerCenterY = containerSize.height / 2
        const pathScreenX = containerCenterX + (pathCenterX - svgViewBox.width / 2) * svgScale
        const pathScreenY = containerCenterY + (pathCenterY - svgViewBox.height / 2) * svgScale

        return {
          x1: buttonCenterX,
          y1: buttonCenterY,
          x2: pathScreenX,
          y2: pathScreenY,
        }
      },
      [containerSize, svgScale, getBodyPartSide, bodyPartPaths, svgViewBox],
    )

    const onLayout = (event: any) => {
      const { width, height } = event.nativeEvent.layout
      setContainerSize({ width, height })
    }

    // Initial animation on mount
    useEffect(() => {
      // Animate SVG fade-in with smooth easing
      svgOpacity.value = withTiming(1, {
        duration: 800,
        easing: Easing.inOut(Easing.ease),
      })

      // Animate lists sliding in and fading in with spring for natural feel
      setTimeout(() => {
        // Left list animates first
        leftListTranslateX.value = withSpring(0, {
          damping: 35,
          stiffness: 150,
          mass: 1.1,
        })
        leftListOpacity.value = withTiming(1, {
          duration: 500,
          easing: Easing.out(Easing.cubic),
        })

        // Right list animates slightly after left for staggered effect
        setTimeout(() => {
          rightListTranslateX.value = withSpring(0, {
            damping: 35,
            stiffness: 150,
            mass: 1.1,
          })
          rightListOpacity.value = withTiming(1, {
            duration: 500,
            easing: Easing.out(Easing.cubic),
          })
        }, 50) // 50ms delay between left and right
      }, 250) // Start list animation 250ms after SVG animation starts
    }, [svgOpacity, leftListTranslateX, rightListTranslateX, leftListOpacity, rightListOpacity])

    // Animated style for SVG fade-in
    const svgAnimatedStyle = useAnimatedStyle(() => {
      return {
        opacity: svgOpacity.value,
      }
    })

    // Animated style for left list slide-in and fade-in
    const leftListAnimatedStyle = useAnimatedStyle(() => {
      return {
        opacity: leftListOpacity.value,
        transform: [
          { translateY: leftListTranslateY.value },
          { translateX: leftListTranslateX.value },
        ],
      }
    })

    // Animated style for right list slide-in and fade-in
    const rightListAnimatedStyle = useAnimatedStyle(() => {
      return {
        opacity: rightListOpacity.value,
        transform: [
          { translateY: rightListTranslateY.value },
          { translateX: rightListTranslateX.value },
        ],
      }
    })

    const [, forceUpdate] = useReducer((x) => x + 1, 0)

    // Render selection list for left or right side
    const renderSelectionList = useCallback(
      (side: 'left' | 'right', parts: typeof headBodyParts.left) => {
        return parts.map((part) => {
          return (
            <View key={part.value} ref={buttonElementRefs.current[part.value]}>
              <TouchableOpacity
                onPress={() => {
                  const newValue = internalSelectedBodyPart === part.value ? null : part.value
                  setInternalSelectedBodyPart(newValue)
                  if (newValue) {
                    onSelectBodyPart?.(newValue)
                  } else {
                    onSelectBodyPart?.(null)
                  }
                }}
                className={cn(
                  'mb-3 w-fit rounded-lg border-[1.2px] border-custom-lavender-400 px-3 py-2',
                  side === 'left' ? 'self-start' : 'self-end',
                  internalSelectedBodyPart === part.value ? ' bg-custom-lavender-400' : ' bg-white',
                )}
              >
                <Text
                  size="body6"
                  variant={internalSelectedBodyPart === part.value ? 'white' : 'default'}
                >
                  {part.displayLabel}
                </Text>
              </TouchableOpacity>
            </View>
          )
        })
      },
      [
        internalSelectedBodyPart,
        setInternalSelectedBodyPart,
        buttonElementRefs,
        onSelectBodyPart,
        headBodyParts,
      ],
    )

    // Initialize refs for buttons
    useEffect(() => {
      const allParts = [...headBodyParts.left, ...headBodyParts.right]
      allParts.forEach((part) => {
        if (!buttonElementRefs.current[part.value]) {
          buttonElementRefs.current[part.value] = createRef<View>()
        }
      })
    }, [headBodyParts])

    // Measure button positions after layout
    useEffect(() => {
      if (!wrapperRef.current) return

      const allParts = [...headBodyParts.left, ...headBodyParts.right]
      allParts.forEach((part) => {
        const buttonRef = buttonElementRefs.current[part.value]
        if (buttonRef?.current && wrapperRef.current) {
          buttonRef.current.measureLayout(
            wrapperRef.current as any,
            (x, y, width, height) => {
              const oldPos = buttonRefs.current[part.value]
              const newPos = { x, y, width, height }
              // Only update if position actually changed
              if (
                !oldPos ||
                oldPos.x !== newPos.x ||
                oldPos.y !== newPos.y ||
                oldPos.width !== newPos.width ||
                oldPos.height !== newPos.height
              ) {
                buttonRefs.current[part.value] = newPos
                forceUpdate()
              }
            },
            () => {
              // Fallback
            },
          )
        }
      })
    }, [headBodyParts, containerSize])

    return (
      <View ref={wrapperRef} style={styles.wrapper}>
        <View
          style={[styles.container, { height: containerHeight, maxHeight: containerHeight }]}
          onLayout={onLayout}
        >
          <Animated.View
            style={[styles.svgContainer, svgAnimatedStyle]}
            renderToHardwareTextureAndroid={true}
            shouldRasterizeIOS={true}
          >
            <Svg
              width={'100%'}
              height={'100%'}
              viewBox={`0 0 ${svgViewBox.width} ${svgViewBox.height}`}
              fill="none"
              {...props}
            >
              {renderSvgContent(props)}
            </Svg>
          </Animated.View>
        </View>

        {/* Connection line  */}
        {selectedBodyPart &&
          (() => {
            const coords = getLineCoordinates(selectedBodyPart)
            if (!coords) return null
            return (
              <Svg
                style={StyleSheet.absoluteFill}
                width={containerSize.width || '100%'}
                height={containerSize.height || '100%'}
                pointerEvents="none"
              >
                <Line
                  x1={coords.x1}
                  y1={coords.y1}
                  x2={coords.x2}
                  y2={coords.y2}
                  stroke={lavender[400]}
                  strokeWidth={1.5}
                  strokeDasharray="3 3"
                />
                <Defs>
                  <Filter id="drop-shadow">
                    <FeDropShadow
                      dx="0"
                      dy="0"
                      stdDeviation="1"
                      floodColor="#8704EC33"
                      floodOpacity={0.2}
                    />
                    <FeGaussianBlur stdDeviation="0.3" result="blurred" />
                  </Filter>
                </Defs>

                <Circle
                  cx={coords.x2}
                  cy={coords.y2}
                  r={6}
                  fill="#fff"
                  stroke={'#9879DA80'}
                  strokeWidth={2}
                  filter="url(#drop-shadow)"
                />
              </Svg>
            )
          })()}

        {/* Body Parts Selection - Left side */}
        <Animated.View
          ref={leftContainerRef}
          className="absolute left-0 top-1/2 px-4 py-3"
          style={leftListAnimatedStyle}
          onLayout={(event) => {
            const { height } = event.nativeEvent.layout
            leftContainerHeight.current = height
            // Calculate translateY to center vertically: move up by half the height
            leftListTranslateY.value = -height / 2
            forceUpdate()
          }}
        >
          <View className="flex flex-col gap-2">
            {renderSelectionList('left', headBodyParts.left)}
          </View>
        </Animated.View>

        {/* Body Parts Selection - Right side */}
        <Animated.View
          ref={rightContainerRef}
          className="absolute right-0 top-1/2 px-4 py-3"
          style={rightListAnimatedStyle}
          onLayout={(event) => {
            const { height } = event.nativeEvent.layout
            rightContainerHeight.current = height
            // Calculate translateY to center vertically: move up by half the height
            rightListTranslateY.value = -height / 2
            forceUpdate()
          }}
        >
          <View className="flex flex-col justify-center gap-2">
            {renderSelectionList('right', headBodyParts.right)}
          </View>
        </Animated.View>
      </View>
    )
  },
)

BaseBodyBoxComponent.displayName = 'BaseBodyBox'

export const BaseBodyBox = memo(BaseBodyBoxComponent)
const styles = StyleSheet.create({
  wrapper: {
    width: '100%',
    position: 'relative',
  },
  container: {
    width: '100%',
    height: 400,
    maxHeight: 400,
    overflow: 'hidden',
  },
  svgContainer: {
    width: '100%',
    height: '100%',
    overflow: 'hidden',
  },
})
