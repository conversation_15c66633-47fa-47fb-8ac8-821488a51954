import { Text } from '@/components/ui/Text/Text'
import { LocaleEnum } from '@/enums/locale.enum'
import { useAppLanguage } from '@/hooks/common/useAppLanguage'
import { lavender } from '@/styles/_colors'
import { cn } from '@/utils/cn'
import { isTablet } from '@/utils/device'
import React, {
  createRef,
  forwardRef,
  memo,
  RefObject,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useReducer,
  useRef,
  useState,
} from 'react'

import { StyleSheet, TouchableOpacity, View } from 'react-native'
import Animated, {
  Easing,
  useAnimatedStyle,
  useSharedValue,
  withDelay,
  withSpring,
  withTiming,
} from 'react-native-reanimated'
import Svg, {
  Circle,
  Defs,
  FeDropShadow,
  FeGaussianBlur,
  Filter,
  Line,
  SvgProps,
} from 'react-native-svg'
import getBounds from 'svg-path-bounds'
import { AESTHETIC_BODY_PARTS_OPTIONS } from '../../../constants'
import { AestheticBodyAreaEnum, AestheticBodyPartsEnum, BodySideEnum } from '../../../enums'

const getPathBounds = (
  pathData: string,
  svgViewBox: { width: number; height: number },
): { x: number; y: number; width: number; height: number } => {
  try {
    const [left, top, right, bottom] = getBounds(pathData)
    return {
      x: left,
      y: top,
      width: right - left,
      height: bottom - top,
    }
  } catch {
    return {
      x: svgViewBox.width,
      y: svgViewBox.height,
      width: 100,
      height: 100,
    }
  }
}

interface BaseBodyBoxProps extends SvgProps {
  onSelectBodyPart?: (bodyPart: AestheticBodyPartsEnum | null) => void
  svgViewBox: { width: number; height: number }
  bodyPartPaths: Partial<Record<AestheticBodyPartsEnum, string>>
  bodySide: BodySideEnum
  leftBodyParts: AestheticBodyPartsEnum[]
  rightBodyParts: AestheticBodyPartsEnum[]
  renderSvgContent: (props: SvgProps) => React.ReactNode
}

export interface BaseBodyBoxRef {
  resetSelectedBodyPart: () => void
}

interface AnimatedOptionItemProps {
  part: {
    value: AestheticBodyPartsEnum
    displayLabel: string
  }
  index: number
  side: 'left' | 'right'
  isSelected: boolean
  onPress: () => void
  buttonRef: RefObject<View | null>
}

const AnimatedOptionItem = memo(
  ({ part, index, side, isSelected, onPress, buttonRef }: AnimatedOptionItemProps) => {
    const baseXDistance = 90 - index * 10
    const minXDistance = 55
    const xDistance = Math.max(baseXDistance, minXDistance)

    const yPattern = index % 3
    let yOffset: number
    if (yPattern === 0) {
      yOffset = -80
    } else if (yPattern === 1) {
      yOffset = 0
    } else {
      yOffset = 80
    }

    const scaleStart = 0.5 + index * 0.02

    const initialX = side === 'left' ? -xDistance : xDistance
    const initialY = yOffset
    const initialScale = scaleStart

    const itemOpacity = useSharedValue(0)
    const itemScale = useSharedValue(initialScale)
    const itemTranslateX = useSharedValue(initialX)
    const itemTranslateY = useSharedValue(initialY)

    useEffect(() => {
      const delay = 200 + index * 60

      itemOpacity.value = withDelay(
        delay,
        withSpring(1, {
          damping: 18,
          stiffness: 200,
          mass: 0.7,
        }),
      )
      itemScale.value = withDelay(
        delay,
        withSpring(1, {
          damping: 16,
          stiffness: 220,
          mass: 0.6,
        }),
      )
      itemTranslateX.value = withDelay(
        delay,
        withSpring(0, {
          damping: 20,
          stiffness: 180,
          mass: 0.8,
        }),
      )
      itemTranslateY.value = withDelay(
        delay,
        withSpring(0, {
          damping: 20,
          stiffness: 180,
          mass: 0.8,
        }),
      )
    }, [index, side, itemOpacity, itemScale, itemTranslateX, itemTranslateY])

    const itemAnimatedStyle = useAnimatedStyle(() => {
      return {
        opacity: itemOpacity.value,
        transform: [
          { translateX: itemTranslateX.value },
          { translateY: itemTranslateY.value },
          { scale: itemScale.value },
        ],
      }
    })

    return (
      <View ref={buttonRef}>
        <Animated.View style={itemAnimatedStyle}>
          <TouchableOpacity
            onPress={onPress}
            className={cn(
              'mb-3 w-fit rounded-lg border-[1.2px] border-custom-lavender-400 px-3 py-2',
              side === 'left' ? 'self-start' : 'self-end',
              isSelected ? ' bg-custom-lavender-400' : ' bg-white',
            )}
          >
            <Text size="body6" variant={isSelected ? 'white' : 'default'}>
              {part.displayLabel}
            </Text>
          </TouchableOpacity>
        </Animated.View>
      </View>
    )
  },
)

AnimatedOptionItem.displayName = 'AnimatedOptionItem'

const BaseBodyBoxComponent = forwardRef<BaseBodyBoxRef, BaseBodyBoxProps>(
  (
    {
      onSelectBodyPart,
      svgViewBox,
      bodyPartPaths,
      bodySide,
      leftBodyParts,
      rightBodyParts,
      renderSvgContent,
      ...props
    },
    ref,
  ) => {
    const [internalSelectedBodyPart, setInternalSelectedBodyPart] =
      useState<AestheticBodyPartsEnum | null>(null)

    const selectedBodyPart = internalSelectedBodyPart

    useImperativeHandle(ref, () => ({
      resetSelectedBodyPart: () => {
        setInternalSelectedBodyPart(null)
        onSelectBodyPart?.(null)
      },
    }))

    const [containerSize, setContainerSize] = useState({
      width: 0,
      height: 0,
    })

    const svgOpacity = useSharedValue(0)
    const leftListOpacity = useSharedValue(0)
    const rightListOpacity = useSharedValue(0)
    const leftListTranslateX = useSharedValue(-60)
    const rightListTranslateX = useSharedValue(60)
    const leftListTranslateY = useSharedValue(0)
    const rightListTranslateY = useSharedValue(0)

    const buttonRefs = useRef<
      Record<string, { x: number; y: number; width: number; height: number }>
    >({})
    const buttonElementRefs = useRef<Record<string, RefObject<View | null>>>({})
    const wrapperRef = useRef<View>(null)
    const leftContainerRef = useRef<View>(null)
    const rightContainerRef = useRef<View>(null)
    const leftContainerHeight = useRef<number>(0)
    const rightContainerHeight = useRef<number>(0)

    const { primaryLanguage } = useAppLanguage()

    const flatBodyParts = useMemo(() => {
      return Object.values(AESTHETIC_BODY_PARTS_OPTIONS)
        .filter(
          (part) =>
            part.area === AestheticBodyAreaEnum.FULL_BODY &&
            part.hasOwnProperty('side') &&
            part.side === bodySide,
        )
        .map((part) => ({
          ...part,
          displayLabel:
            primaryLanguage === LocaleEnum.VI
              ? part.fixedLabel[LocaleEnum.VI] || part.label
              : part.fixedLabel[LocaleEnum.JA] || part.label,
        }))
    }, [primaryLanguage, bodySide])

    const headBodyParts = useMemo(() => {
      const leftFlat = flatBodyParts.filter((part) => leftBodyParts.includes(part.value))
      const rightFlat = flatBodyParts.filter((part) => rightBodyParts.includes(part.value))

      return {
        left: [...leftFlat],
        right: [...rightFlat],
      }
    }, [flatBodyParts, leftBodyParts, rightBodyParts])

    const containerHeight = useMemo(() => {
      return isTablet(false) ? 500 : 400
    }, [])

    const svgScale = useMemo(() => {
      if (containerSize.width === 0 || containerSize.height === 0) return 1
      const scaleX = containerSize.width / svgViewBox.width
      const scaleY = containerSize.height / svgViewBox.height
      return Math.min(scaleX, scaleY)
    }, [containerSize, svgViewBox])

    const getBodyPartSide = useCallback(
      (bodyPart: AestheticBodyPartsEnum): 'left' | 'right' | null => {
        if (leftBodyParts.includes(bodyPart)) return 'left'
        if (rightBodyParts.includes(bodyPart)) return 'right'
        return null
      },
      [leftBodyParts, rightBodyParts],
    )

    const getLineCoordinates = useCallback(
      (bodyPart: AestheticBodyPartsEnum) => {
        const buttonPos = buttonRefs.current[bodyPart]
        if (!buttonPos) return null

        const pathData = bodyPartPaths[bodyPart]
        if (!pathData) return null

        const bounds = getPathBounds(pathData, svgViewBox)
        const pathCenterX = bounds.x + bounds.width / 2
        const pathCenterY = bounds.y + bounds.height / 2

        const buttonCenterX = buttonPos.x + buttonPos.width / 2
        let buttonCenterY = buttonPos.y + buttonPos.height / 2

        const side = getBodyPartSide(bodyPart)
        if (side === 'left' && leftContainerHeight.current > 0) {
          buttonCenterY -= leftContainerHeight.current * 0.5
        } else if (side === 'right' && rightContainerHeight.current > 0) {
          buttonCenterY -= rightContainerHeight.current * 0.5
        }

        const containerCenterX = containerSize.width / 2
        const containerCenterY = containerSize.height / 2
        const pathScreenX = containerCenterX + (pathCenterX - svgViewBox.width / 2) * svgScale
        const pathScreenY = containerCenterY + (pathCenterY - svgViewBox.height / 2) * svgScale

        return {
          x1: buttonCenterX,
          y1: buttonCenterY,
          x2: pathScreenX,
          y2: pathScreenY,
        }
      },
      [containerSize, svgScale, getBodyPartSide, bodyPartPaths, svgViewBox],
    )

    const onLayout = (event: any) => {
      const { width, height } = event.nativeEvent.layout
      setContainerSize({ width, height })
    }

    useEffect(() => {
      svgOpacity.value = withTiming(1, {
        duration: 400,
        easing: Easing.out(Easing.cubic),
      })

      setTimeout(() => {
        leftListTranslateX.value = withSpring(0, {
          damping: 20,
          stiffness: 180,
          mass: 0.9,
        })
        leftListOpacity.value = withSpring(1, {
          damping: 18,
          stiffness: 200,
          mass: 0.8,
        })

        setTimeout(() => {
          rightListTranslateX.value = withSpring(0, {
            damping: 20,
            stiffness: 180,
            mass: 0.9,
          })
          rightListOpacity.value = withSpring(1, {
            damping: 18,
            stiffness: 200,
            mass: 0.8,
          })
        }, 80)
      }, 150)
    }, [svgOpacity, leftListTranslateX, rightListTranslateX, leftListOpacity, rightListOpacity])

    const svgAnimatedStyle = useAnimatedStyle(() => {
      return {
        opacity: svgOpacity.value,
      }
    })

    const leftListAnimatedStyle = useAnimatedStyle(() => {
      return {
        opacity: leftListOpacity.value,
        transform: [
          { translateY: leftListTranslateY.value },
          { translateX: leftListTranslateX.value },
        ],
      }
    })

    const rightListAnimatedStyle = useAnimatedStyle(() => {
      return {
        opacity: rightListOpacity.value,
        transform: [
          { translateY: rightListTranslateY.value },
          { translateX: rightListTranslateX.value },
        ],
      }
    })

    const [, forceUpdate] = useReducer((x) => x + 1, 0)

    const renderSelectionList = useCallback(
      (side: 'left' | 'right', parts: typeof headBodyParts.left) => {
        return parts.map((part, index) => {
          return (
            <AnimatedOptionItem
              key={part.value}
              part={part}
              index={index}
              side={side}
              isSelected={internalSelectedBodyPart === part.value}
              onPress={() => {
                const newValue = internalSelectedBodyPart === part.value ? null : part.value
                setInternalSelectedBodyPart(newValue)
                if (newValue) {
                  onSelectBodyPart?.(newValue)
                } else {
                  onSelectBodyPart?.(null)
                }
              }}
              buttonRef={buttonElementRefs.current[part.value]}
            />
          )
        })
      },
      [
        internalSelectedBodyPart,
        setInternalSelectedBodyPart,
        buttonElementRefs,
        onSelectBodyPart,
        headBodyParts,
      ],
    )

    useEffect(() => {
      const allParts = [...headBodyParts.left, ...headBodyParts.right]
      allParts.forEach((part) => {
        if (!buttonElementRefs.current[part.value]) {
          buttonElementRefs.current[part.value] = createRef<View>()
        }
      })
    }, [headBodyParts])

    useEffect(() => {
      if (!wrapperRef.current) return

      const allParts = [...headBodyParts.left, ...headBodyParts.right]
      allParts.forEach((part) => {
        const buttonRef = buttonElementRefs.current[part.value]
        if (buttonRef?.current && wrapperRef.current) {
          buttonRef.current.measureLayout(
            wrapperRef.current as any,
            (x, y, width, height) => {
              const oldPos = buttonRefs.current[part.value]
              const newPos = { x, y, width, height }
              if (
                !oldPos ||
                oldPos.x !== newPos.x ||
                oldPos.y !== newPos.y ||
                oldPos.width !== newPos.width ||
                oldPos.height !== newPos.height
              ) {
                buttonRefs.current[part.value] = newPos
                forceUpdate()
              }
            },
            () => {},
          )
        }
      })
    }, [headBodyParts, containerSize])

    return (
      <View ref={wrapperRef} style={styles.wrapper}>
        <View
          style={[styles.container, { height: containerHeight, maxHeight: containerHeight }]}
          onLayout={onLayout}
        >
          <Animated.View
            style={[styles.svgContainer, svgAnimatedStyle]}
            renderToHardwareTextureAndroid={true}
            shouldRasterizeIOS={true}
          >
            <Svg
              width={'100%'}
              height={'100%'}
              viewBox={`0 0 ${svgViewBox.width} ${svgViewBox.height}`}
              fill="none"
              {...props}
            >
              {renderSvgContent(props)}
            </Svg>
          </Animated.View>
        </View>

        {/* Connection line  */}
        {selectedBodyPart &&
          (() => {
            const coords = getLineCoordinates(selectedBodyPart)
            if (!coords) return null
            return (
              <Svg
                style={StyleSheet.absoluteFill}
                width={containerSize.width || '100%'}
                height={containerSize.height || '100%'}
                pointerEvents="none"
              >
                <Line
                  x1={coords.x1}
                  y1={coords.y1}
                  x2={coords.x2}
                  y2={coords.y2}
                  stroke={lavender[400]}
                  strokeWidth={1.5}
                  strokeDasharray="3 3"
                />
                <Defs>
                  <Filter id="drop-shadow">
                    <FeDropShadow
                      dx="0"
                      dy="0"
                      stdDeviation="1"
                      floodColor="#8704EC33"
                      floodOpacity={0.2}
                    />
                    <FeGaussianBlur stdDeviation="0.3" result="blurred" />
                  </Filter>
                </Defs>

                <Circle
                  cx={coords.x2}
                  cy={coords.y2}
                  r={6}
                  fill="#fff"
                  stroke={'#9879DA80'}
                  strokeWidth={2}
                  filter="url(#drop-shadow)"
                />
              </Svg>
            )
          })()}

        {/* Body Parts Selection - Left side */}
        <Animated.View
          ref={leftContainerRef}
          className="absolute left-0 top-1/2 px-4 py-3"
          style={leftListAnimatedStyle}
          onLayout={(event) => {
            const { height } = event.nativeEvent.layout
            leftContainerHeight.current = height
            leftListTranslateY.value = -height / 2
            forceUpdate()
          }}
        >
          <View className="flex flex-col gap-2">
            {renderSelectionList('left', headBodyParts.left)}
          </View>
        </Animated.View>

        {/* Body Parts Selection - Right side */}
        <Animated.View
          ref={rightContainerRef}
          className="absolute right-0 top-1/2 px-4 py-3"
          style={rightListAnimatedStyle}
          onLayout={(event) => {
            const { height } = event.nativeEvent.layout
            rightContainerHeight.current = height
            rightListTranslateY.value = -height / 2
            forceUpdate()
          }}
        >
          <View className="flex flex-col justify-center gap-2">
            {renderSelectionList('right', headBodyParts.right)}
          </View>
        </Animated.View>
      </View>
    )
  },
)

BaseBodyBoxComponent.displayName = 'BaseBodyBox'

export const BaseBodyBox = memo(BaseBodyBoxComponent)
const styles = StyleSheet.create({
  wrapper: {
    width: '100%',
    position: 'relative',
  },
  container: {
    width: '100%',
    height: 400,
    maxHeight: 400,
    overflow: 'hidden',
  },
  svgContainer: {
    width: '100%',
    height: '100%',
    overflow: 'hidden',
  },
})
