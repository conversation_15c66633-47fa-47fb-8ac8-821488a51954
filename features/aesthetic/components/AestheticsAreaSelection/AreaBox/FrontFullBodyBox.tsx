import React, { forwardRef } from 'react'
import { BaseBodyBox, BaseBodyBoxRef } from './BaseBodyBox'

import { AestheticBodyPartsEnum, BodySideEnum } from '@/features/aesthetic/enums'
import { ClipPath, Defs, G, Path, Rect } from 'react-native-svg'
interface FrontFullBodyBoxProps {
  onSelectBodyPart?: (bodyPart: AestheticBodyPartsEnum | null) => void
}
export const FrontFullBodyBox = forwardRef<BaseBodyBoxRef, FrontFullBodyBoxProps>(
  ({ onSelectBodyPart }, ref) => {
    return (
      <BaseBodyBox
        ref={ref}
        onSelectBodyPart={onSelectBodyPart}
        svgViewBox={{ width: 149, height: 488 }}
        bodyPartPaths={{
          [AestheticBodyPartsEnum.ARMPIT]:
            'M32.4736 40.75C34.5295 40.7501 36.1961 42.4174 36.1963 44.4746C36.1963 46.532 34.5296 48.2001 32.4736 48.2002C30.4175 48.2002 28.75 46.5321 28.75 44.4746C28.7502 42.4173 30.4177 40.75 32.4736 40.75Z',
          [AestheticBodyPartsEnum.ABDOMEN]:
            'M58.4736 151.75C60.5295 151.75 62.1961 153.417 62.1963 155.475C62.1963 157.532 60.5296 159.2 58.4736 159.2C56.4175 159.2 54.75 157.532 54.75 155.475C54.7502 153.417 56.4177 151.75 58.4736 151.75Z',
          [AestheticBodyPartsEnum.THIGH]:
            'M41.4736 257.75C43.5295 257.75 45.1961 259.417 45.1963 261.475C45.1963 263.532 43.5296 265.2 41.4736 265.2C39.4175 265.2 37.75 263.532 37.75 261.475C37.7502 259.417 39.4177 257.75 41.4736 257.75Z',
          [AestheticBodyPartsEnum.CHEST]:
            'M101.473 64.75C99.4172 64.7501 97.7506 66.4174 97.7504 68.4746C97.7504 70.532 99.417 72.2001 101.473 72.2002C103.529 72.2002 105.197 70.5321 105.197 68.4746C105.196 66.4173 103.529 64.75 101.473 64.75Z',
          [AestheticBodyPartsEnum.BODY_SKIN]:
            'M133.473 129.75C131.417 129.75 129.751 131.417 129.75 133.475C129.75 135.532 131.417 137.2 133.473 137.2C135.529 137.2 137.197 135.532 137.197 133.475C137.197 131.417 135.529 129.75 133.473 129.75Z',
          [AestheticBodyPartsEnum.BIKINI_AREA]:
            'M86.4731 190.75C84.4173 190.75 82.7507 192.417 82.7505 194.475C82.7505 196.532 84.4172 198.2 86.4731 198.2C88.5292 198.2 90.1968 196.532 90.1968 194.475C90.1966 192.417 88.5291 190.75 86.4731 190.75Z',
        }}
        bodySide={BodySideEnum.FRONT}
        leftBodyParts={[
          AestheticBodyPartsEnum.ARMPIT,
          AestheticBodyPartsEnum.ABDOMEN,
          AestheticBodyPartsEnum.THIGH,
        ]}
        rightBodyParts={[
          AestheticBodyPartsEnum.CHEST,
          AestheticBodyPartsEnum.BODY_SKIN,
          AestheticBodyPartsEnum.BIKINI_AREA,
        ]}
        renderSvgContent={() => (
          <>
            <G clipPath="url(#clip0_22945_178220)">
              <Path
                d="M146.18 206.154C145.905 202.613 145.207 200.269 145.207 197.476C145.207 194.633 144.509 191.84 144.334 185.531C143.337 151.667 140.045 135.733 136.853 119.698C133.013 100.522 130.37 58.6029 129.148 45.5609C127.926 32.494 121.043 29.701 114.385 28.1051C114.136 28.0303 113.862 27.9555 113.612 27.9056C113.238 27.8308 112.864 27.756 112.515 27.7061C111.867 27.5814 111.193 27.4567 110.545 27.332C106.829 26.6338 103.363 25.9855 100.495 25.4119C94.9844 24.3147 90.1965 21.422 89.0993 19.1278C86.1318 13.0682 87.653 0.5 87.653 0.5H60.5217C60.5217 0.5 62.0428 13.0931 59.0753 19.1278C57.9532 21.3971 53.1902 24.3147 47.6792 25.4119C44.8115 25.9855 41.3452 26.6338 37.6296 27.332C36.9563 27.4567 36.308 27.5814 35.6596 27.7061C35.2856 27.7809 34.9115 27.8308 34.5624 27.9056C34.2881 27.9555 34.0387 28.0303 33.7894 28.1051C27.1562 29.701 20.2487 32.494 19.0268 45.5609C17.8048 58.6029 15.1615 100.497 11.3213 119.698C8.12935 135.733 4.83769 151.692 3.84021 185.531C3.66566 191.84 2.96742 194.633 2.96742 197.476C2.96742 200.269 2.26919 202.613 1.99489 206.154C1.62083 211.191 0.573486 219.57 0.573486 219.57L3.9649 232.687L9.84999 237.475V237.375L13.2165 240.617C13.9147 239.993 14.962 237.849 13.0419 235.43C11.8948 233.959 10.6978 232.463 10.3986 232.064L9.62556 221.665C10.3487 219.296 12.1941 217.775 12.1941 217.775C12.7676 219.271 13.0918 220.293 12.9422 222.338C12.7427 225.131 12.8175 228.173 13.9147 229.42C14.9122 230.517 16.4084 230.293 16.4084 230.293C16.4084 230.293 17.331 225.755 17.6552 224.184C19.4756 215.007 18.4781 210.418 15.8099 204.758C14.6379 202.264 13.9895 199.895 13.9646 196.529L14.7875 188.424C15.6353 183.462 16.957 177.926 19.0766 170.669C22.1439 160.146 23.6899 153.388 25.0864 145.682C26.9816 135.259 27.9292 126.855 29.2259 119.923C32.3181 103.29 36.4077 85.5346 36.8815 83.7641C38.0286 91.2701 38.7019 98.1527 39.7243 104.611C41.1707 113.738 43.3152 121.967 42.2928 129.847C41.0709 139.298 36.3329 154.011 31.0712 167.178C23.8396 185.332 23.3408 201.915 25.7846 230.742C27.8295 255.08 32.5924 272.885 35.1858 292.885C37.9289 314.081 38.6521 332.135 37.9039 341.536C36.3828 360.563 37.6047 369.865 39.6246 385.176C42.1681 404.277 47.1805 430.237 48.7515 446.471C49.1006 450.186 47.2054 452.281 48.2278 456.221C48.8263 458.59 47.9784 460.31 47.3799 463.502C46.7815 466.694 42.7666 478.689 42.3676 480.958C41.8689 483.851 43.1656 485.522 45.5346 484.873C45.5346 484.873 47.2802 486.918 49.6243 485.322C49.6243 485.322 51.719 488.065 54.5368 485.721C54.5368 485.721 55.0605 487.342 57.0554 487.292C59.1003 487.217 59.7486 485.098 59.7486 485.098C59.7486 485.098 61.0703 487.143 63.6388 486.768C66.2322 486.37 67.1549 484.599 67.2047 480.559C67.2546 476.519 66.7808 465.522 65.7833 458.939C64.9105 453.104 64.0128 440.81 64.736 429.389C66.6312 400.038 66.9803 381.111 65.9579 358.169C64.8357 332.684 66.0078 326.524 66.9803 310.091C67.9529 293.658 70.8455 257.25 71.1198 229.62C71.2694 216.578 70.4964 212.488 70.4964 212.488C71.2944 212.563 73.3392 212.613 74.1372 212.613C74.9352 212.613 76.98 212.563 77.778 212.488C77.778 212.488 77.0049 216.578 77.1545 229.62C77.4538 257.25 80.3465 293.658 81.2941 310.091C82.2666 326.524 83.4386 332.709 82.3165 358.169C81.319 381.111 81.6432 400.038 83.5384 429.389C84.2865 440.785 83.3638 453.104 82.491 458.939C81.4936 465.522 81.0447 476.519 81.0696 480.559C81.1195 484.599 82.0422 486.37 84.6356 486.768C87.2041 487.143 88.5257 485.098 88.5257 485.098C88.5257 485.098 89.1492 487.242 91.2189 487.292C93.2139 487.367 93.7375 485.721 93.7375 485.721C96.5554 488.065 98.6501 485.322 98.6501 485.322C100.994 486.943 102.74 484.873 102.74 484.873C105.109 485.522 106.405 483.851 105.907 480.958C105.508 478.689 101.518 466.694 100.894 463.502C100.296 460.31 99.4481 458.59 100.047 456.221C101.044 452.281 99.1738 450.161 99.5229 446.471C101.069 430.237 106.106 404.277 108.65 385.176C110.67 369.865 111.892 360.563 110.37 341.536C109.622 332.135 110.37 314.081 113.089 292.885C115.682 272.885 120.42 255.08 122.49 230.742C124.934 201.915 124.435 185.332 117.203 167.178C111.966 154.011 107.203 139.274 105.982 129.847C104.959 121.967 106.879 113.738 108.5 104.611C109.647 98.1527 110.221 91.2701 111.368 83.7641C111.842 85.5346 115.956 103.29 119.048 119.923C120.345 126.855 121.293 135.259 123.188 145.682C124.584 153.363 126.131 160.146 129.198 170.669C131.542 178.674 132.938 184.584 133.736 189.945C134.858 197.376 134.285 200.917 132.464 204.758C129.796 210.393 128.799 215.007 130.619 224.184C130.918 225.755 131.866 230.293 131.866 230.293C131.866 230.293 133.362 230.517 134.36 229.42C135.457 228.173 135.532 225.131 135.332 222.338C135.183 220.293 135.532 219.271 136.08 217.775C136.08 217.775 138.474 220.119 138.823 222.263L137.901 232.039C137.626 232.388 136.404 233.934 135.232 235.43C133.312 237.874 134.36 240.018 135.058 240.617L138.399 237.375V237.475L144.285 232.687L147.676 219.57C147.676 219.57 146.629 211.166 146.255 206.154H146.18Z"
                fill="#FFE1D6"
              />
              <Path
                d="M83.9374 207.549C80.8702 211.015 78.8005 212.237 77.005 212.461C75.9577 212.586 75.2345 212.586 73.9876 212.586C72.7408 212.586 71.9927 212.586 70.9703 212.461C69.1748 212.237 67.1051 211.015 64.0378 207.549"
                fill="#FFE1D6"
              />
              <Path
                d="M83.9374 207.549C80.8702 211.015 78.8005 212.237 77.005 212.461C75.9577 212.586 75.2345 212.586 73.9876 212.586C72.7408 212.586 71.9927 212.586 70.9703 212.461C69.1748 212.237 67.1051 211.015 64.0378 207.549"
                stroke="#8E7D6E"
                strokeWidth={0.997474}
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <Path
                d="M138.325 237.498L144.21 232.71L147.601 219.594C147.601 219.594 146.554 211.19 146.18 206.178C145.905 202.637 145.207 200.293 145.207 197.5C145.207 194.657 144.509 191.864 144.334 185.555C143.337 151.691 140.045 135.756 136.853 119.722C133.013 100.545 130.37 58.6263 129.148 45.5843C127.926 32.5174 121.043 29.7245 114.385 28.1285C114.136 28.0537 113.862 27.9789 113.612 27.929C113.238 27.8542 112.864 27.7794 112.515 27.7295C111.867 27.6049 111.193 27.4802 110.545 27.3555C106.829 26.6573 103.363 26.0089 100.495 25.4353C94.9844 24.3381 90.1965 21.4455 89.0993 19.1513C86.1318 13.0916 87.653 0.523438 87.653 0.523438H60.5217C60.5217 0.523438 62.0428 13.1165 59.0753 19.1513C57.9532 21.4205 53.1902 24.3381 47.6792 25.4353C44.8114 26.0089 41.3452 26.6573 37.6296 27.3555C36.9563 27.4802 36.308 27.6049 35.6596 27.7295C35.2856 27.8043 34.9115 27.8542 34.5624 27.929C34.2881 27.9789 34.0387 28.0537 33.7894 28.1285C27.1562 29.7245 20.2487 32.5174 19.0268 45.5843C17.8048 58.6263 15.1615 100.52 11.3213 119.722C8.12935 135.756 4.83769 151.716 3.84021 185.555C3.66566 191.864 2.96742 194.657 2.96742 197.5C2.96742 200.293 2.26919 202.637 1.99489 206.178C1.62083 211.215 0.573486 219.594 0.573486 219.594L3.9649 232.71L9.84999 237.498"
                stroke="#8E7D6E"
                strokeWidth={0.997474}
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <Path
                d="M113.238 56.084C112.839 74.6869 110.445 91.2449 108.276 105.708C107.029 114.012 104.037 121.693 105.907 133.987C107.328 143.388 111.892 154.036 117.128 167.178C124.36 185.332 124.859 201.915 122.415 230.742C120.37 255.08 115.607 272.885 113.014 292.884C110.271 314.081 109.548 332.135 110.296 341.536C111.817 360.563 110.595 369.864 108.575 385.176C106.032 404.277 101.019 430.236 99.4482 446.47C99.0991 450.186 100.994 452.281 99.9719 456.221C99.3734 458.59 100.221 460.31 100.82 463.502C101.418 466.694 105.433 478.689 105.832 480.958C106.331 483.851 105.034 485.521 102.665 484.873C102.665 484.873 100.919 486.918 98.5754 485.322C98.5754 485.322 96.4807 488.065 93.6629 485.721C93.6629 485.721 93.1392 487.342 91.1442 487.292C89.0994 487.217 88.4511 485.097 88.4511 485.097C88.4511 485.097 87.1294 487.142 84.5609 486.768C81.9675 486.369 81.0448 484.599 80.9949 480.559C80.9451 476.519 81.4189 465.522 82.4163 458.939C83.2891 453.104 84.1869 440.81 83.4637 429.389C81.5685 400.038 81.2194 381.111 82.2418 358.169C83.3639 332.683 82.1919 326.524 81.2194 310.091C80.2468 293.657 77.3542 257.25 77.0799 229.62C76.9552 216.578 77.7033 212.488 77.7033 212.488C76.9053 212.563 74.8605 212.613 74.0625 212.613C73.2645 212.613 71.2197 212.563 70.4217 212.488C70.4217 212.488 71.1948 216.578 71.0451 229.62C70.7459 257.25 67.8532 293.657 66.9056 310.091C65.9331 326.524 64.7611 332.708 65.8832 358.169C66.8807 381.111 66.5565 400.038 64.6613 429.389C63.9132 440.785 64.8359 453.104 65.7087 458.939C66.7061 465.522 67.155 476.519 67.1301 480.559C67.0802 484.599 66.1575 486.369 63.5641 486.768C60.9956 487.142 59.6739 485.097 59.6739 485.097C59.6739 485.097 59.0505 487.242 56.9808 487.292C54.9858 487.367 54.4621 485.721 54.4621 485.721C51.6443 488.065 49.5496 485.322 49.5496 485.322C47.2055 486.943 45.4599 484.873 45.4599 484.873C43.0909 485.521 41.7942 483.851 42.293 480.958C42.6919 478.689 46.6818 466.694 47.3053 463.502C47.9037 460.31 48.7516 458.59 48.1531 456.221C47.1556 452.281 49.0259 450.161 48.6768 446.47C47.1307 430.236 42.0935 404.277 39.5499 385.176C37.53 369.864 36.3081 360.563 37.8293 341.536C38.5774 332.135 37.8293 314.081 35.1111 292.884C32.5177 272.885 27.7797 255.08 25.71 230.742C23.2661 201.915 23.7649 185.332 30.9966 167.178C36.2333 153.986 40.7967 143.413 42.2181 133.987C44.0635 121.693 41.096 114.012 39.8491 105.708C37.6796 91.2449 35.2857 74.6869 34.8867 56.084"
                stroke="#8E7D6E"
                strokeWidth={0.997474}
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <Path
                d="M36.8319 83.6143C36.8319 83.6143 32.3931 102.392 29.1264 119.922C27.8297 126.855 26.8821 135.258 24.9869 145.682C23.5904 153.363 22.0443 160.145 18.9771 170.669C16.633 178.674 15.2366 184.584 14.4386 189.945C13.3164 197.376 13.89 200.917 15.7104 204.757C18.3786 210.393 19.3761 215.006 17.5557 224.183C17.2565 225.754 16.3089 230.293 16.3089 230.293C16.3089 230.293 14.8127 230.517 13.8152 229.42C12.718 228.173 12.6432 225.131 12.8426 222.338C13.0172 219.67 12.4187 218.772 11.521 216.278"
                stroke="#8E7D6E"
                strokeWidth={0.997474}
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <Path
                d="M9.15183 220.941C9.45108 225.33 10.2241 231.963 10.2241 231.963C10.2241 231.963 11.6206 233.709 12.9672 235.43C14.8873 237.873 13.84 240.018 13.1417 240.616L5.41131 233.135L3.16699 220.717"
                stroke="#8E7D6E"
                strokeWidth={0.997474}
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <Path
                d="M12.0945 217.773C12.0945 217.773 9.77533 219.095 9.37634 223.484"
                stroke="#8E7D6E"
                strokeWidth={0.997474}
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <Path
                d="M111.243 83.6143C111.243 83.6143 115.707 102.392 118.974 119.922C120.27 126.855 121.218 135.258 123.113 145.682C124.51 153.363 126.056 160.145 129.123 170.669C131.467 178.674 132.863 184.584 133.661 189.945C134.784 197.376 134.21 200.917 132.39 204.757C129.721 210.393 128.724 215.006 130.544 224.183C130.844 225.754 131.791 230.293 131.791 230.293C131.791 230.293 133.287 230.517 134.285 229.42C135.382 228.173 135.457 225.131 135.257 222.338C135.083 219.67 135.681 218.772 136.579 216.278"
                stroke="#8E7D6E"
                strokeWidth={0.997474}
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <Path
                d="M138.948 220.941C138.649 225.33 137.876 231.963 137.876 231.963C137.876 231.963 136.479 233.709 135.133 235.43C133.213 237.873 134.26 240.018 134.958 240.616L142.689 233.135L144.933 220.717"
                stroke="#8E7D6E"
                strokeWidth={0.997474}
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <Path
                d="M136.006 217.773C136.006 217.773 138.325 219.095 138.724 223.484"
                stroke="#8E7D6E"
                strokeWidth={0.997474}
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <Path
                d="M75.06 148.923C75.06 150.12 74.6111 151.067 74.0625 151.067C73.5139 151.067 73.0651 150.095 73.0651 148.923C73.0651 147.751 73.5139 146.778 74.0625 146.778C74.6111 146.778 75.06 147.751 75.06 148.923Z"
                fill="#D2B2A8"
              />
              <Path
                d="M74.0376 213.409C80.4214 213.409 85.2093 209.02 94.5107 202.96C103.862 196.851 122.964 190.367 122.964 190.367C122.54 178.996 116.505 165.58 116.505 165.58C103.313 170.842 85.2342 175.355 74.0376 175.355C62.8409 175.355 44.7867 170.867 31.5701 165.58C31.5701 165.58 25.5354 178.996 25.0865 190.367C25.0865 190.367 44.2131 196.851 53.5644 202.96C62.8659 209.02 67.6288 213.409 74.0376 213.409Z"
                fill="#FFF7F4"
                stroke="#8E7D6E"
                strokeWidth={0.997474}
                strokeMiterlimit={10}
              />
              <Path
                d="M88.4511 485.072L88.4012 481.182"
                stroke="#8E7D6E"
                strokeWidth={0.997474}
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <Path
                d="M93.6628 485.721L93.4135 482.18"
                stroke="#8E7D6E"
                strokeWidth={0.997474}
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <Path
                d="M98.5754 485.321L97.9769 482.154"
                stroke="#8E7D6E"
                strokeWidth={0.997474}
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <Path
                d="M102.665 484.872L102.141 482.154"
                stroke="#8E7D6E"
                strokeWidth={0.997474}
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <Path
                d="M59.649 485.072L59.6989 481.182"
                stroke="#8E7D6E"
                strokeWidth={0.997474}
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <Path
                d="M54.4373 485.721L54.6866 482.18"
                stroke="#8E7D6E"
                strokeWidth={0.997474}
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <Path
                d="M49.5245 485.321L50.123 482.154"
                stroke="#8E7D6E"
                strokeWidth={0.997474}
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <Path
                d="M45.4349 484.872L45.9337 482.154"
                stroke="#8E7D6E"
                strokeWidth={0.997474}
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <Path
                d="M70.6959 30.4972C69.7233 29.8488 68.651 28.5771 66.1574 28.6768C63.6886 28.7766 55.6091 28.3526 51.4197 27.9287"
                stroke="#D3BCA3"
                strokeWidth={0.997474}
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <Path
                d="M77.4041 30.4972C78.3766 29.8488 79.4489 28.5771 81.9426 28.6768C84.4113 28.7766 92.4908 28.3526 96.6802 27.9287"
                stroke="#D3BCA3"
                strokeWidth={0.997474}
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <Path
                d="M74.0375 53.8887C74.0375 53.8887 73.1647 60.4471 69.5239 65.4095H78.5511C74.9103 60.4471 74.0375 53.8887 74.0375 53.8887Z"
                fill="#D8BEAC"
              />
              <Path
                d="M57.1304 91.0949C65.185 89.3244 68.6761 88.277 74.0625 88.277C79.4488 88.277 82.94 89.3244 90.9946 91.0949C99.4731 92.9652 107.029 91.3443 110.395 88.0775C114.485 84.3121 116.455 78.9756 115.906 73.8386C115.383 68.9759 113.338 63.2903 113.338 63.2903C113.338 63.2903 113.338 63.2654 113.338 63.2155C110.52 54.8617 104.46 48.802 93.4135 50.1486C82.5909 51.4703 74.0625 61.0959 74.0625 61.0959C74.0625 61.0959 65.5091 51.4703 54.7115 50.1486C43.6645 48.802 37.6048 54.8617 34.7869 63.2155C34.7869 63.2654 34.7869 63.2903 34.7869 63.2903C34.7869 63.2903 32.7172 68.9759 32.2184 73.8386C31.6698 78.9507 33.6398 84.2871 37.7295 88.0775C41.096 91.3692 48.6518 92.9652 57.1304 91.0949Z"
                fill="#FFF7F4"
                stroke="#8E7D6E"
                strokeWidth={0.997474}
                strokeMiterlimit={10}
              />
              <Path
                d="M37.2806 81.7686C37.2806 81.7686 40.4476 89.1748 50.4224 89.1748C60.3971 89.1748 62.2424 84.4368 67.1051 82.9157C67.1051 82.9157 63.8883 82.9655 59.3747 84.4119C56.9808 85.1849 53.7888 85.7335 50.0483 85.7335C46.3078 85.7335 41.2456 85.135 37.2806 81.7935V81.7686Z"
                fill="#E7D4D4"
              />
              <Path
                d="M110.819 81.7686C110.819 81.7686 107.652 89.1748 97.6775 89.1748C87.7028 89.1748 85.8574 84.4368 80.9948 82.9157C80.9948 82.9157 84.2116 82.9655 88.7252 84.4119C91.1191 85.1849 94.311 85.7335 98.0516 85.7335C101.792 85.7335 106.854 85.135 110.819 81.7935V81.7686Z"
                fill="#E7D4D4"
              />
              <G filter="url(#filter0_d_22945_178220)">
                <Path d="M32.4736 40.75C34.5295 40.7501 36.1961 42.4174 36.1963 44.4746C36.1963 46.532 34.5296 48.2001 32.4736 48.2002C30.4175 48.2002 28.75 46.5321 28.75 44.4746C28.7502 42.4173 30.4177 40.75 32.4736 40.75Z" />
              </G>
              <G filter="url(#filter1_d_22945_178220)">
                <Path d="M58.4736 151.75C60.5295 151.75 62.1961 153.417 62.1963 155.475C62.1963 157.532 60.5296 159.2 58.4736 159.2C56.4175 159.2 54.75 157.532 54.75 155.475C54.7502 153.417 56.4177 151.75 58.4736 151.75Z" />
              </G>
              <G filter="url(#filter2_d_22945_178220)">
                <Path d="M101.473 64.75C99.4172 64.7501 97.7506 66.4174 97.7504 68.4746C97.7504 70.532 99.417 72.2001 101.473 72.2002C103.529 72.2002 105.197 70.5321 105.197 68.4746C105.196 66.4173 103.529 64.75 101.473 64.75Z" />
              </G>
              <G filter="url(#filter3_d_22945_178220)">
                <Path d="M41.4736 257.75C43.5295 257.75 45.1961 259.417 45.1963 261.475C45.1963 263.532 43.5296 265.2 41.4736 265.2C39.4175 265.2 37.75 263.532 37.75 261.475C37.7502 259.417 39.4177 257.75 41.4736 257.75Z" />
              </G>
              <G filter="url(#filter4_d_22945_178220)">
                <Path d="M86.4731 190.75C84.4173 190.75 82.7507 192.417 82.7505 194.475C82.7505 196.532 84.4172 198.2 86.4731 198.2C88.5292 198.2 90.1968 196.532 90.1968 194.475C90.1966 192.417 88.5291 190.75 86.4731 190.75Z" />
              </G>
              <G filter="url(#filter5_d_22945_178220)">
                <Path d="M133.473 129.75C131.417 129.75 129.751 131.417 129.75 133.475C129.75 135.532 131.417 137.2 133.473 137.2C135.529 137.2 137.197 135.532 137.197 133.475C137.197 131.417 135.529 129.75 133.473 129.75Z" />
              </G>
            </G>
            <Defs>
              <ClipPath id="clip0_22945_178220">
                <Rect width={149} height={488} fill="white" />
              </ClipPath>
            </Defs>
          </>
        )}
      />
    )
  },
)

FrontFullBodyBox.displayName = 'FrontFullBodyBox'
