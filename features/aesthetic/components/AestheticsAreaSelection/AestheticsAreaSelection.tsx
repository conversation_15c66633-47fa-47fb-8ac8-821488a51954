import CloseIconDanger from '@/assets/icons/close-icon-danger.svg'
import EmptyBoxIcon from '@/assets/icons/empty-box.svg'
import FilterIcon from '@/assets/icons/filter-icon.svg'
import { BaseSearchTipsBox } from '@/components/BaseSearchTipsBox/BaseSearchTipsBox'
import { Skeleton } from '@/components/ui/Skeleton/Skeleton'
import { Text } from '@/components/ui/Text/Text'
import { primary } from '@/styles/_colors'
import React, { memo, useCallback, useEffect, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import {
  ActivityIndicator,
  FlatList,
  RefreshControl,
  TouchableOpacity,
  useWindowDimensions,
  View,
} from 'react-native'
import Animated, { useAnimatedStyle, useSharedValue, withTiming } from 'react-native-reanimated'
import { useSafeAreaInsets } from 'react-native-safe-area-context'
import { isTablet } from 'react-native-size-scaling'
import { useShallow } from 'zustand/react/shallow'
import { useAestheticStoreContext } from '../../contexts/AestheticStoreContext'
import { AestheticBodyAreaEnum, AestheticBodyPartsEnum, BodySideEnum } from '../../enums'
import { useGetInfiniteAesthetics } from '../../hooks/query/useGetInfiniteAesthetics'
import { SearchAestheticFilterItem, SearchAestheticFilterType } from '../../stores/AestheticStore'
import { Aesthetic } from '../../types'
import { AestheticItem } from '../AestheticItem/AestheticItem'
import { useOpenFilterAestheticBox } from '../FilterAestheticBox/FilterAestheticBox'
import { SelectedFilterBadge } from '../FilterAestheticBox/SelectedFilterBadge'
import { FrontFaceBox } from './AreaBox/FrontFaceBox'
import { FullBodyBox } from './AreaBox/FullBodyBox'

type ListItem =
  | { type: 'aesthetic'; aesthetic: Aesthetic }
  | { type: 'loading' }
  | { type: 'loading_skeleton' }

interface AestheticsAreaSelectionProps {
  bodyArea?: AestheticBodyAreaEnum
}

export const AestheticsAreaSelection = ({
  bodyArea = AestheticBodyAreaEnum.HEAD,
}: AestheticsAreaSelectionProps) => {
  const [refreshing, setRefreshing] = useState(false)
  const { useStore } = useAestheticStoreContext()
  const {
    searchAestheticFilters,
    hasActiveFilters,
    toggleSearchAestheticFilter,
    clearAllSearchAestheticFilters,
    resetAllAestheticFilters,
  } = useStore(
    useShallow((state) => ({
      searchAestheticFilters: state.searchAestheticFilters,
      hasActiveFilters: state.hasActiveFilters,
      toggleSearchAestheticFilter: state.toggleSearchAestheticFilter,
      clearAllSearchAestheticFilters: state.clearAllSearchAestheticFilters,
      resetAllAestheticFilters: state.resetAllAestheticFilters,
    })),
  )

  const [selectedBodyPart, setSelectedBodyPart] = useState<AestheticBodyPartsEnum | null>(null)

  // Animated value for header centering
  const headerCenterProgress = useSharedValue(selectedBodyPart ? 0 : 1)

  // Separate animated value for list opacity with delay
  const listOpacityProgress = useSharedValue(selectedBodyPart ? 1 : 0)

  // Animate header centering when selectedBodyPart changes
  useEffect(() => {
    headerCenterProgress.value = withTiming(selectedBodyPart ? 0 : 1, {
      duration: 400,
    })

    // Delay list opacity animation to ensure header animation completes first
    if (selectedBodyPart) {
      // Start list animation after header animation completes (400ms delay)
      const delayTimeout = setTimeout(() => {
        listOpacityProgress.value = withTiming(1, {
          duration: 300,
        })
      }, 400) // Wait for header animation to complete

      return () => clearTimeout(delayTimeout)
    } else {
      // Hide immediately when deselecting
      listOpacityProgress.value = withTiming(0, {
        duration: 200,
      })
    }
  }, [selectedBodyPart, headerCenterProgress, listOpacityProgress])

  // Get active filters for display
  const activeFilters = useMemo(() => {
    return Object.entries(searchAestheticFilters)
      .filter(
        ([key, value]) =>
          key !== SearchAestheticFilterType.SEARCH_TEXT &&
          value &&
          Array.isArray(value) &&
          value.length > 0,
      )
      .flatMap(([_, value]) => value as SearchAestheticFilterItem[])
  }, [searchAestheticFilters])

  // Build query params based on filters
  const queryParams = useMemo(() => {
    const treatmentNeedIds = searchAestheticFilters[SearchAestheticFilterType.TREATMENT_NEED]?.map(
      (f) => f.id,
    )
    const interventionLevelIds = searchAestheticFilters[
      SearchAestheticFilterType.INTERVENTION_LEVEL
    ]?.map((f) => f.id)
    const recoveryTimeIds = searchAestheticFilters[SearchAestheticFilterType.RECOVERY_TIME]?.map(
      (f) => f.id,
    )
    const effectivenessIds = searchAestheticFilters[SearchAestheticFilterType.EFFECTIVENESS]?.map(
      (f) => f.id,
    )
    const specialTypesIds = searchAestheticFilters[SearchAestheticFilterType.SPECIAL_TYPES]?.map(
      (f) => f.id,
    )
    const bodyAreaIds = searchAestheticFilters[SearchAestheticFilterType.BODY_AREA]?.map(
      (f) => f.id,
    )
    const bodyPartsIds = [
      ...(selectedBodyPart ? [selectedBodyPart] : []),
      ...(searchAestheticFilters[SearchAestheticFilterType.BODY_PARTS]?.map((f) => f.id) || []),
    ]

    return {
      locale: 'all',
      limit: 20,
      select: {
        name: 'true',
        icon: 'true',
        image: 'true',
        interventionLevels: 'true',
        effectiveness: 'true',
        recoveryTimes: 'true',
        specialTypes: 'true',
        bodyAreas: 'true',
        bodyParts: 'true',
        treatmentNeeds: 'true',
      },
      interventionLevels: interventionLevelIds || [],
      treatmentNeeds: treatmentNeedIds || [],
      recoveryTimes: recoveryTimeIds || [],
      effectiveness: effectivenessIds || [],
      specialTypes: specialTypesIds || [],
      bodyAreas: bodyAreaIds || [],
      bodyParts: bodyPartsIds || [],
    }
  }, [searchAestheticFilters, selectedBodyPart])

  const {
    aesthetics,
    isGetAestheticsError,
    isGetAestheticsFetching,
    isGetAestheticsFetchingNextPage,
    fetchNextPage,
    hasNextPage,
    isRefetching,
    refetch,
  } = useGetInfiniteAesthetics({
    params: queryParams,
    config: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      enabled: Boolean(selectedBodyPart),
    },
  })

  // Handle pull-to-refresh
  const handleRefresh = useCallback(async () => {
    setRefreshing(true)
    try {
      await refetch()
    } finally {
      setRefreshing(false)
    }
  }, [refetch])

  // Flatten all aesthetics from all pages
  const allAesthetics = useMemo(() => {
    if (!aesthetics?.pages) return []
    return aesthetics.pages.flatMap((page) => page?.docs || [])
  }, [aesthetics])

  const data = useMemo((): ListItem[] => {
    // Always return items, but control visibility with opacity
    if (!selectedBodyPart) {
      return []
    }

    const items: ListItem[] = []

    // Show loading skeletons if initial loading or refreshing
    if (isGetAestheticsFetching || isRefetching || refreshing) {
      items.push({ type: 'loading_skeleton' })
    } else {
      // Aesthetics as individual items
      allAesthetics.forEach((aesthetic) => {
        items.push({ type: 'aesthetic', aesthetic })
      })

      // Loading indicator for pagination
      if (isGetAestheticsFetchingNextPage) {
        items.push({ type: 'loading' })
      }
    }

    return items
  }, [
    allAesthetics,
    isGetAestheticsFetchingNextPage,
    isGetAestheticsFetching,
    isRefetching,
    refreshing,
    selectedBodyPart,
  ])

  // Creative animation for list: slide up from below + fade
  // Use listOpacityProgress which has delay to ensure header animation completes first
  const listAnimatedStyle = useAnimatedStyle(() => {
    return {
      opacity: listOpacityProgress.value,
      transform: [
        { translateY: (1 - listOpacityProgress.value) * 20 }, // Slide up from below
      ],
    }
  })

  const handleLoadMore = useCallback(() => {
    if (hasNextPage && !isGetAestheticsFetchingNextPage) {
      fetchNextPage()
    }
  }, [hasNextPage, isGetAestheticsFetchingNextPage, fetchNextPage])

  const totalAesthetics = useMemo(() => {
    if (isGetAestheticsFetching) {
      return 0
    }
    return aesthetics?.pages[0]?.totalDocs || 0
  }, [aesthetics, isGetAestheticsFetching])

  const handleSelectBodyPart = useCallback(
    (bodyPart: AestheticBodyPartsEnum | null) => {
      setSelectedBodyPart(bodyPart)
      resetAllAestheticFilters()
    },
    [resetAllAestheticFilters],
  )

  const renderItem = useCallback(
    ({ item }: { item: ListItem }) => {
      switch (item.type) {
        case 'aesthetic':
          return (
            <Animated.View style={listAnimatedStyle} className="px-4">
              <AestheticItem aesthetic={item.aesthetic} />
            </Animated.View>
          )

        case 'loading':
          return (
            <Animated.View style={listAnimatedStyle} className="items-center px-4 py-2">
              <ActivityIndicator size="small" />
            </Animated.View>
          )

        case 'loading_skeleton':
          return (
            <Animated.View style={listAnimatedStyle} className="flex flex-col gap-y-4 px-4">
              {new Array(8).fill(0).map((_, index) => (
                <Skeleton key={index} className="h-20 w-full" />
              ))}
            </Animated.View>
          )

        default:
          return null
      }
    },
    [listAnimatedStyle],
  )

  const keyExtractor = useCallback((item: ListItem, index: number) => {
    if (item.type === 'aesthetic') {
      return `aesthetic-${item.aesthetic.id}-${index}`
    }
    return `${item.type}-${index}`
  }, [])

  const inset = useSafeAreaInsets()
  const { height: windowHeight } = useWindowDimensions()

  const contentContainerStyle = useMemo(
    () => ({
      paddingBottom: inset.bottom + 40,
      flexGrow: 1,
    }),
    [inset.bottom],
  )

  // Animated style to center header when no body part is selected
  const headerWrapperAnimatedStyle = useAnimatedStyle(() => {
    // Calculate center offset: move header down to center when progress is 1
    // Center position = (available screen height) / 2 - (estimated header height) / 2
    // Available screen height = windowHeight - top safe area - bottom safe area
    const availableHeight = windowHeight - inset.top - inset.bottom
    const estimatedHeaderHeight = isTablet ? 640 : 540
    // Move down by: (availableHeight / 2) - (headerHeight / 2) - some top padding
    const centerOffset = (availableHeight - estimatedHeaderHeight) / 2 - 20
    const translateY = headerCenterProgress.value * centerOffset
    return {
      transform: [{ translateY }],
    }
  })

  return (
    <View className="flex-1 ">
      <FlatList
        data={data}
        renderItem={renderItem}
        keyExtractor={keyExtractor}
        ListHeaderComponent={
          <Animated.View style={headerWrapperAnimatedStyle}>
            <AestheticsAreaSelectionsHeader
              handleSelectBodyPart={handleSelectBodyPart}
              onSelectBodyPart={handleSelectBodyPart}
              resetAllAestheticFilters={resetAllAestheticFilters}
              totalAesthetics={totalAesthetics}
              activeFilters={activeFilters}
              toggleSearchAestheticFilter={toggleSearchAestheticFilter}
              clearAllSearchAestheticFilters={clearAllSearchAestheticFilters}
              hasActiveFilters={hasActiveFilters}
              selectedBodyPart={selectedBodyPart}
              bodyArea={bodyArea}
            />
          </Animated.View>
        }
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.3}
        ListEmptyComponent={
          <Animated.View style={listAnimatedStyle}>
            <EmptyComponent
              isGetAestheticsError={isGetAestheticsError}
              selectedBodyPart={selectedBodyPart}
            />
          </Animated.View>
        }
        showsVerticalScrollIndicator={false}
        removeClippedSubviews={false}
        maxToRenderPerBatch={21}
        windowSize={21}
        initialNumToRender={21}
        contentContainerStyle={contentContainerStyle}
        ItemSeparatorComponent={Separator}
        style={{ flex: 1 }}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[primary['500']]}
            tintColor={primary['500']}
            progressBackgroundColor="#FFFFFF"
          />
        }
        className="h-full flex-1"
      />
    </View>
  )
}

const Separator = memo(() => {
  return <View className="h-2" />
})
Separator.displayName = 'Separator'

interface AestheticsAreaSelectionsHeaderProps {
  resetAllAestheticFilters: () => void
  onSelectBodyPart: (bodyPart: AestheticBodyPartsEnum) => void
  totalAesthetics: number
  activeFilters: SearchAestheticFilterItem[]
  toggleSearchAestheticFilter: (
    type: SearchAestheticFilterType,
    filter: SearchAestheticFilterItem,
  ) => void
  clearAllSearchAestheticFilters: () => void
  handleSelectBodyPart: (bodyPart: AestheticBodyPartsEnum | null) => void
  hasActiveFilters: boolean
  selectedBodyPart?: AestheticBodyPartsEnum | null
  bodyArea?: AestheticBodyAreaEnum
}

const AestheticsAreaSelectionsHeader = memo((props: AestheticsAreaSelectionsHeaderProps) => {
  const { t } = useTranslation()
  const {
    handleSelectBodyPart,
    resetAllAestheticFilters,
    totalAesthetics,
    activeFilters,
    toggleSearchAestheticFilter,
    clearAllSearchAestheticFilters,
    hasActiveFilters,
    selectedBodyPart,
    bodyArea,
  } = props
  const { handleOpenFilterAestheticBox } = useOpenFilterAestheticBox()

  // Animated values for smooth transition using Reanimated
  // Initialize with current state to prevent flash
  const animationProgress = useSharedValue(selectedBodyPart ? 1 : 0)

  // Animate when selectedBodyPart changes
  // Use immediate update to prevent flash, then animate
  useEffect(() => {
    const targetValue = selectedBodyPart ? 1 : 0
    // Only animate if value actually changed
    if (Math.abs(animationProgress.value - targetValue) > 0.01) {
      animationProgress.value = withTiming(targetValue, {
        duration: 400,
      })
    }
  }, [selectedBodyPart, animationProgress])

  // Creative animation for face box: scale down + slide up + slight fade
  // When progress is 0 (not selected): face box is centered
  // When progress is 1 (selected): face box moves to top
  const faceBoxAnimatedStyle = useAnimatedStyle(() => {
    const progress = animationProgress.value
    return {
      transform: [
        { scale: 1 - progress * 0.05 }, // Slight scale down (5%) when selected
      ],
      opacity: 1 - progress * 0.1, // Slight fade (10%) when selected
    }
  })

  // Creative animation for header content: slide up from below + fade + scale
  const headerAnimatedStyle = useAnimatedStyle(() => {
    const progress = animationProgress.value
    return {
      transform: [
        // { translateY: (1 - progress) * 30 }, // Slide up from below
        { scale: 0.9 + progress * 0.1 }, // Scale from 90% to 100%
      ],
      opacity: progress, // Fade in
    }
  })

  const handleBodySideChange = useCallback(
    (bodySide: BodySideEnum) => {
      resetAllAestheticFilters()
    },
    [resetAllAestheticFilters],
  )

  return (
    <View className={`flex flex-col gap-y-3 ${selectedBodyPart ? 'mb-3' : ''}`}>
      <Animated.View style={[faceBoxAnimatedStyle]}>
        <View className="flex flex-col gap-y-6">
          {/* <FrontFaceBox onSelectBodyPart={handleSelectBodyPart} /> */}
          {/* <FrontFullBodyBoxV2 onSelectBodyPart={handleSelectBodyPart} /> */}
          {bodyArea === AestheticBodyAreaEnum.FULL_BODY ? (
            <FullBodyBox
              onSelectBodyPart={handleSelectBodyPart}
              onBodySideChange={handleBodySideChange}
            />
          ) : (
            <FrontFaceBox onSelectBodyPart={handleSelectBodyPart} />
          )}
          {!selectedBodyPart && (
            <View className="px-4">
              <BaseSearchTipsBox description={t('MES-1012')} />
            </View>
          )}
        </View>
      </Animated.View>
      <Animated.View
        style={[
          headerAnimatedStyle,
          {
            // pointerEvents: selectedBodyPart ? 'auto' : 'none',
          },
        ]}
        className="flex flex-col gap-y-3 px-4"
      >
        {/* Header with total and filter button */}
        <View className=" flex flex-row items-center justify-between">
          <Text size="body3" variant="default">
            {t('MES-898')} ({totalAesthetics})
          </Text>
          <TouchableOpacity
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
            onPress={() => {
              handleOpenFilterAestheticBox([
                SearchAestheticFilterType.EFFECTIVENESS,
                SearchAestheticFilterType.RECOVERY_TIME,
                SearchAestheticFilterType.INTERVENTION_LEVEL,
              ])
            }}
            className="relative flex flex-row items-center gap-x-2"
          >
            <Text size="body6" variant="primary">
              {t('MES-481')}
            </Text>
            <FilterIcon width={18} height={18} />
            {hasActiveFilters && (
              <View className="absolute -right-[1px] -top-1 size-2 rounded-full bg-custom-danger-700 opacity-80" />
            )}
          </TouchableOpacity>
        </View>

        {/* Selected filters section */}
        {activeFilters.length > 0 && (
          <View className="flex flex-col gap-y-3 ">
            <View className="flex flex-row flex-wrap gap-2">
              {activeFilters.map((filter: SearchAestheticFilterItem) => (
                <SelectedFilterBadge
                  key={`${filter.type}-${filter.id}`}
                  filterItem={filter}
                  onClearFilter={(filterItem) => {
                    toggleSearchAestheticFilter(
                      filterItem.type as SearchAestheticFilterType,
                      filterItem as SearchAestheticFilterItem,
                    )
                  }}
                />
              ))}
              <TouchableOpacity
                onPress={() => clearAllSearchAestheticFilters()}
                className="flex flex-row items-center gap-x-1"
              >
                <Text size="body8" variant="error">
                  {t('MES-709')}
                </Text>
                <View className="-ml-1">
                  <CloseIconDanger width={20} height={20} />
                </View>
              </TouchableOpacity>
            </View>
          </View>
        )}
      </Animated.View>
    </View>
  )
})
AestheticsAreaSelectionsHeader.displayName = 'AestheticsAreaSelectionsHeader'

interface EmptyComponentProps {
  isGetAestheticsError: boolean
  selectedBodyPart: AestheticBodyPartsEnum | null
}
const EmptyComponent = memo((props: EmptyComponentProps) => {
  const { isGetAestheticsError } = props
  const { t } = useTranslation()

  if (isGetAestheticsError) {
    return (
      <View className="items-center  px-4 py-8">
        <Text size="body6" className="text-red-500">
          {t('MES-197')}
        </Text>
      </View>
    )
  }

  // Show empty state when body part is selected but no results
  return (
    <View className="flex flex-col items-center justify-center gap-y-2  px-4 py-8">
      <EmptyBoxIcon />
      <Text size="body6" variant="default">
        {t('MES-967')}
      </Text>
    </View>
  )
})
EmptyComponent.displayName = 'EmptyComponent'
