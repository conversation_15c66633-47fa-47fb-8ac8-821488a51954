import { Button } from '@/components/ui/Button/Button'
import { Text } from '@/components/ui/Text/Text'
import { StyledExpoImage } from '@/libs/styled'
import { APP_ROUTES, AppRoutesEnum } from '@/routes/appRoutes'
import { primary } from '@/styles/_colors'
import { Media } from '@/types/media.type'
import { getMediaInfo } from '@/utils/media-cms'
import * as Haptics from 'expo-haptics'
import { Link, LinkProps } from 'expo-router'
import React from 'react'
import { View } from 'react-native'
import { SvgUri } from 'react-native-svg'
import { Aesthetic } from '../../types'

interface AestheticCardProps {
  item: Aesthetic
  onPress?: () => void
  backgroundColor?: string
}

export const AestheticCard = ({
  item,
  onPress,
  backgroundColor = primary['50'],
}: AestheticCardProps) => {
  const { media } = getMediaInfo(item.icon)
  const { mimeType, url } = (media as Media) || {}

  const handlePress = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light)
    onPress?.()
  }

  return (
    <Link
      href={
        {
          pathname:
            APP_ROUTES.AESTHETICS?.children?.[AppRoutesEnum.AESTHETICS_DETAILS]?.path + '/[id]',
          params: {
            id: item.id,
          },
        } as LinkProps['href']
      }
      asChild
    >
      <Button
        onPress={handlePress}
        className="flex h-[130px] w-[120px] flex-col items-center justify-center gap-3 rounded-lg px-3 py-2"
        style={{
          backgroundColor: backgroundColor,
        }}
      >
        {url ? (
          mimeType === 'image/svg+xml' ? (
            <View className="h-10 w-10 shrink-0">
              <SvgUri width="100%" height="100%" uri={url} />
            </View>
          ) : (
            <View className="h-10 w-10 shrink-0">
              <StyledExpoImage source={url} contentFit="cover" className="h-full w-full" />
            </View>
          )
        ) : null}
        <View className="h-[50px] overflow-hidden">
          <Text
            size="body6"
            variant="default"
            className="whitespace-pre text-center"
            numberOfLines={2}
          >
            {item.name}
          </Text>
        </View>
      </Button>
    </Link>
  )
}
