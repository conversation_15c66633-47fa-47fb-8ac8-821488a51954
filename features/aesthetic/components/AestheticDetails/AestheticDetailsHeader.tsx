import ArchiveIcon from '@/assets/icons/archive-icon.svg'
import ArchiveTickIcon from '@/assets/icons/archive-tick-icon.svg'
import BackScreenIcon from '@/assets/icons/back-screen-icon.svg'
import { Text } from '@/components/ui/Text/Text'
import { useAuthentication } from '@/hooks/auth/useAuthentication'
import { cn } from '@/utils/cn'
import { router } from 'expo-router'
import { useCallback, useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { TouchableOpacity, View } from 'react-native'
import { useUpdateFavoriteAesthetic } from '../../hooks/query/useUpdateFavoriteAesthetic'
interface AestheticDetailsHeaderProps {
  isFavorite?: boolean
  id: string
  isLoading?: boolean
  onUpdateFavoriteAestheticSuccess?: ({ type, id }: { type: 'add' | 'delete'; id: string }) => void
}
export const AestheticDetailsHeader = ({
  isFavorite,
  id,
  isLoading,
  onUpdateFavoriteAestheticSuccess,
}: AestheticDetailsHeaderProps) => {
  const { user, status } = useAuthentication()
  const { t } = useTranslation()
  const [isFavoriteAesthetic, setIsFavoriteAesthetic] = useState<boolean>(Boolean(isFavorite))

  const { updateFavoriteAestheticMutation, isUpdateFavoriteAestheticPending } =
    useUpdateFavoriteAesthetic()

  const handleUpdateFavoriteAesthetic = useCallback(
    (id: string, isFavorite: boolean) => {
      const type = isFavorite ? 'delete' : 'add'
      // Optimistic update - update immediately
      setIsFavoriteAesthetic(type === 'add' ? true : false)
      updateFavoriteAestheticMutation(
        { id, type },
        {
          onSuccess: () => {
            setIsFavoriteAesthetic(type === 'add' ? true : false)
            onUpdateFavoriteAestheticSuccess?.({ type, id })
          },
          onError: () => {
            // Rollback on error
            setIsFavoriteAesthetic(isFavorite)
            onUpdateFavoriteAestheticSuccess?.({ type, id })
          },
        },
      )
    },
    [updateFavoriteAestheticMutation, onUpdateFavoriteAestheticSuccess],
  )
  useEffect(() => {
    setIsFavoriteAesthetic(Boolean(isFavorite))
  }, [isFavorite])
  return (
    <View className="flex flex-row  items-center justify-between gap-x-2 px-4 py-3">
      <TouchableOpacity onPress={() => router.back()}>
        <BackScreenIcon width={24} height={24} />
      </TouchableOpacity>

      <Text size="body3" variant="primary">
        {t('MES-1010')}
      </Text>
      {user && status === 'success' ? (
        <TouchableOpacity
          disabled={isUpdateFavoriteAestheticPending || isLoading}
          onPress={() => handleUpdateFavoriteAesthetic(id, isFavoriteAesthetic)}
          className={cn('h-7 w-7', (isUpdateFavoriteAestheticPending || isLoading) && 'opacity-50')}
        >
          {!isLoading && (
            <>
              {isFavoriteAesthetic ? (
                <ArchiveTickIcon width={24} height={24} />
              ) : (
                <ArchiveIcon width={24} height={24} />
              )}
            </>
          )}
        </TouchableOpacity>
      ) : (
        <View className="h-7 w-7" />
      )}
    </View>
  )
}
