import { BaseWebView } from '@/components/BaseWebView/BaseWebView'
import { MESSAGE_NATIVE } from '@/constants/message-native.constant'
import { WEBVIEW_APP_ROUTES } from '@/routes/webviewRoutes'
import { useQueryClient } from '@tanstack/react-query'
import { useCallback, useEffect, useRef, useState } from 'react'
import { View } from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import { WebViewMessageEvent } from 'react-native-webview'
import { aestheticQueryKeys } from '../../hooks/query/queryKeys'
import { AestheticDetailsHeader } from './AestheticDetailsHeader'
interface AestheticDetailsProps {
  id: string
}
export const AestheticDetails = ({ id }: AestheticDetailsProps) => {
  const [isFinishedLoading, setIsFinishedLoading] = useState(false)
  const [isFavoriteAesthetic, setIsFavoriteAesthetic] = useState(false)
  const queryClient = useQueryClient()
  const shouldInvalidateQueries = useRef<boolean>(false)

  // Invalidate queries when favorite aesthetic is updated ( Fav Aesthetics section in main screen)
  const handleInvalidateQueries = useCallback(() => {
    queryClient.invalidateQueries({
      queryKey: [aestheticQueryKeys['favoriteAestheticsInMainScreen'].base()],
      type: 'active',
      refetchType: 'active',
      exact: false,
    })

    queryClient.invalidateQueries({
      queryKey: [aestheticQueryKeys['favoriteAesthetics'].base()],
      type: 'active',
      refetchType: 'active',
      exact: false,
    })
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const handleMessage = (e: WebViewMessageEvent) => {
    const event = JSON.parse(e.nativeEvent.data)
    switch (event.action) {
      case MESSAGE_NATIVE.finishedLoading: {
        const { payload } = event

        if (payload.isFavorite) {
          setIsFavoriteAesthetic(true)
        }
        setIsFinishedLoading(true)
        break
      }
      default:
        break
    }
  }

  useEffect(() => {
    return () => {
      if (shouldInvalidateQueries.current) {
        handleInvalidateQueries()
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])
  return (
    <SafeAreaView className="flex-1 bg-white" edges={['left', 'right', 'top']}>
      <View className="flex-1 flex-col">
        <AestheticDetailsHeader
          isFavorite={isFavoriteAesthetic}
          id={id}
          isLoading={!isFinishedLoading}
          onUpdateFavoriteAestheticSuccess={() => {
            shouldInvalidateQueries.current = true
          }}
        />
        <View className="flex-1">
          <BaseWebView
            source={{
              uri: WEBVIEW_APP_ROUTES.AESTHETICS?.path + `/${id}`,
            }}
            onMessage={handleMessage}
          />
        </View>
      </View>
    </SafeAreaView>
  )
}
