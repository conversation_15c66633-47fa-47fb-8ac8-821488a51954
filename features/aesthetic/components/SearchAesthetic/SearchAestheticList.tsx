import CloseIconDanger from '@/assets/icons/close-icon-danger.svg'
import EmptyBoxIcon from '@/assets/icons/empty-box.svg'
import { Skeleton } from '@/components/ui/Skeleton/Skeleton'
import { Text } from '@/components/ui/Text/Text'
import { primary } from '@/styles/_colors'
import React, { memo, useCallback, useEffect, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { ActivityIndicator, FlatList, RefreshControl, TouchableOpacity, View } from 'react-native'
import { useShallow } from 'zustand/react/shallow'

import { useSafeAreaInsets } from 'react-native-safe-area-context'
import { useAestheticStoreContext } from '../../contexts/AestheticStoreContext'
import { useGetInfiniteAesthetics } from '../../hooks/query/useGetInfiniteAesthetics'
import { SearchAestheticFilterItem, SearchAestheticFilterType } from '../../stores/AestheticStore'
import { Aesthetic } from '../../types'
import { AestheticItem } from '../AestheticItem/AestheticItem'
import { SelectedFilterBadge } from '../FilterAestheticBox/SelectedFilterBadge'
interface SearchAestheticListProps {
  fixedFilters?: {
    [key in SearchAestheticFilterType]?: string[]
  }
  isShowTotalAesthetics?: boolean
  featuredFilter?: {
    enabled: boolean
    isFeatured: boolean
  }
}
export const SearchAestheticList = ({
  fixedFilters,
  isShowTotalAesthetics = true,
  featuredFilter,
}: SearchAestheticListProps) => {
  const { t } = useTranslation()
  const [refreshing, setRefreshing] = useState(false)
  const { useStore } = useAestheticStoreContext()
  const {
    searchTextValue,
    searchAestheticFilters,
    toggleSearchAestheticFilter,
    hasActiveFilters,
    clearAllSearchAestheticFilters,
    resetAllAestheticFilters,
  } = useStore(
    useShallow((state) => ({
      searchTextValue: state.searchAestheticFilters[SearchAestheticFilterType.SEARCH_TEXT],
      searchAestheticFilters: state.searchAestheticFilters,
      toggleSearchAestheticFilter: state.toggleSearchAestheticFilter,
      hasActiveFilters: state.hasActiveFilters,
      clearAllSearchAestheticFilters: state.clearAllSearchAestheticFilters,
      resetAllAestheticFilters: state.resetAllAestheticFilters,
    })),
  )

  // Build query params based on filters
  const queryParams = useMemo(() => {
    // Treatment need filter
    const treatmentNeedIds = searchAestheticFilters[SearchAestheticFilterType.TREATMENT_NEED]?.map(
      (f) => f.id,
    )

    // Intervention level filter
    const interventionLevelIds = searchAestheticFilters[
      SearchAestheticFilterType.INTERVENTION_LEVEL
    ]?.map((f) => f.id)

    // Recovery time filter
    const recoveryTimeIds = searchAestheticFilters[SearchAestheticFilterType.RECOVERY_TIME]?.map(
      (f) => f.id,
    )

    // Effectiveness filter
    const effectivenessIds = searchAestheticFilters[SearchAestheticFilterType.EFFECTIVENESS]?.map(
      (f) => f.id,
    )

    // Special types filter
    const specialTypesIds = searchAestheticFilters[SearchAestheticFilterType.SPECIAL_TYPES]?.map(
      (f) => f.id,
    )

    // Body area filter
    const bodyAreaIds = searchAestheticFilters[SearchAestheticFilterType.BODY_AREA]?.map(
      (f) => f.id,
    )

    // Body parts filter
    const bodyPartsIds = searchAestheticFilters[SearchAestheticFilterType.BODY_PARTS]?.map(
      (f) => f.id,
    )

    return {
      locale: 'all',
      limit: 20,
      searchQuery: searchTextValue,
      select: {
        name: 'true',
        icon: 'true',
        image: 'true',
        interventionLevels: 'true',
        effectiveness: 'true',
        recoveryTimes: 'true',
        specialTypes: 'true',
        bodyAreas: 'true',
        bodyParts: 'true',
        treatmentNeeds: 'true',
      },
      interventionLevels: [
        ...(fixedFilters?.[SearchAestheticFilterType.INTERVENTION_LEVEL] || []),
        ...(interventionLevelIds || []),
      ],
      treatmentNeeds: [
        ...(fixedFilters?.[SearchAestheticFilterType.TREATMENT_NEED] || []),
        ...(treatmentNeedIds || []),
      ],
      recoveryTimes: [
        ...(fixedFilters?.[SearchAestheticFilterType.RECOVERY_TIME] || []),
        ...(recoveryTimeIds || []),
      ],
      effectiveness: [
        ...(fixedFilters?.[SearchAestheticFilterType.EFFECTIVENESS] || []),
        ...(effectivenessIds || []),
      ],
      specialTypes: [
        ...(fixedFilters?.[SearchAestheticFilterType.SPECIAL_TYPES] || []),
        ...(specialTypesIds || []),
      ],
      bodyAreas: [
        ...(fixedFilters?.[SearchAestheticFilterType.BODY_AREA] || []),
        ...(bodyAreaIds || []),
      ],
      bodyParts: [
        ...(fixedFilters?.[SearchAestheticFilterType.BODY_PARTS] || []),
        ...(bodyPartsIds || []),
      ],
      isFeatured: featuredFilter?.enabled ? featuredFilter?.isFeatured : undefined,
    }
  }, [searchTextValue, searchAestheticFilters, fixedFilters, featuredFilter])

  const {
    aesthetics,
    isGetAestheticsError,
    isGetAestheticsFetching,
    isGetAestheticsFetchingNextPage,
    fetchNextPage,
    hasNextPage,
    isRefetching,
    refetch,
  } = useGetInfiniteAesthetics({
    params: queryParams,
    config: {
      staleTime: 0,
      enabled: Boolean(
        fixedFilters || hasActiveFilters || (searchTextValue && searchTextValue.length > 0),
      ),
    },
  })

  // Handle pull-to-refresh
  const handleRefresh = useCallback(async () => {
    setRefreshing(true)
    try {
      await refetch()
    } finally {
      setRefreshing(false)
    }
  }, [refetch])

  // Flatten all aesthetics from all pages
  const allAesthetics = useMemo(() => {
    if (!aesthetics?.pages) return []
    return aesthetics.pages.flatMap((page) => page?.docs || [])
  }, [aesthetics])

  const data = useMemo((): ListItem[] => {
    const items: ListItem[] = []

    // Show loading skeletons if initial loading or refreshing
    if (isGetAestheticsFetching || isRefetching || refreshing) {
      items.push({ type: 'loading_skeleton' })
    } else {
      // Aesthetics as individual items
      allAesthetics.forEach((aesthetic) => {
        items.push({ type: 'aesthetic', aesthetic })
      })

      // Loading indicator for pagination
      if (isGetAestheticsFetchingNextPage) {
        items.push({ type: 'loading' })
      }
    }

    return items
  }, [
    allAesthetics,
    isGetAestheticsFetchingNextPage,
    isGetAestheticsFetching,
    isRefetching,
    refreshing,
  ])

  const handleLoadMore = useCallback(() => {
    if (hasNextPage && !isGetAestheticsFetchingNextPage) {
      fetchNextPage()
    }
  }, [hasNextPage, isGetAestheticsFetchingNextPage, fetchNextPage])

  const totalAesthetics = useMemo(() => {
    if (isGetAestheticsFetching) {
      return 0
    }
    return aesthetics?.pages[0]?.totalDocs || 0
  }, [aesthetics, isGetAestheticsFetching])

  const renderItem = useCallback(({ item }: { item: ListItem }) => {
    switch (item.type) {
      case 'aesthetic':
        return <AestheticItem aesthetic={item.aesthetic} />

      case 'loading':
        return (
          <View className="items-center py-2">
            <ActivityIndicator size="small" />
          </View>
        )

      case 'loading_skeleton':
        return (
          <View className="flex flex-col gap-y-4">
            {new Array(8).fill(0).map((_, index) => (
              <Skeleton key={index} className="h-20 w-full" />
            ))}
          </View>
        )

      default:
        return null
    }
  }, [])

  const renderHeaderComponent = useCallback(() => {
    const appliedFilters = Object.entries(searchAestheticFilters)
      .filter(
        ([key, value]) =>
          key !== SearchAestheticFilterType.SEARCH_TEXT &&
          value &&
          Array.isArray(value) &&
          value.length > 0,
      )
      .flatMap(([_, value]) => value as SearchAestheticFilterItem[])

    return (
      <View className="mb-3 gap-y-3">
        {isShowTotalAesthetics && (
          <View>
            <Text size="body3">
              {t('MES-71')} ({totalAesthetics})
            </Text>
          </View>
        )}
        {/* Active Filters */}
        {appliedFilters.length > 0 && (
          <View className="flex flex-col gap-y-2">
            <Text size="body7" variant="subdued">
              {t('MES-706')} ({appliedFilters.length}):
            </Text>
            <View className="flex flex-row flex-wrap gap-2">
              {appliedFilters.map((filter: SearchAestheticFilterItem) => (
                <SelectedFilterBadge
                  key={`${filter.type}-${filter.id}`}
                  filterItem={filter}
                  onClearFilter={(filterItem) => {
                    toggleSearchAestheticFilter(
                      filterItem.type as SearchAestheticFilterType,
                      filterItem as SearchAestheticFilterItem,
                    )
                  }}
                />
              ))}
              <TouchableOpacity
                onPress={() => clearAllSearchAestheticFilters()}
                className="flex flex-row items-center gap-x-1"
              >
                <Text size="body8" variant="error">
                  {t('MES-709')}
                </Text>
                <View className="-ml-1">
                  <CloseIconDanger width={20} height={20} />
                </View>
              </TouchableOpacity>
            </View>
          </View>
        )}
      </View>
    )
  }, [
    t,
    searchAestheticFilters,
    toggleSearchAestheticFilter,
    clearAllSearchAestheticFilters,
    totalAesthetics,
    isShowTotalAesthetics,
  ])

  const renderEmptyComponent = useCallback(() => {
    if (isGetAestheticsError) {
      return (
        <View className="items-center py-8">
          <Text size="body6" className="text-red-500">
            {t('MES-197')}
          </Text>
        </View>
      )
    }

    return (
      <View className="flex flex-col items-center justify-center gap-y-2 py-8">
        <EmptyBoxIcon />
        <Text size="body6" variant="default">
          {t('MES-967')}
        </Text>
      </View>
    )
  }, [isGetAestheticsError, t])

  const keyExtractor = useCallback((item: ListItem, index: number) => {
    if (item.type === 'aesthetic') {
      return `aesthetic-${item.aesthetic.id}-${index}`
    }
    return `${item.type}-${index}`
  }, [])
  const inset = useSafeAreaInsets()
  useEffect(() => {
    return () => {
      resetAllAestheticFilters()
    }
  }, [resetAllAestheticFilters])
  return (
    <FlatList
      data={data}
      renderItem={renderItem}
      keyExtractor={keyExtractor}
      ListHeaderComponent={renderHeaderComponent}
      onEndReached={handleLoadMore}
      onEndReachedThreshold={0.3}
      ListEmptyComponent={renderEmptyComponent}
      showsVerticalScrollIndicator={true}
      removeClippedSubviews={false}
      maxToRenderPerBatch={21}
      windowSize={21}
      initialNumToRender={21}
      contentContainerStyle={{ paddingBottom: inset.bottom + 12, paddingHorizontal: 16 }}
      ItemSeparatorComponent={Separator}
      refreshControl={
        <RefreshControl
          refreshing={refreshing}
          onRefresh={handleRefresh}
          colors={[primary['500']]}
          tintColor={primary['500']}
          progressBackgroundColor="#FFFFFF"
        />
      }
      className="flex-1"
    />
  )
}
type ListItem =
  | { type: 'aesthetic'; aesthetic: Aesthetic }
  | { type: 'loading' }
  | { type: 'loading_skeleton' }

const Separator = memo(() => {
  return <View className="h-2" />
})
Separator.displayName = 'Separator'
