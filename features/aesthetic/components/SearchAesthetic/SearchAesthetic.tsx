import { useLocalSearchParams } from 'expo-router'
import { useMemo } from 'react'
import { View } from 'react-native'
import { useShallow } from 'zustand/react/shallow'
import { useAestheticStoreContext } from '../../contexts/AestheticStoreContext'
import { SearchAestheticFilterType } from '../../stores/AestheticStore'
import { SearchAestheticHeader } from './SearchAestheticHeader'
import { SearchAestheticList } from './SearchAestheticList'

export const SearchAesthetic = () => {
  const { treatmentNeeds, specialTypes } = useLocalSearchParams()
  const { useStore } = useAestheticStoreContext()
  const { searchTextValue, hasActiveFilters } = useStore(
    useShallow((state) => ({
      searchTextValue: state.searchAestheticFilters['search_text'],
      hasActiveFilters: state.hasActiveFilters,
    })),
  )

  const shouldShowTreatmentNeedsList = useMemo(() => {
    return searchTextValue || hasActiveFilters || treatmentNeeds || specialTypes
  }, [searchTextValue, hasActiveFilters, treatmentNeeds, specialTypes])

  const shouldAutoFocusInput = useMemo(() => {
    return !Boolean(treatmentNeeds || specialTypes)
  }, [treatmentNeeds, specialTypes])

  const shouldShowTotalAesthetics = useMemo(() => {
    return !Boolean(treatmentNeeds || specialTypes)
  }, [treatmentNeeds, specialTypes])

  const fixedFilters = useMemo(() => {
    return {
      [SearchAestheticFilterType.TREATMENT_NEED]: treatmentNeeds
        ? [treatmentNeeds as string]
        : undefined,
      [SearchAestheticFilterType.SPECIAL_TYPES]: specialTypes
        ? [specialTypes as string]
        : undefined,
    }
  }, [treatmentNeeds, specialTypes])

  // Override filter types if treatment needs or special types are present
  const allowedFilterTypes = useMemo(() => {
    return treatmentNeeds || specialTypes
      ? [
          SearchAestheticFilterType.EFFECTIVENESS,
          SearchAestheticFilterType.RECOVERY_TIME,
          SearchAestheticFilterType.INTERVENTION_LEVEL,
        ]
      : undefined
  }, [treatmentNeeds, specialTypes])
  return (
    <View className="flex flex-1 flex-col gap-y-3 pt-4">
      <SearchAestheticHeader
        shouldAutoFocusInput={shouldAutoFocusInput}
        allowedFilterTypes={allowedFilterTypes}
      />
      {shouldShowTreatmentNeedsList && (
        <SearchAestheticList
          fixedFilters={fixedFilters}
          isShowTotalAesthetics={shouldShowTotalAesthetics}
        />
      )}
    </View>
  )
}
