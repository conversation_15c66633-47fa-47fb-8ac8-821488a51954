import { useAppLanguage } from '@/hooks/common/useAppLanguage'
import { cream } from '@/styles/_colors'
import React from 'react'
import { useTranslation } from 'react-i18next'
import { useGetAesthetics } from '../../hooks/query/useGetAesthetics'
import { AestheticHorizontalSection } from '../AestheticHorizontalSection/AestheticHorizontalSection'

export const AestheticFeaturedSection = () => {
  const { t } = useTranslation()
  const { primaryLanguage } = useAppLanguage()
  const { aesthetics, isGetAestheticsLoading } = useGetAesthetics({
    params: {
      locale: primaryLanguage,
      isFeatured: true,
      select: {
        name: 'true',
        icon: 'true',
      },
      limit: 8,
    },
    useQueryOptions: {
      staleTime: 0,
    },
  })

  return (
    <AestheticHorizontalSection
      title={t('MES-1014')}
      aesthetics={aesthetics?.docs}
      isLoading={isGetAestheticsLoading}
      backgroundColor={cream['50']}
    />
  )
}
