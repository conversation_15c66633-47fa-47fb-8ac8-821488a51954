import { Skeleton } from '@/components/ui/Skeleton/Skeleton'
import React from 'react'
import { FlatList } from 'react-native'

export const AestheticHorizontalListLoading = () => {
  return (
    <FlatList
      horizontal
      data={Array.from({ length: 8 }, (_, i) => i)}
      showsHorizontalScrollIndicator={false}
      contentContainerStyle={{
        gap: 12,
      }}
      renderItem={({ index }) => (
        <Skeleton key={index} className="h-[130px] w-[120px] rounded-lg bg-gray-200" />
      )}
    />
  )
}
