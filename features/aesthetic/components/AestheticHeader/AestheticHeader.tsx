import SearchInputIcon from '@/assets/icons/search-input-icon.svg'
import { Text } from '@/components/ui/Text/Text'
import { APP_ROUTES, AppRoutesEnum } from '@/routes/appRoutes'
import * as Haptics from 'expo-haptics'
import { Link } from 'expo-router'
import { useTranslation } from 'react-i18next'
import { TouchableOpacity, View } from 'react-native'
export const AestheticHeader = () => {
  const { t } = useTranslation()

  return (
    <View className="flex flex-col gap-y-4">
      {/* Search Bar */}
      <Link href={APP_ROUTES.AESTHETICS?.children?.[AppRoutesEnum.AESTHETICS_SEARCH]?.path} asChild>
        <TouchableOpacity
          className="flex-row items-center gap-2 overflow-hidden rounded-lg border border-custom-divider bg-white p-3"
          onPress={() => {
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light)
          }}
        >
          <SearchInputIcon width={18} height={18} />

          <View className="flex-1 overflow-hidden">
            <Text className="line-clamp-1" size="field1" variant="subdued">
              {t('MES-66')}
            </Text>
          </View>
        </TouchableOpacity>
      </Link>
    </View>
  )
}
