import ArrowRightIcon from '@/assets/icons/arrow-right-icon.svg'
import EmptyBoxIcon from '@/assets/icons/empty-box.svg'
import { SearchInput, SearchInputRef } from '@/components/ui/SearchInput/SearchInput'
import { Skeleton } from '@/components/ui/Skeleton/Skeleton'
import { Text } from '@/components/ui/Text/Text'
import { useAppLanguage } from '@/hooks/common/useAppLanguage'
import { StyledExpoImage } from '@/libs/styled'
import { APP_ROUTES, AppRoutesEnum } from '@/routes/appRoutes'
import { primary } from '@/styles/_colors'
import { Media } from '@/types/media.type'
import { getMediaInfo } from '@/utils/media-cms'
import * as Haptics from 'expo-haptics'
import { Link } from 'expo-router'
import { debounce } from 'lodash-es'
import { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { ActivityIndicator, FlatList, RefreshControl, TouchableOpacity, View } from 'react-native'
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context'
import { SvgUri } from 'react-native-svg'
import { useGetInfiniteAestheticTreatmentNeeds } from '../../hooks/query/useGetInfiniteAestheticTreatmentNeeds'
import { AestheticTreatmentNeed } from '../../types'
type ListItem =
  | { type: 'treatment_need'; treatmentNeed: AestheticTreatmentNeed }
  | { type: 'loading' }
  | { type: 'loading_skeleton' }

const TreatmentNeedItem = ({ treatmentNeed }: { treatmentNeed: AestheticTreatmentNeed }) => {
  const { media: iconMedia } = getMediaInfo(treatmentNeed.icon as Media)
  const { mimeType: iconMimeType, url: iconUrl } = iconMedia as Media
  return (
    <Link
      key={treatmentNeed.id}
      href={{
        pathname: APP_ROUTES.AESTHETICS?.children?.[AppRoutesEnum.AESTHETICS_SEARCH].path,
        params: {
          treatmentNeeds: treatmentNeed.id,
          customTitle: treatmentNeed.name,
        },
      }}
      asChild
    >
      <TouchableOpacity
        className="flex flex-row items-center gap-x-3 rounded-lg bg-custom-background-form px-4 py-3"
        onPress={() => {
          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light)
        }}
      >
        {iconUrl && (
          <>
            {iconMimeType === 'image/svg+xml' ? (
              <View className="h-[36px] w-[36px] flex-shrink-0 overflow-hidden rounded-[4px]">
                <SvgUri width="100%" height="100%" uri={iconUrl || ''} />
              </View>
            ) : (
              <View className="h-[36px] w-[36px] flex-shrink-0 overflow-hidden rounded-[4px]">
                <StyledExpoImage source={iconUrl} className="h-full w-full" />
              </View>
            )}
          </>
        )}
        <View className="flex-1">
          <Text size="body10" variant="default" numberOfLines={2}>
            {treatmentNeed.name}
          </Text>
        </View>
        <ArrowRightIcon width={20} height={20} />
      </TouchableOpacity>
    </Link>
  )
}

export const AestheticTreatmentNeedsScreen = () => {
  const { t } = useTranslation()
  const [searchInputValue, setSearchInputValue] = useState('')
  const [debouncedSearchValue, setDebouncedSearchValue] = useState('')
  const [refreshing, setRefreshing] = useState(false)
  const searchInputRef = useRef<SearchInputRef>(null)
  const { primaryLanguage } = useAppLanguage()
  const debouncedSearch = useMemo(
    () =>
      debounce((value: string) => {
        setDebouncedSearchValue(value)
      }, 300),
    [],
  )

  // Cleanup debounced function on unmount
  useEffect(() => {
    return () => {
      debouncedSearch.cancel()
    }
  }, [debouncedSearch])

  const params = useMemo(() => {
    return {
      locale: primaryLanguage,
      limit: 20,
      where: {
        name: {
          like: debouncedSearchValue,
        },
      },
    }
  }, [debouncedSearchValue, primaryLanguage])

  const {
    aestheticTreatmentNeeds,
    isGetAestheticTreatmentNeedsError,
    isGetAestheticTreatmentNeedsFetching,
    isGetAestheticTreatmentNeedsFetchingNextPage,
    fetchNextPage,
    hasNextPage,
    refetch,
  } = useGetInfiniteAestheticTreatmentNeeds({
    params,
    config: {
      staleTime: 0,
      refetchOnWindowFocus: true,
      refetchOnMount: true,
    },
  })

  // Flatten all treatment needs from all pages
  const allTreatmentNeeds = useMemo(() => {
    if (!aestheticTreatmentNeeds?.pages) return []
    return aestheticTreatmentNeeds.pages.flatMap((page) => page?.docs || [])
  }, [aestheticTreatmentNeeds])

  // Handle pull-to-refresh
  const handleRefresh = useCallback(async () => {
    setRefreshing(true)
    try {
      await refetch()
    } finally {
      setRefreshing(false)
    }
  }, [refetch])

  // Handle load more
  const handleLoadMore = useCallback(() => {
    if (hasNextPage && !isGetAestheticTreatmentNeedsFetchingNextPage) {
      fetchNextPage()
    }
  }, [hasNextPage, isGetAestheticTreatmentNeedsFetchingNextPage, fetchNextPage])

  const handleSearchInputChange = useCallback(
    (text: string) => {
      setSearchInputValue(text)
      debouncedSearch(text)
    },
    [debouncedSearch],
  )

  const handleClearSearchInput = useCallback(() => {
    setSearchInputValue('')
    debouncedSearch.cancel()
    setDebouncedSearchValue('')
  }, [debouncedSearch])

  // Build data array with different ListItem types
  const data = useMemo((): ListItem[] => {
    const items: ListItem[] = []

    // Show loading skeletons during initial load or refresh (but not during pagination)
    const isShowingLoadingSkeleton =
      (isGetAestheticTreatmentNeedsFetching || refreshing) &&
      !isGetAestheticTreatmentNeedsFetchingNextPage

    if (isShowingLoadingSkeleton) {
      items.push({ type: 'loading_skeleton' })
    } else {
      // Treatment needs as individual items
      allTreatmentNeeds.forEach((treatmentNeed) => {
        items.push({ type: 'treatment_need', treatmentNeed })
      })

      // Loading indicator for pagination
      if (isGetAestheticTreatmentNeedsFetchingNextPage) {
        items.push({ type: 'loading' })
      }
    }

    return items
  }, [
    allTreatmentNeeds,
    isGetAestheticTreatmentNeedsFetchingNextPage,
    isGetAestheticTreatmentNeedsFetching,
    refreshing,
  ])

  // Render item based on ListItem type
  const renderItem = useCallback(({ item }: { item: ListItem }) => {
    switch (item.type) {
      case 'treatment_need':
        return <TreatmentNeedItem treatmentNeed={item.treatmentNeed} />

      case 'loading':
        return (
          <View className="items-center py-4">
            <ActivityIndicator size="small" />
          </View>
        )

      case 'loading_skeleton':
        return (
          <View className="flex flex-col gap-y-4">
            {new Array(8).fill(0).map((_, index) => (
              <Skeleton key={index} className="h-20 w-full" />
            ))}
          </View>
        )

      default:
        return null
    }
  }, [])

  const renderEmptyComponent = useCallback(() => {
    if (isGetAestheticTreatmentNeedsError) {
      return (
        <View className="items-center py-8">
          <Text size="body6" className="text-red-500">
            {t('MES-197')}
          </Text>
        </View>
      )
    }
    if (isGetAestheticTreatmentNeedsFetching || isGetAestheticTreatmentNeedsFetchingNextPage) {
      return null
    }

    return (
      <View className="items-center py-8">
        <EmptyBoxIcon />
        <Text size="body6" variant="default">
          {t('MES-968')}
        </Text>
      </View>
    )
  }, [
    isGetAestheticTreatmentNeedsError,
    isGetAestheticTreatmentNeedsFetching,
    isGetAestheticTreatmentNeedsFetchingNextPage,
    t,
  ])

  const keyExtractor = useCallback((item: ListItem, index: number) => {
    if (item.type === 'treatment_need') {
      return `treatment_need-${item.treatmentNeed.id}`
    }
    return `${item.type}-${index}`
  }, [])

  const inset = useSafeAreaInsets()
  return (
    <SafeAreaView className="flex-1 bg-white" edges={['left', 'right']}>
      <View className="h-full flex-1 flex-col gap-y-6 bg-white pt-4">
        {/* Header */}
        <View className="flex flex-col gap-y-3 px-4">
          <View className="flex flex-col gap-y-3 bg-white">
            <View className="flex flex-col gap-y-3">
              <View className="flex flex-row items-center gap-x-2">
                {/* Search Input */}
                <SearchInput
                  ref={searchInputRef}
                  placeholder={t('MES-66')}
                  value={searchInputValue}
                  onChangeText={handleSearchInputChange}
                  onClear={handleClearSearchInput}
                  onSubmitEditing={() => {
                    // Search will be handled later
                  }}
                />
              </View>
            </View>
          </View>
        </View>

        {/* List */}
        <View className="flex-1 px-4 ">
          <FlatList
            showsVerticalScrollIndicator={true}
            data={data}
            renderItem={renderItem}
            keyExtractor={keyExtractor}
            contentContainerStyle={{
              paddingBottom: inset.bottom + 12,
            }}
            ItemSeparatorComponent={Separator}
            ListEmptyComponent={renderEmptyComponent}
            onEndReached={handleLoadMore}
            onEndReachedThreshold={0.1}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={handleRefresh}
                colors={[primary['500']]}
                tintColor={primary['500']}
                progressBackgroundColor="#FFFFFF"
              />
            }
            className="flex-1"
          />
        </View>
      </View>
    </SafeAreaView>
  )
}

const Separator = memo(() => {
  return <View className="h-2" />
})
Separator.displayName = 'Separator'
