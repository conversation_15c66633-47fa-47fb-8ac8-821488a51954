import { ScrollView, View } from 'react-native'
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context'
import { AestheticFeaturedSection } from '../../components/AestheticFeaturedSection/AestheticFeaturedSection'
import { AestheticHeader } from '../../components/AestheticHeader/AestheticHeader'
import { AestheticInjectionTherapySection } from '../../components/AestheticInjectionTherapySection/AestheticInjectionTherapySection'
import { AestheticSmartFeatures } from '../../components/AestheticSmartFeatures/AestheticSmartFeatures'
import { AestheticTreatmentNeedsSection } from '../../components/AestheticTreatmentNeedsSection/AestheticTreatmentNeedsSection'
import { FavoriteAestheticsSection } from '../../components/FavoriteAestheticsSection/FavoriteAestheticsSection'

export const AestheticScreen = () => {
  const insets = useSafeAreaInsets()
  return (
    <SafeAreaView className="flex-1 bg-white" edges={['left', 'right']}>
      <ScrollView
        contentContainerStyle={{ paddingBottom: insets.bottom + 12 }}
        showsVerticalScrollIndicator={false}
      >
        <View className="flex flex-col gap-y-4 px-4 pt-4">
          <AestheticHeader />
          <AestheticSmartFeatures />
          <AestheticTreatmentNeedsSection />
          <AestheticInjectionTherapySection />
          <AestheticFeaturedSection />
          <FavoriteAestheticsSection />
        </View>
      </ScrollView>
    </SafeAreaView>
  )
}
