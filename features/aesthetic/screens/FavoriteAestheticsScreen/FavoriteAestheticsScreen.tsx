import { SafeAreaView } from 'react-native-safe-area-context'
import { FavoriteAesthetics } from '../../components/FavoriteAesthetics/FavoriteAesthetics'
import { AestheticStoreProvider } from '../../contexts/AestheticStoreContext'
import { useFavoriteAestheticStore } from '../../stores'

export const FavoriteAestheticsScreen = () => {
  return (
    <SafeAreaView className="flex-1 bg-white" edges={['left', 'right']}>
      <AestheticStoreProvider useStore={useFavoriteAestheticStore}>
        <FavoriteAesthetics />
      </AestheticStoreProvider>
    </SafeAreaView>
  )
}
