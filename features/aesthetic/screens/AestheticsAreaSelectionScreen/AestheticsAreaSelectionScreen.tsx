import { useLocalSearchParams } from 'expo-router'
import { SafeAreaView } from 'react-native-safe-area-context'
import { AestheticsAreaSelection } from '../../components/AestheticsAreaSelection/AestheticsAreaSelection'
import { AestheticStoreProvider } from '../../contexts/AestheticStoreContext'
import { AestheticBodyAreaEnum } from '../../enums'
import { useAreaSelectionAestheticStore } from '../../stores/AestheticStore'

export const AestheticsAreaSelectionScreen = () => {
  const { bodyArea } = useLocalSearchParams()
  return (
    <SafeAreaView className="flex-1 bg-white" edges={['left', 'right']}>
      <AestheticStoreProvider useStore={useAreaSelectionAestheticStore}>
        <AestheticsAreaSelection
          bodyArea={(bodyArea as AestheticBodyAreaEnum) || AestheticBodyAreaEnum.HEAD}
        />
      </AestheticStoreProvider>
    </SafeAreaView>
  )
}
