import { MutationOptions, useMutation } from '@tanstack/react-query'
import { useRef } from 'react'
import { aestheticService } from '../../services/aesthetic.service'
import { aestheticMutationKeys } from './queryKeys'

type UpdateFavoriteAestheticVariables = { id: string; type: 'add' | 'delete' }

export const useUpdateFavoriteAesthetic = ({
  options,
}: {
  options?: Omit<MutationOptions | undefined, 'mutationFn' | 'mutationKey'>
} = {}) => {
  // Define an AbortController to cancel previous requests
  const abortControllerRef = useRef<AbortController | null>(null)

  const {
    isError: isUpdateFavoriteAestheticError,
    isPending: isUpdateFavoriteAestheticPending,
    mutate: updateFavoriteAestheticMutation,
    ...rest
  } = useMutation({
    mutationKey: aestheticMutationKeys['updateFavoriteAesthetic'].base(),
    mutationFn: async ({ id, type }: UpdateFavoriteAestheticVariables) => {
      // Abort any ongoing request before initiating a new one
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }

      // Create a new AbortController for the new request
      const abortController = new AbortController()
      abortControllerRef.current = abortController

      // Pass the signal from AbortController to the service method
      if (type === 'add') {
        return aestheticService.addFavoriteAesthetic(id, {
          signal: abortControllerRef.current.signal,
        })
      } else if (type === 'delete') {
        return aestheticService.deleteFavoriteAesthetic(id, {
          signal: abortControllerRef.current.signal,
        })
      }

      return Promise.resolve()
    },
    ...options,
  })

  return {
    isUpdateFavoriteAestheticError,
    isUpdateFavoriteAestheticPending,
    updateFavoriteAestheticMutation,
    ...rest,
  }
}
