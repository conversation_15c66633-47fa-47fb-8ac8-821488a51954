import { Params } from '@/types/http.type'
import { InfiniteData, UseInfiniteQueryOptions, useInfiniteQuery } from '@tanstack/react-query'

import { PaginatedDocs } from '@/types/global.type'
import { AxiosRequestConfig } from 'axios'
import { aestheticService } from '../../services/aesthetic.service'
import { AestheticTreatmentNeed } from '../../types'
import { aestheticQueryKeys } from './queryKeys'

export type AestheticTreatmentNeedQueryConfig = Omit<
  UseInfiniteQueryOptions<
    PaginatedDocs<AestheticTreatmentNeed> | null,
    Error,
    InfiniteData<PaginatedDocs<AestheticTreatmentNeed>>,
    (string | Params)[],
    number
  >,
  'queryFn' | 'queryKey' | 'getNextPageParam' | 'initialPageParam'
>

interface UseGetInfiniteAestheticTreatmentNeedsProps {
  params?: Params
  options?: AxiosRequestConfig
  key?: string | number
  config?: AestheticTreatmentNeedQueryConfig
  overrideKey?: (string | Params)[]
}

/**
 * Custom hook for fetching paginated aesthetic treatment needs data
 * @param params - Query parameters for filtering and pagination
 * @param options - Request initialization options
 * @param key - Optional cache key
 * @param config - React Query configuration options
 * @param overrideKey - Override the query key
 * @returns Object containing aesthetic treatment needs data and pagination controls
 */
export const useGetInfiniteAestheticTreatmentNeeds = ({
  params = {},
  options = {},
  config = {},
  overrideKey,
}: UseGetInfiniteAestheticTreatmentNeedsProps = {}) => {
  const {
    isError: isGetAestheticTreatmentNeedsError,
    isFetching: isGetAestheticTreatmentNeedsFetching,
    isFetchingNextPage: isGetAestheticTreatmentNeedsFetchingNextPage,
    data: aestheticTreatmentNeeds,
    fetchNextPage,
    hasNextPage,
    ...rest
  } = useInfiniteQuery({
    queryKey: overrideKey ? overrideKey : [aestheticQueryKeys['treatmentNeeds'].base(), params],
    queryFn: async ({ pageParam = 1 }) => {
      return aestheticService.getAestheticTreatmentNeeds({
        params: {
          ...params,
          page: pageParam,
        },
        options,
      })
    },
    getNextPageParam: (lastPage) => lastPage?.nextPage,
    getPreviousPageParam: (lastPage) => lastPage?.prevPage,
    initialPageParam: 1,
    ...config,
  })

  return {
    isGetAestheticTreatmentNeedsError,
    isGetAestheticTreatmentNeedsFetching,
    isGetAestheticTreatmentNeedsFetchingNextPage,
    aestheticTreatmentNeeds,
    fetchNextPage,
    hasNextPage,
    ...rest,
  }
}
