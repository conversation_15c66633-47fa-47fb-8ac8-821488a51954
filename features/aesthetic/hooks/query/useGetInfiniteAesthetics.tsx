import { Params } from '@/types/http.type'
import { InfiniteData, UseInfiniteQueryOptions, useInfiniteQuery } from '@tanstack/react-query'

import { PaginatedDocs } from '@/types/global.type'
import { AxiosRequestConfig } from 'axios'
import { aestheticService } from '../../services/aesthetic.service'
import { Aesthetic } from '../../types'
import { aestheticQueryKeys } from './queryKeys'

export type AestheticQueryConfig = Omit<
  UseInfiniteQueryOptions<
    PaginatedDocs<Aesthetic> | null,
    Error,
    InfiniteData<PaginatedDocs<Aesthetic>>,
    (string | Params)[],
    number
  >,
  'queryFn' | 'queryKey' | 'getNextPageParam' | 'initialPageParam'
>

interface UseGetInfiniteAestheticsProps {
  params?: Params
  options?: AxiosRequestConfig
  key?: string | number
  config?: AestheticQueryConfig
  overrideKey?: (string | Params)[]
}

/**
 * Custom hook for fetching paginated aesthetics data
 * @param params - Query parameters for filtering and pagination
 * @param options - Request initialization options
 * @param key - Optional cache key
 * @param config - React Query configuration options
 * @param overrideKey - Override the query key
 * @returns Object containing aesthetic data and pagination controls
 */
export const useGetInfiniteAesthetics = ({
  params = {},
  options = {},
  config = {},
  overrideKey,
}: UseGetInfiniteAestheticsProps = {}) => {
  const {
    isError: isGetAestheticsError,
    isFetching: isGetAestheticsFetching,
    isFetchingNextPage: isGetAestheticsFetchingNextPage,
    data: aesthetics,
    fetchNextPage,
    hasNextPage,
    ...rest
  } = useInfiniteQuery({
    queryKey: overrideKey ? overrideKey : [aestheticQueryKeys['aesthetics'].base(), params],
    queryFn: async ({ pageParam = 1 }) => {
      return aestheticService.getAesthetics({
        params: {
          ...params,
          page: pageParam,
        },
        options,
      })
    },
    getNextPageParam: (lastPage) => lastPage?.nextPage,
    getPreviousPageParam: (lastPage) => lastPage?.prevPage,
    initialPageParam: 1,
    ...config,
  })

  return {
    isGetAestheticsError,
    isGetAestheticsFetching,
    isGetAestheticsFetchingNextPage,
    aesthetics,
    fetchNextPage,
    hasNextPage,
    ...rest,
  }
}
