import { PaginatedDocs } from '@/types/global.type'
import { Params } from '@/types/http.type'
import { UseQueryOptions, useQuery } from '@tanstack/react-query'
import { AxiosRequestConfig } from 'axios'
import { aestheticService } from '../../services/aesthetic.service'
import { FavoriteAesthetic } from '../../types'
import { aestheticQueryKeys } from './queryKeys'

export const useGetFavoriteAesthetics = ({
  params = {},
  options = {},
  useQueryOptions,
  overrideKey,
}: {
  params?: Params
  options?: AxiosRequestConfig
  useQueryOptions?: Omit<
    UseQueryOptions<PaginatedDocs<FavoriteAesthetic> | null>,
    'queryKey' | 'queryFn'
  >
  overrideKey?: (string | Params)[]
} = {}) => {
  const {
    isError: isGetFavoriteAestheticsError,
    isPending: isGetFavoriteAestheticsLoading,
    data: favoriteAesthetics,
    ...rest
  } = useQuery({
    queryKey: overrideKey ? overrideKey : [aestheticQueryKeys['favoriteAesthetics'].base(), params],
    queryFn: async () =>
      await aestheticService.getFavoriteAesthetics({
        params: params,
        options: options,
      }),
    ...useQueryOptions,
  })
  return {
    isGetFavoriteAestheticsError,
    isGetFavoriteAestheticsLoading,
    favoriteAesthetics,
    ...rest,
  }
}
