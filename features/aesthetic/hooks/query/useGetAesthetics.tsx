import { PaginatedDocs } from '@/types/global.type'
import { Params } from '@/types/http.type'
import { UseQueryOptions, useQuery } from '@tanstack/react-query'
import { AxiosRequestConfig } from 'axios'
import { aestheticService } from '../../services/aesthetic.service'
import { Aesthetic } from '../../types'
import { aestheticQueryKeys } from './queryKeys'

export const useGetAesthetics = ({
  params = {},
  options = {},
  useQueryOptions,
}: {
  params?: Params
  options?: AxiosRequestConfig
  useQueryOptions?: Omit<UseQueryOptions<PaginatedDocs<Aesthetic> | null>, 'queryKey' | 'queryFn'>
} = {}) => {
  const {
    isError: isGetAestheticsError,
    isPending: isGetAestheticsLoading,
    data: aesthetics,
    ...rest
  } = useQuery({
    queryKey: [aestheticQueryKeys['aesthetics'].base(), params],
    queryFn: async () =>
      aestheticService.getAesthetics({
        params: params,
        options: options,
      }),
    ...useQueryOptions,
  })
  return {
    isGetAestheticsError,
    isGetAestheticsLoading,
    aesthetics,
    ...rest,
  }
}
