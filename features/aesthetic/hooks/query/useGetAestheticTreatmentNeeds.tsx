import { PaginatedDocs } from '@/types/global.type'
import { Params } from '@/types/http.type'
import { UseQueryOptions, useQuery } from '@tanstack/react-query'
import { AxiosRequestConfig } from 'axios'
import { aestheticService } from '../../services/aesthetic.service'
import { AestheticTreatmentNeed } from '../../types'
import { aestheticQueryKeys } from './queryKeys'

export const useGetAestheticTreatmentNeeds = ({
  params = {},
  options = {},
  useQueryOptions,
}: {
  params?: Params
  options?: AxiosRequestConfig
  useQueryOptions?: Omit<
    UseQueryOptions<PaginatedDocs<AestheticTreatmentNeed> | null>,
    'queryKey' | 'queryFn'
  >
} = {}) => {
  const {
    isError: isGetAestheticTreatmentNeedsError,
    isPending: isGetAestheticTreatmentNeedsLoading,
    data: aestheticTreatmentNeeds,
    ...rest
  } = useQuery({
    queryKey: [aestheticQueryKeys['treatmentNeeds'].base(), params],
    queryFn: async () =>
      aestheticService.getAestheticTreatmentNeeds({
        params: params,
        options: options,
      }),
    ...useQueryOptions,
  })
  return {
    isGetAestheticTreatmentNeedsError,
    isGetAestheticTreatmentNeedsLoading,
    aestheticTreatmentNeeds,
    ...rest,
  }
}
