import { Params } from '@/types/http.type'
import { InfiniteData, UseInfiniteQueryOptions, useInfiniteQuery } from '@tanstack/react-query'

import { PaginatedDocs } from '@/types/global.type'
import { AxiosRequestConfig } from 'axios'
import { aestheticService } from '../../services/aesthetic.service'
import { FavoriteAesthetic } from '../../types'
import { aestheticQueryKeys } from './queryKeys'

export type AestheticQueryConfig = Omit<
  UseInfiniteQueryOptions<
    PaginatedDocs<FavoriteAesthetic> | null,
    Error,
    InfiniteData<PaginatedDocs<FavoriteAesthetic>>,
    (string | Params)[],
    string | undefined
  >,
  'queryFn' | 'queryKey' | 'getNextPageParam' | 'initialPageParam'
>

interface UseGetInfiniteFavoriteAestheticsProps {
  params?: Params
  options?: AxiosRequestConfig
  key?: string | number
  config?: AestheticQueryConfig
  overrideKey?: (string | Params)[]
}

/**
 * Custom hook for fetching paginated aesthetics data
 * @param params - Query parameters for filtering and pagination
 * @param options - Request initialization options
 * @param key - Optional cache key
 * @param config - React Query configuration options
 * @param overrideKey - Override the query key
 * @returns Object containing aesthetic data and pagination controls
 */
export const useGetInfiniteFavoriteAesthetics = ({
  params = {},
  options = {},
  config = {},
  overrideKey,
}: UseGetInfiniteFavoriteAestheticsProps = {}) => {
  const {
    isError: isGetFavoriteAestheticsError,
    isFetching: isGetFavoriteAestheticsFetching,
    isFetchingNextPage: isGetFavoriteAestheticsFetchingNextPage,
    data: favoriteAesthetics,
    fetchNextPage,
    hasNextPage,
    ...rest
  } = useInfiniteQuery({
    queryKey: overrideKey ? overrideKey : [aestheticQueryKeys['favoriteAesthetics'].base(), params],
    queryFn: async ({ pageParam }) => {
      const result = await aestheticService.getFavoriteAesthetics({
        params: {
          ...params,
          cursor: pageParam,
        },
        options,
      })
      return result
    },
    getNextPageParam: (lastPage) => lastPage?.nextCursor || undefined,
    getPreviousPageParam: () => undefined,
    initialPageParam: undefined,
    ...config,
  })

  return {
    isGetFavoriteAestheticsError,
    isGetFavoriteAestheticsFetching,
    isGetFavoriteAestheticsFetchingNextPage,
    favoriteAesthetics,
    fetchNextPage,
    hasNextPage,
    ...rest,
  }
}
