import InvasiveIcon from '@/assets/icons/invasive-icon.svg'
import NonInvasiveIcon from '@/assets/icons/non-invasive-icon.svg'
import { LocaleEnum } from '@/enums/locale.enum'
import {
  AestheticBodyAreaEnum,
  AestheticBodyPartsEnum,
  AestheticEffectivenessEnum,
  AestheticInterventionLevelsEnum,
  AestheticRecoveryTimeEnum,
  AestheticSpecialTypesEnum,
  BodySideEnum,
} from '../enums'
export const AESTHETIC_INTERVENTION_LEVELS_OPTIONS = {
  [AestheticInterventionLevelsEnum.INVASIVE]: {
    label: 'Invasive',
    value: AestheticInterventionLevelsEnum.INVASIVE,
    fixedLabel: {
      [LocaleEnum.VI]: 'Có xâm lấn',
      [LocaleEnum.JA]: '侵襲的な',
    },
    translationKey: 'MES-1000',
    notes: {
      title: {
        fixedLabel: {
          [LocaleEnum.VI]: 'C<PERSON> xâm lấn',
          [LocaleEnum.JA]: '侵襲的な',
        },
        translationKey: 'MES-1000',
      },
      note: {
        fixedLabel: {
          [LocaleEnum.VI]:
            '<PERSON><PERSON><PERSON> thủ thuật thẩm mỹ có sự can thiệp trực tiếp vào cơ thể như cắt rạch, mổ bằng dao, kéo hoặc các dụng cụ y tế chuyên dụng. Các thủ thuật này thường cần gây mê hoặc gây tê, thời gian hồi phục lâu hơn và có thể để lại sẹo.',
          [LocaleEnum.JA]:
            '美容施術には、切開、メスやハサミによる手術、または医療専用の器具を使った直接的な体への介入が含まれます。これらの施術は通常、麻酔や局所麻酔が必要で、回復に時間がかかり、傷跡が残る可能性があります。',
        },
        translationKey: 'MES-1002',
      },
      icon: InvasiveIcon,
    },
  },
  [AestheticInterventionLevelsEnum.NON_INVASIVE]: {
    label: 'Non Invasive',
    value: AestheticInterventionLevelsEnum.NON_INVASIVE,
    fixedLabel: {
      [LocaleEnum.VI]: 'Không xâm lấn',
      [LocaleEnum.JA]: '非侵襲的',
    },
    translationKey: 'MES-1001',
    notes: {
      title: {
        fixedLabel: {
          [LocaleEnum.VI]: 'Không xâm lấn',
          [LocaleEnum.JA]: '非侵襲的な',
        },
        translationKey: 'MES-1001',
      },
      note: {
        fixedLabel: {
          [LocaleEnum.VI]:
            'Là các phương pháp làm đẹp không cần phẫu thuật, không cắt rạch da, thường sử dụng công nghệ cao hoặc tiêm chất làm đầy để cải thiện ngoại hình.',
          [LocaleEnum.JA]:
            '手術を伴わず、皮膚を切開しない美容法で、通常は高度な技術やフィラー注射を用いて外見を改善します。',
        },
        translationKey: 'MES-1003',
      },
      icon: NonInvasiveIcon,
    },
  },
}

export const AESTHETIC_RECOVERY_TIME_OPTIONS = {
  [AestheticRecoveryTimeEnum.SEVEN_TO_TEN_DAYS]: {
    label: '7-10 Days',
    value: AestheticRecoveryTimeEnum.SEVEN_TO_TEN_DAYS,
    fixedLabel: {
      [LocaleEnum.VI]: '7-10 ngày',
      [LocaleEnum.JA]: '7-10日',
    },
    translationKey: 'MES-1004',
  },
  [AestheticRecoveryTimeEnum.ONE_TO_THREE_MONTHS]: {
    label: '1-3 Months',
    value: AestheticRecoveryTimeEnum.ONE_TO_THREE_MONTHS,
    fixedLabel: {
      [LocaleEnum.VI]: '1-3 tháng',
      [LocaleEnum.JA]: '1-3ヶ月',
    },
    translationKey: 'MES-1005',
  },
  [AestheticRecoveryTimeEnum.FOUR_TO_SIX_MONTHS]: {
    label: '4-6 Months',
    value: AestheticRecoveryTimeEnum.FOUR_TO_SIX_MONTHS,
    fixedLabel: {
      [LocaleEnum.VI]: '4-6 tháng',
      [LocaleEnum.JA]: '4-6ヶ月',
    },
    translationKey: 'MES-1006',
  },
  [AestheticRecoveryTimeEnum.OVER_SIX_MONTHS]: {
    label: 'Over 6 Months',
    value: AestheticRecoveryTimeEnum.OVER_SIX_MONTHS,
    fixedLabel: {
      [LocaleEnum.VI]: 'Trên 6 tháng',
      [LocaleEnum.JA]: '6ヶ月以上',
    },
    translationKey: 'MES-1007',
  },
}

export const AESTHETIC_EFFECTIVENESS_OPTIONS = {
  [AestheticEffectivenessEnum.SHORT_TERM]: {
    label: 'Short Term',
    value: AestheticEffectivenessEnum.SHORT_TERM,
    fixedLabel: {
      [LocaleEnum.VI]: 'Ngắn hạn',
      [LocaleEnum.JA]: '短期',
    },
    translationKey: 'MES-1008',
  },
  [AestheticEffectivenessEnum.LONG_TERM]: {
    label: 'Long Term',
    value: AestheticEffectivenessEnum.LONG_TERM,
    fixedLabel: {
      [LocaleEnum.VI]: 'Dài hạn',
      [LocaleEnum.JA]: '長期',
    },
    translationKey: 'MES-1009',
  },
}

export const AESTHETIC_SPECIAL_TYPES_OPTIONS = {
  [AestheticSpecialTypesEnum.AESTHETIC_INJECTION_THERAPY]: {
    label: 'Aesthetic Injection Therapy',
    value: AestheticSpecialTypesEnum.AESTHETIC_INJECTION_THERAPY,
    fixedLabel: {
      [LocaleEnum.VI]: 'Liệu pháp tiêm thẩm mỹ',
      [LocaleEnum.JA]: '美容注射療法',
    },
    translationKey: 'MES-939',
  },
}

export const AESTHETIC_BODY_AREA_OPTIONS: Record<
  AestheticBodyAreaEnum,
  {
    label: string
    value: AestheticBodyAreaEnum
    fixedLabel: Record<LocaleEnum, string>
    translationKey: string
  }
> = {
  [AestheticBodyAreaEnum.HEAD]: {
    label: 'Head',
    value: AestheticBodyAreaEnum.HEAD,
    fixedLabel: {
      [LocaleEnum.VI]: 'Đầu',
      [LocaleEnum.JA]: '頭部',
    },
    translationKey: '',
  },
  [AestheticBodyAreaEnum.FULL_BODY]: {
    label: 'Full Body',
    value: AestheticBodyAreaEnum.FULL_BODY,
    fixedLabel: {
      [LocaleEnum.VI]: 'Toàn thân',
      [LocaleEnum.JA]: '全身',
    },
    translationKey: '',
  },
}

export const AESTHETIC_BODY_PARTS_OPTIONS: Record<
  string,
  {
    label: string
    value: AestheticBodyPartsEnum
    fixedLabel: Record<LocaleEnum, string>
    translationKey?: string
    side?: BodySideEnum
    area?: AestheticBodyAreaEnum
  }
> = {
  // FACE
  [AestheticBodyPartsEnum.HAIR]: {
    label: 'Hair',
    value: AestheticBodyPartsEnum.HAIR,
    fixedLabel: {
      [LocaleEnum.VI]: 'Tóc',
      [LocaleEnum.JA]: '髪',
    },
    translationKey: 'Hair',
    area: AestheticBodyAreaEnum.HEAD,
  },
  [AestheticBodyPartsEnum.SCAR]: {
    label: 'Scar',
    value: AestheticBodyPartsEnum.SCAR,
    fixedLabel: {
      [LocaleEnum.VI]: 'Sẹo',
      [LocaleEnum.JA]: '傷跡',
    },
    translationKey: 'Scar',
    area: AestheticBodyAreaEnum.FULL_BODY,
  },
  [AestheticBodyPartsEnum.SKIN]: {
    label: 'Skin',
    value: AestheticBodyPartsEnum.SKIN,
    fixedLabel: {
      [LocaleEnum.VI]: 'Da',
      [LocaleEnum.JA]: '肌',
    },
    translationKey: 'Skin',
    area: AestheticBodyAreaEnum.HEAD,
  },
  [AestheticBodyPartsEnum.EYEBROW]: {
    label: 'Eyebrow',
    value: AestheticBodyPartsEnum.EYEBROW,
    fixedLabel: {
      [LocaleEnum.VI]: 'Lông mày',
      [LocaleEnum.JA]: '眉毛',
    },
    translationKey: 'Eyebrow',
    area: AestheticBodyAreaEnum.HEAD,
  },
  [AestheticBodyPartsEnum.EYES]: {
    label: 'Eyes',
    value: AestheticBodyPartsEnum.EYES,
    fixedLabel: {
      [LocaleEnum.VI]: 'Mắt',
      [LocaleEnum.JA]: '目',
    },
    translationKey: 'Eyes',
    area: AestheticBodyAreaEnum.HEAD,
  },
  [AestheticBodyPartsEnum.LIPS]: {
    label: 'Lips',
    value: AestheticBodyPartsEnum.LIPS,
    fixedLabel: {
      [LocaleEnum.VI]: 'Môi',
      [LocaleEnum.JA]: '唇',
    },
    translationKey: 'Lips',
    area: AestheticBodyAreaEnum.HEAD,
  },
  [AestheticBodyPartsEnum.MOUTH]: {
    label: 'Mouth',
    value: AestheticBodyPartsEnum.MOUTH,
    fixedLabel: {
      [LocaleEnum.VI]: 'Miệng',
      [LocaleEnum.JA]: '口',
    },
    translationKey: 'Mouth',
    area: AestheticBodyAreaEnum.HEAD,
  },
  [AestheticBodyPartsEnum.NOSE]: {
    label: 'Nose',
    value: AestheticBodyPartsEnum.NOSE,
    fixedLabel: {
      [LocaleEnum.VI]: 'Mũi',
      [LocaleEnum.JA]: '鼻',
    },
    translationKey: 'Nose',
    area: AestheticBodyAreaEnum.HEAD,
  },
  [AestheticBodyPartsEnum.CHIN]: {
    label: 'Chin',
    value: AestheticBodyPartsEnum.CHIN,
    fixedLabel: {
      [LocaleEnum.VI]: 'Cằm',
      [LocaleEnum.JA]: '顎',
    },
    translationKey: 'Chin',
    area: AestheticBodyAreaEnum.HEAD,
  },
  [AestheticBodyPartsEnum.JAW]: {
    label: 'Jaw',
    value: AestheticBodyPartsEnum.JAW,
    fixedLabel: {
      [LocaleEnum.VI]: 'Hàm',
      [LocaleEnum.JA]: '顎',
    },
    translationKey: 'Jaw',
    area: AestheticBodyAreaEnum.HEAD,
  },
  [AestheticBodyPartsEnum.NECK]: {
    label: 'Neck',
    value: AestheticBodyPartsEnum.NECK,
    fixedLabel: {
      [LocaleEnum.VI]: 'Cổ',
      [LocaleEnum.JA]: '首',
    },
    translationKey: 'Neck',
    area: AestheticBodyAreaEnum.HEAD,
  },
  [AestheticBodyPartsEnum.EYELIDS]: {
    label: 'Eyelids',
    value: AestheticBodyPartsEnum.EYELIDS,
    fixedLabel: {
      [LocaleEnum.VI]: 'Mí mắt',
      [LocaleEnum.JA]: '目瞼',
    },
    translationKey: 'Eyelids',
    area: AestheticBodyAreaEnum.HEAD,
  },
  [AestheticBodyPartsEnum.EYE_BAGS]: {
    label: 'Eye Bags',
    value: AestheticBodyPartsEnum.EYE_BAGS,
    fixedLabel: {
      [LocaleEnum.VI]: 'Bọng mắt',
      [LocaleEnum.JA]: '目袋',
    },
    translationKey: 'Eye Bags',
    area: AestheticBodyAreaEnum.HEAD,
  },

  // BODY
  [AestheticBodyPartsEnum.ARMPIT]: {
    label: 'Armpit',
    value: AestheticBodyPartsEnum.ARMPIT,
    fixedLabel: {
      [LocaleEnum.VI]: 'Nách',
      [LocaleEnum.JA]: '腋',
    },
    translationKey: 'Armpit',
    side: BodySideEnum.FRONT,
    area: AestheticBodyAreaEnum.FULL_BODY,
  },
  [AestheticBodyPartsEnum.ABDOMEN]: {
    label: 'Abdomen',
    value: AestheticBodyPartsEnum.ABDOMEN,
    fixedLabel: {
      [LocaleEnum.VI]: 'Bụng',
      [LocaleEnum.JA]: '腹',
    },
    translationKey: 'Abdomen',
    side: BodySideEnum.FRONT,
    area: AestheticBodyAreaEnum.FULL_BODY,
  },
  [AestheticBodyPartsEnum.THIGH]: {
    label: 'Thigh',
    value: AestheticBodyPartsEnum.THIGH,
    fixedLabel: {
      [LocaleEnum.VI]: 'Đùi',
      [LocaleEnum.JA]: '股',
    },
    side: BodySideEnum.FRONT,
    area: AestheticBodyAreaEnum.FULL_BODY,
  },
  [AestheticBodyPartsEnum.CHEST]: {
    label: 'Chest',
    value: AestheticBodyPartsEnum.CHEST,
    fixedLabel: {
      [LocaleEnum.VI]: 'Ngực',
      [LocaleEnum.JA]: '胸',
    },
    side: BodySideEnum.FRONT,
    area: AestheticBodyAreaEnum.FULL_BODY,
  },
  [AestheticBodyPartsEnum.BODY_SKIN]: {
    label: 'Body Skin',
    value: AestheticBodyPartsEnum.BODY_SKIN,
    fixedLabel: {
      [LocaleEnum.VI]: 'Da toàn thân',
      [LocaleEnum.JA]: '全身肌',
    },
    side: BodySideEnum.FRONT,
    area: AestheticBodyAreaEnum.FULL_BODY,
  },
  [AestheticBodyPartsEnum.BIKINI_AREA]: {
    label: 'Bikini Area',
    value: AestheticBodyPartsEnum.BIKINI_AREA,
    fixedLabel: {
      [LocaleEnum.VI]: 'Vùng bikini',
      [LocaleEnum.JA]: 'ビキニエリア',
    },
    side: BodySideEnum.FRONT,
    area: AestheticBodyAreaEnum.FULL_BODY,
  },
  [AestheticBodyPartsEnum.UPPER_ARM]: {
    label: 'Upper Arm',
    value: AestheticBodyPartsEnum.UPPER_ARM,
    fixedLabel: {
      [LocaleEnum.VI]: 'Bắp tay',
      [LocaleEnum.JA]: '上腕',
    },
    side: BodySideEnum.BACK,
    area: AestheticBodyAreaEnum.FULL_BODY,
  },
  [AestheticBodyPartsEnum.TATTOO]: {
    label: 'Tattoo',
    value: AestheticBodyPartsEnum.TATTOO,
    fixedLabel: {
      [LocaleEnum.VI]: 'Hình xăm',
      [LocaleEnum.JA]: '刺青',
    },
    side: BodySideEnum.BACK,
    area: AestheticBodyAreaEnum.FULL_BODY,
  },
  [AestheticBodyPartsEnum.SCAR_BODY]: {
    label: 'Scar Body',
    value: AestheticBodyPartsEnum.SCAR_BODY,
    fixedLabel: {
      [LocaleEnum.VI]: 'Sẹo',
      [LocaleEnum.JA]: '傷跡',
    },
    side: BodySideEnum.BACK,
    area: AestheticBodyAreaEnum.FULL_BODY,
  },
  [AestheticBodyPartsEnum.FULL_BODY]: {
    label: 'Full Body',
    value: AestheticBodyPartsEnum.FULL_BODY,
    fixedLabel: {
      [LocaleEnum.VI]: 'Toàn thân',
      [LocaleEnum.JA]: '全身',
    },
    side: BodySideEnum.BACK,
    area: AestheticBodyAreaEnum.FULL_BODY,
  },
  [AestheticBodyPartsEnum.BUTTOCKS]: {
    label: 'Buttocks',
    value: AestheticBodyPartsEnum.BUTTOCKS,
    fixedLabel: {
      [LocaleEnum.VI]: 'Mông',
      [LocaleEnum.JA]: '尻',
    },
    side: BodySideEnum.BACK,
    area: AestheticBodyAreaEnum.FULL_BODY,
  },
  [AestheticBodyPartsEnum.BODY_HAIR]: {
    label: 'Body Hair',
    value: AestheticBodyPartsEnum.BODY_HAIR,
    fixedLabel: {
      [LocaleEnum.VI]: 'Lông cơ thể',
      [LocaleEnum.JA]: '体毛',
    },
    side: BodySideEnum.BACK,
    area: AestheticBodyAreaEnum.FULL_BODY,
  },
}

// Group body parts (with children)
export const AESTHETIC_BODY_PARTS_GROUP_OPTIONS = {
  [AestheticBodyPartsEnum.CHIN_AND_JAW]: {
    label: 'Chin and Jaw',
    value: AestheticBodyPartsEnum.CHIN_AND_JAW,
    fixedLabel: {
      [LocaleEnum.VI]: 'Cằm, hàm',
      [LocaleEnum.JA]: '顎',
    },
    translationKey: 'ChinAndJaw',
    area: AestheticBodyAreaEnum.HEAD,
    children: [
      AESTHETIC_BODY_PARTS_OPTIONS[AestheticBodyPartsEnum.CHIN],
      AESTHETIC_BODY_PARTS_OPTIONS[AestheticBodyPartsEnum.JAW],
    ],
  },
  [AestheticBodyPartsEnum.MOUTH_AND_LIPS]: {
    label: 'Mouth and Lips',
    value: AestheticBodyPartsEnum.MOUTH_AND_LIPS,
    fixedLabel: {
      [LocaleEnum.VI]: 'Môi, miệng',
      [LocaleEnum.JA]: '唇、口',
    },
    translationKey: 'MouthAndLips',
    area: AestheticBodyAreaEnum.HEAD,
    children: [
      AESTHETIC_BODY_PARTS_OPTIONS[AestheticBodyPartsEnum.LIPS],
      AESTHETIC_BODY_PARTS_OPTIONS[AestheticBodyPartsEnum.MOUTH],
    ],
  },
  [AestheticBodyPartsEnum.EYES]: {
    label: 'Eyes',
    value: AestheticBodyPartsEnum.EYES,
    fixedLabel: {
      [LocaleEnum.VI]: 'Mắt',
      [LocaleEnum.JA]: '目',
    },
    translationKey: 'Eyes',
    area: AestheticBodyAreaEnum.HEAD,
    children: [
      AESTHETIC_BODY_PARTS_OPTIONS[AestheticBodyPartsEnum.EYELIDS],
      AESTHETIC_BODY_PARTS_OPTIONS[AestheticBodyPartsEnum.EYE_BAGS],
    ],
  },
}

export const AESTHETIC_BODY_SIDE_OPTIONS = {
  [BodySideEnum.FRONT]: {
    label: 'Front',
    value: BodySideEnum.FRONT,
    fixedLabel: {
      [LocaleEnum.VI]: 'Trước',
      [LocaleEnum.JA]: '前面',
    },
  },
  [BodySideEnum.BACK]: {
    label: 'Back',
    value: BodySideEnum.BACK,
    fixedLabel: {
      [LocaleEnum.VI]: 'Sau',
      [LocaleEnum.JA]: '背面',
    },
  },
}
