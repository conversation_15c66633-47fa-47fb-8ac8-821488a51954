export enum AestheticInterventionLevelsEnum {
  INVASIVE = 'invasive',
  NON_INVASIVE = 'non_invasive',
}

export enum AestheticRecoveryTimeEnum {
  SEVEN_TO_TEN_DAYS = '7-10-days',
  ONE_TO_THREE_MONTHS = '1-3-months',
  FOUR_TO_SIX_MONTHS = '4-6-months',
  OVER_SIX_MONTHS = 'over-6-months',
}

export enum AestheticEffectivenessEnum {
  SHORT_TERM = 'short-term',
  LONG_TERM = 'long-term',
}

export enum AestheticSpecialTypesEnum {
  AESTHETIC_INJECTION_THERAPY = 'AESTHETIC_INJECTION_THERAPY',
}

export enum AestheticBodyAreaEnum {
  HEAD = 'head',
  FULL_BODY = 'full-body',
}
export enum AestheticBodyPartsEnum {
  // FACE
  HAIR = 'hair',
  SCAR = 'SCAR',
  SKIN = 'skin',
  EYEBROW = 'eyebrow',
  EYES = 'eyes',
  LIPS = 'lips',
  MOUTH = 'mouth',
  NOSE = 'nose',
  CHIN = 'chin',
  JAW = 'jaw',
  NECK = 'neck',
  CHIN_AND_JAW = 'chin_and_jaw',
  MOUTH_AND_LIPS = 'mouth_and_lips',
  EYELIDS = 'eyelids',
  EYE_BAGS = 'eye_bags',

  // BODY
  ARMPIT = 'armpit',
  ABDOMEN = 'abdomen',
  THIGH = 'thigh',
  CHEST = 'chest',
  BODY_SKIN = 'body_skin',
  BIKINI_AREA = 'bikini_area',
  UPPER_ARM = 'upper_arm',
  TATTOO = 'tattoo',
  SCAR_BODY = 'scar_body',
  FULL_BODY = 'full_body',
  BUTTOCKS = 'buttocks',
  BODY_HAIR = 'body_hair',
}

export enum BodySideEnum {
  FRONT = 'front',
  BACK = 'back',
}
