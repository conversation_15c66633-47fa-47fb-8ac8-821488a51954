import { Media } from '@/types/media.type'
import { User } from '@/types/user.type'
import {
  AestheticBodyPartsEnum,
  AestheticEffectivenessEnum,
  AestheticInterventionLevelsEnum,
  AestheticRecoveryTimeEnum,
  AestheticSpecialTypesEnum,
} from '../enums'

export interface AestheticTreatmentNeed {
  id: string
  name: string
  icon?: (string | null) | Media
  updatedAt: string
  createdAt: string
}

export interface Aesthetic {
  id: string
  name: string
  image: string | Media
  icon?: (string | null) | Media
  treatmentNeeds?: (string | AestheticTreatmentNeed)[] | null
  interventionLevels: AestheticInterventionLevelsEnum
  recoveryTime: AestheticRecoveryTimeEnum
  effectiveness: AestheticEffectivenessEnum
  bodyParts: AestheticBodyPartsEnum
  content: {
    root: {
      type: string
      children: {
        type: string
        version: number
        [k: string]: unknown
      }[]
      direction: ('ltr' | 'rtl') | null
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | ''
      indent: number
      version: number
    }
    [k: string]: unknown
  }
  isFeatured?: boolean | null
  specialTypes?: AestheticSpecialTypesEnum[] | null
  updatedAt: string
  createdAt: string
  isFavorite?: boolean | null
}

export interface AddFavoriteAestheticResponse {
  aesthetic: string
  message: string
}

export interface DeleteFavoriteAestheticResponse {
  aesthetic: string
  message: string
}

export interface FavoriteAesthetic {
  id: string
  name?: string | null
  user?: (string | null) | User
  aesthetic: string | Aesthetic
  updatedAt: string
  createdAt: string
}
