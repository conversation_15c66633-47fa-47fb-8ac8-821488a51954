import { create } from 'zustand'
import { devtools } from 'zustand/middleware'

export interface AestheticState {
  searchAestheticFilters: SearchAestheticFilter
  tempSearchFilters: TempSearchAestheticFilter
  hasActiveFilters: boolean
}

export enum SearchAestheticFilterType {
  INTERVENTION_LEVEL = 'intervention_level',
  RECOVERY_TIME = 'recovery_time',
  EFFECTIVENESS = 'effectiveness',
  SPECIAL_TYPES = 'special_types',
  BODY_AREA = 'body_area',
  BODY_PARTS = 'body_parts',
  TREATMENT_NEED = 'treatment_need',
  SEARCH_TEXT = 'search_text',
}

export interface SearchAestheticFilterItem {
  id: string
  label: string
  type: SearchAestheticFilterType
}

export interface SearchAestheticFilter {
  [SearchAestheticFilterType.INTERVENTION_LEVEL]: SearchAestheticFilterItem[] | null
  [SearchAestheticFilterType.RECOVERY_TIME]: SearchAestheticFilterItem[] | null
  [SearchAestheticFilterType.EFFECTIVENESS]: SearchAestheticFilterItem[] | null
  [SearchAestheticFilterType.SPECIAL_TYPES]: SearchAestheticFilterItem[] | null
  [SearchAestheticFilterType.BODY_AREA]: SearchAestheticFilterItem[] | null
  [SearchAestheticFilterType.BODY_PARTS]: SearchAestheticFilterItem[] | null
  [SearchAestheticFilterType.TREATMENT_NEED]: SearchAestheticFilterItem[] | null
  [SearchAestheticFilterType.SEARCH_TEXT]: string | null
}

export interface TempSearchAestheticFilter {
  [SearchAestheticFilterType.INTERVENTION_LEVEL]: SearchAestheticFilterItem[] | null
  [SearchAestheticFilterType.RECOVERY_TIME]: SearchAestheticFilterItem[] | null
  [SearchAestheticFilterType.EFFECTIVENESS]: SearchAestheticFilterItem[] | null
  [SearchAestheticFilterType.SPECIAL_TYPES]: SearchAestheticFilterItem[] | null
  [SearchAestheticFilterType.BODY_AREA]: SearchAestheticFilterItem[] | null
  [SearchAestheticFilterType.BODY_PARTS]: SearchAestheticFilterItem[] | null
  [SearchAestheticFilterType.TREATMENT_NEED]: SearchAestheticFilterItem[] | null
}

export interface AestheticActions {
  toggleSearchAestheticFilter: (
    filterType: SearchAestheticFilterType,
    filter: SearchAestheticFilterItem,
  ) => void
  clearSearchAestheticFilters: (filterType?: SearchAestheticFilterType) => void
  setSearchTextValue: (searchTextValue: string) => void
  toggleTempSearchFilter: (
    filterType: SearchAestheticFilterType,
    filter: SearchAestheticFilterItem,
  ) => void
  applyTempSearchFilters: () => void
  resetTempSearchFilters: () => void
  initTempSearchFilters: () => void
  clearAllSearchAestheticFiltersAndSearchText: () => void
  clearAllTempSearchFilters: () => void
  clearAllSearchAestheticFilters: () => void
  resetAllAestheticFilters: () => void
}

// Initial state for the aesthetic store
const initialState: AestheticState = {
  hasActiveFilters: false,
  searchAestheticFilters: {
    [SearchAestheticFilterType.INTERVENTION_LEVEL]: null,
    [SearchAestheticFilterType.RECOVERY_TIME]: null,
    [SearchAestheticFilterType.EFFECTIVENESS]: null,
    [SearchAestheticFilterType.SPECIAL_TYPES]: null,
    [SearchAestheticFilterType.BODY_AREA]: null,
    [SearchAestheticFilterType.BODY_PARTS]: null,
    [SearchAestheticFilterType.TREATMENT_NEED]: null,
    [SearchAestheticFilterType.SEARCH_TEXT]: null,
  },
  tempSearchFilters: {
    [SearchAestheticFilterType.INTERVENTION_LEVEL]: null,
    [SearchAestheticFilterType.RECOVERY_TIME]: null,
    [SearchAestheticFilterType.EFFECTIVENESS]: null,
    [SearchAestheticFilterType.SPECIAL_TYPES]: null,
    [SearchAestheticFilterType.BODY_AREA]: null,
    [SearchAestheticFilterType.BODY_PARTS]: null,
    [SearchAestheticFilterType.TREATMENT_NEED]: null,
  },
}

// Helper function to check if search filters are active
const hasActiveSearchFilters = (filters: SearchAestheticFilter): boolean => {
  return Object.entries(filters).some(([key, value]) => {
    if (key === SearchAestheticFilterType.SEARCH_TEXT) {
      return false
    }
    return value && Array.isArray(value) && value.length > 0
  })
}

/**
 * Factory function to create a new AestheticStore instance.
 * Use this when you need multiple independent store instances.
 *
 * @param storeName - Optional name for the store (used in devtools). Defaults to 'aesthetic-store'
 * @returns A new Zustand store hook
 *
 * @example
 * // Create multiple instances
 * const useAestheticStore1 = createAestheticStore('aesthetic-store-1')
 * const useAestheticStore2 = createAestheticStore('aesthetic-store-2')
 *
 * // Use in components
 * const MyComponent = () => {
 *   const { searchAestheticFilters } = useAestheticStore1()
 *   // ... component logic
 * }
 */
export const createAestheticStore = (storeName: string = 'aesthetic-store') => {
  return create<AestheticState & AestheticActions>()(
    devtools(
      (set) => ({
        ...initialState,
        toggleSearchAestheticFilter: (
          filterType: SearchAestheticFilterType,
          filter: SearchAestheticFilterItem,
        ) => {
          set((state) => {
            // Handle different filter types
            if (filterType === SearchAestheticFilterType.SEARCH_TEXT) {
              return state
            }

            const currentFilters =
              (state.searchAestheticFilters[filterType] as SearchAestheticFilterItem[] | null) || []
            const isSelected = currentFilters.some(
              (f: SearchAestheticFilterItem) => f.id === filter.id,
            )

            const newSearchFilters = {
              ...state.searchAestheticFilters,
              [filterType]: isSelected
                ? currentFilters.filter((f: SearchAestheticFilterItem) => f.id !== filter.id)
                : [...currentFilters, filter],
            }

            return {
              searchAestheticFilters: newSearchFilters,
              hasActiveFilters: hasActiveSearchFilters(newSearchFilters),
            }
          })
        },
        clearSearchAestheticFilters: (filterType?: SearchAestheticFilterType) => {
          set((state) => {
            const newSearchFilters = filterType
              ? {
                  ...state.searchAestheticFilters,
                  [filterType]: null,
                }
              : {
                  ...initialState.searchAestheticFilters,
                  [SearchAestheticFilterType.SEARCH_TEXT]:
                    state.searchAestheticFilters[SearchAestheticFilterType.SEARCH_TEXT],
                }

            return {
              searchAestheticFilters: newSearchFilters,
              hasActiveFilters: hasActiveSearchFilters(newSearchFilters),
            }
          })
        },
        setSearchTextValue: (searchTextValue: string) => {
          set((state) => {
            const newSearchFilters = {
              ...state.searchAestheticFilters,
              [SearchAestheticFilterType.SEARCH_TEXT]: searchTextValue,
            }

            return {
              searchAestheticFilters: newSearchFilters,
              hasActiveFilters: hasActiveSearchFilters(newSearchFilters),
            }
          })
        },
        toggleTempSearchFilter: (
          filterType: SearchAestheticFilterType,
          filter: SearchAestheticFilterItem,
        ) => {
          set((state) => {
            // Only handle filter types that are arrays
            if (filterType === SearchAestheticFilterType.SEARCH_TEXT) {
              return state
            }

            // Type assertion to ensure we're working with array types
            const currentTempFilters =
              (state.tempSearchFilters[filterType] as SearchAestheticFilterItem[] | null) || []
            const isSelected = currentTempFilters.some(
              (f: SearchAestheticFilterItem) => f.id === filter.id,
            )

            const newTempFilters = {
              ...state.tempSearchFilters,
              [filterType]: isSelected
                ? currentTempFilters.filter((f: SearchAestheticFilterItem) => f.id !== filter.id)
                : [...currentTempFilters, filter],
            }

            return {
              tempSearchFilters: newTempFilters,
            }
          })
        },
        applyTempSearchFilters: () => {
          set((state) => {
            const newSearchFilters = {
              ...state.searchAestheticFilters,
              [SearchAestheticFilterType.INTERVENTION_LEVEL]:
                state.tempSearchFilters[SearchAestheticFilterType.INTERVENTION_LEVEL],
              [SearchAestheticFilterType.RECOVERY_TIME]:
                state.tempSearchFilters[SearchAestheticFilterType.RECOVERY_TIME],
              [SearchAestheticFilterType.EFFECTIVENESS]:
                state.tempSearchFilters[SearchAestheticFilterType.EFFECTIVENESS],
              [SearchAestheticFilterType.SPECIAL_TYPES]:
                state.tempSearchFilters[SearchAestheticFilterType.SPECIAL_TYPES],
              [SearchAestheticFilterType.BODY_AREA]:
                state.tempSearchFilters[SearchAestheticFilterType.BODY_AREA],
              [SearchAestheticFilterType.BODY_PARTS]:
                state.tempSearchFilters[SearchAestheticFilterType.BODY_PARTS],
              [SearchAestheticFilterType.TREATMENT_NEED]:
                state.tempSearchFilters[SearchAestheticFilterType.TREATMENT_NEED],
              [SearchAestheticFilterType.SEARCH_TEXT]:
                state.searchAestheticFilters[SearchAestheticFilterType.SEARCH_TEXT],
            }

            return {
              searchAestheticFilters: newSearchFilters,
              hasActiveFilters: hasActiveSearchFilters(newSearchFilters),
            }
          })
        },
        resetTempSearchFilters: () => {
          set((state) => ({
            tempSearchFilters: {
              [SearchAestheticFilterType.INTERVENTION_LEVEL]:
                state.searchAestheticFilters[SearchAestheticFilterType.INTERVENTION_LEVEL],
              [SearchAestheticFilterType.RECOVERY_TIME]:
                state.searchAestheticFilters[SearchAestheticFilterType.RECOVERY_TIME],
              [SearchAestheticFilterType.EFFECTIVENESS]:
                state.searchAestheticFilters[SearchAestheticFilterType.EFFECTIVENESS],
              [SearchAestheticFilterType.SPECIAL_TYPES]:
                state.searchAestheticFilters[SearchAestheticFilterType.SPECIAL_TYPES],
              [SearchAestheticFilterType.BODY_AREA]:
                state.searchAestheticFilters[SearchAestheticFilterType.BODY_AREA],
              [SearchAestheticFilterType.BODY_PARTS]:
                state.searchAestheticFilters[SearchAestheticFilterType.BODY_PARTS],
              [SearchAestheticFilterType.TREATMENT_NEED]:
                state.searchAestheticFilters[SearchAestheticFilterType.TREATMENT_NEED],
            },
          }))
        },
        clearAllTempSearchFilters: () => {
          set({
            tempSearchFilters: initialState.tempSearchFilters,
          })
        },
        initTempSearchFilters: () => {
          set((state) => ({
            tempSearchFilters: {
              [SearchAestheticFilterType.INTERVENTION_LEVEL]:
                state.searchAestheticFilters[SearchAestheticFilterType.INTERVENTION_LEVEL],
              [SearchAestheticFilterType.RECOVERY_TIME]:
                state.searchAestheticFilters[SearchAestheticFilterType.RECOVERY_TIME],
              [SearchAestheticFilterType.EFFECTIVENESS]:
                state.searchAestheticFilters[SearchAestheticFilterType.EFFECTIVENESS],
              [SearchAestheticFilterType.SPECIAL_TYPES]:
                state.searchAestheticFilters[SearchAestheticFilterType.SPECIAL_TYPES],
              [SearchAestheticFilterType.BODY_AREA]:
                state.searchAestheticFilters[SearchAestheticFilterType.BODY_AREA],
              [SearchAestheticFilterType.BODY_PARTS]:
                state.searchAestheticFilters[SearchAestheticFilterType.BODY_PARTS],
              [SearchAestheticFilterType.TREATMENT_NEED]:
                state.searchAestheticFilters[SearchAestheticFilterType.TREATMENT_NEED],
            },
          }))
        },
        clearAllSearchAestheticFiltersAndSearchText: () => {
          set({
            searchAestheticFilters: initialState.searchAestheticFilters,
            tempSearchFilters: initialState.tempSearchFilters,
            hasActiveFilters: false,
          })
        },
        clearAllSearchAestheticFilters: () => {
          set((state) => ({
            searchAestheticFilters: {
              ...initialState.searchAestheticFilters,
              [SearchAestheticFilterType.SEARCH_TEXT]:
                state.searchAestheticFilters[SearchAestheticFilterType.SEARCH_TEXT],
            },
            tempSearchFilters: {
              ...state.tempSearchFilters,
            },
            hasActiveFilters: false,
          }))
        },
        resetAllAestheticFilters: () => {
          set(initialState)
        },
      }),
      {
        name: storeName,
      },
    ),
  )
}

// Default global store
export const useAestheticStore = createAestheticStore()

// Area selection store
export const useAreaSelectionAestheticStore = createAestheticStore('area-selection-aesthetic-store')

// Favorite aesthetic store
export const useFavoriteAestheticStore = createAestheticStore('favorite-aesthetic-store')
