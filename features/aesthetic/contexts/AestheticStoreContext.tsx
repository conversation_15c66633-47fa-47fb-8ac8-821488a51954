import React, { createContext, useContext, type ReactNode } from 'react'
import type { UseBoundStore, StoreApi } from 'zustand'
import type { AestheticActions, AestheticState } from '../stores/AestheticStore'
import { useAestheticStore } from '../stores/AestheticStore'

export type AestheticStoreHook = UseBoundStore<StoreApi<AestheticState & AestheticActions>>

interface AestheticStoreContextValue {
  useStore: AestheticStoreHook
}

const AestheticStoreContext = createContext<AestheticStoreContextValue | null>(null)

interface AestheticStoreProviderProps {
  children: ReactNode
  useStore?: AestheticStoreHook
}

export const AestheticStoreProvider = ({
  children,
  useStore = useAestheticStore,
}: AestheticStoreProviderProps) => {
  return (
    <AestheticStoreContext.Provider value={{ useStore }}>{children}</AestheticStoreContext.Provider>
  )
}

export const useAestheticStoreContext = (): AestheticStoreContextValue => {
  const context = useContext(AestheticStoreContext)
  if (!context) {
    // Fallback to global store if no provider (backward compatibility)
    return { useStore: useAestheticStore }
  }
  return context
}
