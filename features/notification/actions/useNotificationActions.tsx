import { analyticsService } from '@/analytics/services/analytics.service'
import { useCallback } from 'react'
import { PushNotificationMessagesTypeEnum } from '../enums/push-notification.enum'
import { useMarkNotificationAsViewed } from '../hooks/query/useMarkNotificationAsViewed'
import {
  CommentReplyNotificationBaseData,
  NewCommentNotificationBaseData,
  NewPostNotificationBaseData,
  NewProductNotificationBaseData,
  NotificationBaseData,
  UserPushNotification,
} from '../types'
import { useAppSystemNotiAction } from './useAppSystemNotiAction'
import { useChatMessageNotiAction } from './useChatMessageNotiAction'
import { useCommentReplyAction } from './useCommentReplyAction'
import { useGeneralNotiAction } from './useGeneralNotiAction'
import { useNewCommentAction } from './useNewCommentAction'
import { useNewPostNotiAction } from './useNewPostNotiAction'
import { useNewProductNotiAction } from './useNewProductNotiAction'

/**
 * Central notification action handler that routes to specific handlers based on notification type
 */
export const useNotificationActions = () => {
  const { handleOpenNewPostNoti } = useNewPostNotiAction()
  const { handleOpenNewProductNoti } = useNewProductNotiAction()
  const { handleOpenChatMessageNoti } = useChatMessageNotiAction()
  const { handleOpenAppSystemNoti } = useAppSystemNotiAction()
  const { handleOpenGeneralNoti } = useGeneralNotiAction()
  const { handleOpenCommentReplyNoti } = useCommentReplyAction()
  const { handleOpenNewCommentNoti } = useNewCommentAction()
  const { markNotificationAsViewedMutation } = useMarkNotificationAsViewed()

  const handleNotificationAction = useCallback(
    (notification: UserPushNotification) => {
      const { type, data, viewed } = notification
      const { trackingId } = data as NotificationBaseData

      switch (type) {
        case PushNotificationMessagesTypeEnum.NEW_POST:
          if (data && 'postSlug' in data && 'postId' in data) {
            handleOpenNewPostNoti(
              (data as NewPostNotificationBaseData).postSlug,
              (data as NewPostNotificationBaseData).postId,
            )
            if (trackingId && !viewed) {
              markNotificationAsViewedMutation({ trackingId: trackingId })
            }
          }
          break

        case PushNotificationMessagesTypeEnum.NEW_PRODUCT:
          if (data && 'productSlug' in data) {
            handleOpenNewProductNoti((data as NewProductNotificationBaseData).productSlug)
            if (trackingId && !viewed) {
              markNotificationAsViewedMutation({ trackingId: trackingId })
            }
          }
          break

        case PushNotificationMessagesTypeEnum.CHAT_MESSAGE:
          {
            handleOpenChatMessageNoti()
            if (trackingId && !viewed) {
              markNotificationAsViewedMutation({ trackingId: trackingId })
            }
          }
          break

        case PushNotificationMessagesTypeEnum.APP_SYSTEM:
          handleOpenAppSystemNoti(notification)
          if (trackingId && !viewed) {
            markNotificationAsViewedMutation({ trackingId: trackingId })
          }
          break
        case PushNotificationMessagesTypeEnum.COMMENT_REPLY:
          handleOpenCommentReplyNoti(data as CommentReplyNotificationBaseData)
          if (trackingId && !viewed) {
            markNotificationAsViewedMutation({ trackingId: trackingId })
          }
          break
        case PushNotificationMessagesTypeEnum.NEW_COMMENT:
          handleOpenNewCommentNoti(data as NewCommentNotificationBaseData)
          if (trackingId && !viewed) {
            markNotificationAsViewedMutation({ trackingId: trackingId })
          }
          break
        case PushNotificationMessagesTypeEnum.GENERAL:
        default:
          handleOpenGeneralNoti(notification)
          if (trackingId && !viewed) {
            markNotificationAsViewedMutation({ trackingId: trackingId })
          }
          break
      }
    },
    [
      handleOpenNewPostNoti,
      handleOpenNewProductNoti,
      handleOpenChatMessageNoti,
      handleOpenAppSystemNoti,
      handleOpenGeneralNoti,
      handleOpenCommentReplyNoti,
      handleOpenNewCommentNoti,
      markNotificationAsViewedMutation,
    ],
  )

  /**
   * Handle notification action from FCM message data
   * This is used when handling background/foreground FCM messages
   */
  const handleNotificationActionFromData = useCallback(
    (data: any) => {
      if (!data || !data.type) {
        console.warn('No notification type found in data')
        return
      }

      const { trackingId } = data

      switch (data.type) {
        case PushNotificationMessagesTypeEnum.NEW_POST:
          if (data.postSlug && data.postId) {
            handleOpenNewPostNoti(data.postSlug, data.postId)
            if (trackingId) {
              markNotificationAsViewedMutation({ trackingId: trackingId })
              try {
                void analyticsService.logNotificationOpen(
                  PushNotificationMessagesTypeEnum.NEW_POST,
                  {
                    type: PushNotificationMessagesTypeEnum.NEW_POST,
                    topic: data?.topic,
                    postId: data?.postId,
                    postSlug: data?.postSlug,
                  },
                )
              } catch (_) {}
            }
          }
          break

        case PushNotificationMessagesTypeEnum.NEW_PRODUCT:
          if (data.productSlug) {
            handleOpenNewProductNoti(data.productSlug)
            if (trackingId) {
              markNotificationAsViewedMutation({ trackingId: trackingId })
              try {
                void analyticsService.logNotificationOpen(
                  PushNotificationMessagesTypeEnum.NEW_PRODUCT,
                  {
                    type: PushNotificationMessagesTypeEnum.NEW_PRODUCT,
                    topic: data?.topic,
                    productId: data?.productId,
                    productSlug: data?.productSlug,
                  },
                )
              } catch (_) {}
            }
          }
          break

        case PushNotificationMessagesTypeEnum.CHAT_MESSAGE:
          {
            handleOpenChatMessageNoti()
            if (trackingId) {
              markNotificationAsViewedMutation({ trackingId: trackingId })
            }
          }
          break

        case PushNotificationMessagesTypeEnum.APP_SYSTEM:
          handleOpenAppSystemNoti(data)
          if (trackingId) {
            markNotificationAsViewedMutation({ trackingId: trackingId })
            try {
              void analyticsService.logNotificationOpen(
                PushNotificationMessagesTypeEnum.APP_SYSTEM,
                {
                  type: PushNotificationMessagesTypeEnum.APP_SYSTEM,
                  title: data?.title,
                  description: data?.description,
                  topic: data?.topic,
                },
              )
            } catch (_) {}
          }
          break

        case PushNotificationMessagesTypeEnum.COMMENT_REPLY:
          handleOpenCommentReplyNoti(data as CommentReplyNotificationBaseData)
          if (trackingId) {
            markNotificationAsViewedMutation({ trackingId: trackingId })
          }
          break
        case PushNotificationMessagesTypeEnum.NEW_COMMENT:
          handleOpenNewCommentNoti(data as NewCommentNotificationBaseData)
          if (trackingId) {
            markNotificationAsViewedMutation({ trackingId: trackingId })
          }
          break
        case PushNotificationMessagesTypeEnum.GENERAL:
        default:
          handleOpenGeneralNoti(data)
          if (trackingId) {
            markNotificationAsViewedMutation({ trackingId: trackingId })
            try {
              void analyticsService.logNotificationOpen(PushNotificationMessagesTypeEnum.GENERAL, {
                type: PushNotificationMessagesTypeEnum.GENERAL,
                title: data?.title,
                description: data?.description,
                topic: data?.topic,
              })
            } catch (_) {}
          }
          break
      }
    },
    [
      handleOpenNewPostNoti,
      handleOpenNewProductNoti,
      handleOpenChatMessageNoti,
      handleOpenAppSystemNoti,
      handleOpenGeneralNoti,
      handleOpenCommentReplyNoti,
      handleOpenNewCommentNoti,
      markNotificationAsViewedMutation,
    ],
  )

  return {
    handleNotificationAction,
    handleNotificationActionFromData,
  }
}
