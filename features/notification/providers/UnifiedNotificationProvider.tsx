import { LocaleEnum } from '@/enums/locale.enum'
import { useAuthentication } from '@/hooks/auth/useAuthentication'
import { useAppLanguage } from '@/hooks/common/useAppLanguage'
import * as Notifications from 'expo-notifications'
import { useRootNavigationState } from 'expo-router'
import React, { useEffect, useRef } from 'react'
import { AppState, AppStateStatus } from 'react-native'
import { useNotificationActions } from '../actions/useNotificationActions'
import { useAddNotificationToCache } from '../hooks/common/useAddNotificationToCache'
import { useInvalidateNotificationQuery } from '../hooks/common/useInvalidateNotificationQuery'
import { unifiedNotificationService } from '../services/unified-notification.service'
import { useUnifiedNotificationStore } from '../stores/UnifiedNotificationStore'

interface UnifiedNotificationProviderProps {
  children: React.ReactNode
}

export const UnifiedNotificationProvider: React.FC<UnifiedNotificationProviderProps> = ({
  children,
}) => {
  const rootNavigationState = useRootNavigationState()

  const { status: authStatus, user } = useAuthentication()
  const { primaryLanguage } = useAppLanguage()
  const {
    initializeNotifications,
    updateAuthNotifiableStatus,
    updateNotificationLanguage,
    attachUserToToken,
    setAuthNotifiable,
    setUserAttached,

    setFcmToken,
    setFcmIsRegistered,
    setFcmError,
    setFcmPermissionGranted,
    language,
    userAttached,
    pushToken,
    pushTokenIsValid,
  } = useUnifiedNotificationStore()

  const initializationRef = useRef(false)
  const tokenCreationTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null)
  const fcmHandlersSetupRef = useRef(false) // Track if FCM handlers are already set up
  const expoNotificationListenerRef = useRef<Notifications.EventSubscription | null>(null)
  const authNotifiableUpdatedRef = useRef(false) // Track if authNotifiable has been updated for current session
  const appStateRef = useRef<AppStateStatus>(AppState.currentState)

  // ACTIONS

  const { handleNotificationActionFromData } = useNotificationActions()
  const { handleResetAllNotificationQueries, handleRemoveAllNotificationQueries } =
    useInvalidateNotificationQuery()
  // Initialize notifications on mount with debouncing
  useEffect(() => {
    const initNotifications = async () => {
      if (initializationRef.current) return
      initializationRef.current = true

      try {
        await initializeNotifications(primaryLanguage as LocaleEnum)
      } catch (error) {
        console.error('Failed to initialize notifications:', error)
      }
    }

    // Clear any existing timeout
    if (tokenCreationTimeoutRef.current) {
      clearTimeout(tokenCreationTimeoutRef.current)
    }

    // Debounce initialization to prevent race conditions
    tokenCreationTimeoutRef.current = setTimeout(() => {
      initNotifications()
    }, 100) // 100ms debounce

    return () => {
      if (tokenCreationTimeoutRef.current) {
        clearTimeout(tokenCreationTimeoutRef.current)
      }
    }
  }, [initializeNotifications, primaryLanguage])

  // Handle user attachment to token - consolidated logic
  useEffect(() => {
    const handleUserAttachment = async () => {
      if (!initializationRef.current) return

      // Only attach user if they are logged in, we have a valid token, and user is not already attached
      if (authStatus === 'success' && user && !userAttached) {
        const { pushToken, pushTokenIsValid } = useUnifiedNotificationStore.getState()
        if (pushToken && pushTokenIsValid) {
          try {
            await attachUserToToken()
          } catch (error) {
            console.error('Failed to attach user to token:', error)
          }
        }
      }
    }

    handleUserAttachment()
  }, [authStatus, user, userAttached, attachUserToToken])

  // Handle authentication status changes
  useEffect(() => {
    const handleAuthStatusChange = async () => {
      if (!initializationRef.current) return

      try {
        const { pushToken, pushTokenIsValid } = useUnifiedNotificationStore.getState()

        switch (authStatus) {
          case 'success':
            if (user) {
              console.log('🔐 User authenticated successfully, setting authNotifiable to TRUE')
              setAuthNotifiable(true)

              // Update backend if we have a valid push token
              if (pushToken && pushTokenIsValid) {
                console.log('📡 Updating authNotifiable=true on backend...')
                await updateAuthNotifiableStatus(true)
                console.log('✅ Backend updated: authNotifiable=true')
                authNotifiableUpdatedRef.current = true // Mark as updated
                handleResetAllNotificationQueries()
              } else {
                console.log('⚠️ No valid token yet, will update authNotifiable when token is ready')
                authNotifiableUpdatedRef.current = false // Reset flag to retry when token is ready
              }
            }
            break

          case 'unauthorized':
            console.log('🔐 User unauthorized, setting authNotifiable to FALSE')
            setAuthNotifiable(false)
            setUserAttached(false) // Reset user attachment flag
            authNotifiableUpdatedRef.current = false // Reset flag for next login

            // Update backend if we have a valid push token
            if (pushToken && pushTokenIsValid) {
              console.log('📡 Updating authNotifiable=false on backend...')
              await updateAuthNotifiableStatus(false)
              console.log('✅ Backend updated: authNotifiable=false')
              handleRemoveAllNotificationQueries()
            } else {
              console.log('⚠️ No valid token, skipping backend update')
            }
            break

          case 'failure':
            console.log('🔐 User authentication failed, setting authNotifiable to FALSE')
            setAuthNotifiable(false)
            setUserAttached(false) // Reset user attachment flag
            authNotifiableUpdatedRef.current = false // Reset flag

            // Update backend if we have a valid push token
            if (pushToken && pushTokenIsValid) {
              console.log('📡 Updating authNotifiable=false on backend...')
              await updateAuthNotifiableStatus(false)
              console.log('✅ Backend updated: authNotifiable=false')
            } else {
              console.log('⚠️ No valid token, skipping backend update')
            }
            break
        }
      } catch (error) {
        console.error('❌ Failed to handle auth status change:', error)
      }
    }

    handleAuthStatusChange()
  }, [authStatus, user, updateAuthNotifiableStatus, setAuthNotifiable, setUserAttached])

  // Handle FCM token updates - REMOVED to prevent re-initialization loop
  // The token refresh handler in setupFCMMessageHandlers already handles token updates
  // useEffect(() => {
  //   const handleFCMTokenUpdate = async () => {
  //     if (!fcmToken) return
  //
  //     try {
  //       await initializeNotifications(primaryLanguage as LocaleEnum)
  //     } catch (error) {
  //       console.error('Failed to reinitialize notifications after FCM token update:', error)
  //     }
  //   }
  //
  //   handleFCMTokenUpdate()
  // }, [fcmToken, initializeNotifications, primaryLanguage])

  const { handleAddNotificationToCache } = useAddNotificationToCache()
  const { handleInvalidateUnviewedNotificationCount, handleInvalidateAllUserPushNotifications } =
    useInvalidateNotificationQuery()

  const handleOnMessageForegroundReceived = (message: any) => {
    console.log('🔥 UnifiedNotificationProvider: FCM message foreground received:', message)

    // Extract notification data and add to cache
    const notificationData = message.data
    if (notificationData) {
      // Transform FCM message to UserPushNotification format
      const userNotification = {
        id: notificationData.id || notificationData.trackingId || Date.now().toString(),
        trackingId: notificationData.trackingId || notificationData.id || Date.now().toString(),
        user: user?.id || null,
        type: notificationData.type || 'GENERAL',
        title: message.notification?.title || notificationData.title || '',
        description: message.notification?.body || notificationData.body || '',
        data: notificationData,
        viewed: false,
        viewedAt: null,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      }

      // Try to add notification to cache (all tab and not-viewed tab) and increment count
      const { countUpdated, tabsUpdated } = handleAddNotificationToCache(userNotification)

      // Invalidate queries that don't have cache so they will fetch fresh data when needed
      if (!countUpdated) {
        console.log('🔥 UnifiedNotificationProvider: Count not in cache, invalidating count query')
        handleInvalidateUnviewedNotificationCount()
      }

      if (!tabsUpdated) {
        console.log('🔥 UnifiedNotificationProvider: Tabs not in cache, invalidating tab queries')
        handleInvalidateAllUserPushNotifications()
      }

      if (countUpdated && tabsUpdated) {
        console.log('🔥 UnifiedNotificationProvider: All caches updated successfully!')
      }
    }
  }

  const handleOnMessageBackgroundReceived = (message: any) => {
    console.log('🔥 UnifiedNotificationProvider: FCM message background received:', message)

    // Extract notification data and add to cache
    const notificationData = message.data
    if (notificationData) {
      // Transform FCM message to UserPushNotification format
      const userNotification = {
        id: notificationData.id || notificationData.trackingId || Date.now().toString(),
        trackingId: notificationData.trackingId || notificationData.id || Date.now().toString(),
        user: user?.id || null,
        type: notificationData.type || 'GENERAL',
        title: message.notification?.title || notificationData.title || '',
        description: message.notification?.body || notificationData.body || '',
        data: notificationData,
        viewed: false,
        viewedAt: null,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      }

      // Try to add notification to cache (all tab and not-viewed tab) and increment count
      const { countUpdated, tabsUpdated } = handleAddNotificationToCache(userNotification)

      // Invalidate queries that don't have cache so they will fetch fresh data when needed
      if (!countUpdated) {
        console.log('🔥 UnifiedNotificationProvider: Count not in cache, invalidating count query')
        handleInvalidateUnviewedNotificationCount()
      }

      if (!tabsUpdated) {
        console.log('🔥 UnifiedNotificationProvider: Tabs not in cache, invalidating tab queries')
        handleInvalidateAllUserPushNotifications()
      }

      if (countUpdated && tabsUpdated) {
        console.log('🔥 UnifiedNotificationProvider: All caches updated successfully!')
      }
    }
  }

  const handleOnNotificationOpened = (message: any) => {
    console.log('🔥 UnifiedNotificationProvider: FCM notification opened:', message)

    // Handle notification tap - navigate to appropriate screen
    const data = message.data
    if (data) {
      setTimeout(() => {
        handleNotificationActionFromData(data)
      }, 1000)
    }
  }

  // Setup FCM message handlers - do this ONCE on mount, before initialization
  useEffect(() => {
    console.log('🔥 UnifiedNotificationProvider: Setting up FCM handlers')
    console.log(
      '🔥 UnifiedNotificationProvider: FCM available:',
      unifiedNotificationService.isFCMAvailable(),
    )
    console.log(
      '🔥 UnifiedNotificationProvider: Handlers already set up:',
      fcmHandlersSetupRef.current,
    )

    if (!unifiedNotificationService.isFCMAvailable() || fcmHandlersSetupRef.current) {
      console.log('🔥 UnifiedNotificationProvider: Skipping FCM handler setup')
      return
    }

    fcmHandlersSetupRef.current = true // Mark as set up
    console.log('🔥 UnifiedNotificationProvider: Setting up FCM message handlers')

    if (expoNotificationListenerRef.current) {
      expoNotificationListenerRef.current.remove()
    }

    expoNotificationListenerRef.current = Notifications.addNotificationResponseReceivedListener(
      (response) => {
        console.log(
          '🔥 UnifiedNotificationProvider: Expo notification response received:',
          response,
        )
        console.log('🔥 UnifiedNotificationProvider: Response notification:', response.notification)
        console.log(
          '🔥 UnifiedNotificationProvider: Response actionIdentifier:',
          response.actionIdentifier,
        )
      },
    )

    // Setup FCM token refresh handler
    const tokenRefreshUnsubscribe = unifiedNotificationService.setupFCMTokenRefreshHandler(
      async (refreshedToken) => {
        setFcmToken(refreshedToken)
        setFcmIsRegistered(true)
        setFcmError(null)

        // Reinitialize notifications with the new token
        try {
          await initializeNotifications(primaryLanguage as LocaleEnum)
        } catch (error) {
          console.error('Failed to reinitialize after token refresh:', error)
        }
      },
    )

    return () => {
      if (tokenRefreshUnsubscribe) {
        tokenRefreshUnsubscribe()
      }
      if (expoNotificationListenerRef.current) {
        expoNotificationListenerRef.current.remove()
        expoNotificationListenerRef.current = null
      }
    }
  }, [])

  useEffect(() => {
    // Setup FCM message handlers ONCE to catch background notifications
    unifiedNotificationService.setupFCMMessageHandlers(
      handleOnMessageForegroundReceived,
      handleOnMessageBackgroundReceived,
      handleOnNotificationOpened,
    )
    //eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user])
  // Handle language changes
  useEffect(() => {
    const handleLanguageChange = async () => {
      if (!initializationRef.current) return

      const newLanguage = primaryLanguage as LocaleEnum
      if (newLanguage !== language) {
        try {
          await updateNotificationLanguage(newLanguage)
        } catch (error) {
          console.error('Failed to update notification language:', error)
        }
      }
    }

    handleLanguageChange()
  }, [primaryLanguage, language, updateNotificationLanguage])

  // Retry auth notifiable update when token becomes valid AFTER user is already authenticated
  useEffect(() => {
    if (!initializationRef.current) return

    // Skip if we've already updated authNotifiable for this session
    if (authNotifiableUpdatedRef.current) return

    const handleAuthNotifiableUpdate = async () => {
      const { authNotifiable } = useUnifiedNotificationStore.getState()

      // If token becomes valid and user is authenticated, update backend (only once)
      if (pushToken && pushTokenIsValid && authStatus === 'success' && user && authNotifiable) {
        try {
          console.log('🔄 Token is now valid, updating authNotifiable on backend...')
          await updateAuthNotifiableStatus(true)
          console.log('✅ AuthNotifiable updated after token became valid')
          authNotifiableUpdatedRef.current = true // Mark as updated
          handleResetAllNotificationQueries()
        } catch (error) {
          console.error('❌ Failed to update authNotifiable after token became valid:', error)
        }
      }
    }

    handleAuthNotifiableUpdate()
    //eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pushToken, pushTokenIsValid, authStatus, user, updateAuthNotifiableStatus])

  // Retry user attachment when token becomes available
  // Consolidated to avoid multiple simultaneous attempts
  useEffect(() => {
    // Skip if already attached or not initialized
    if (userAttached || !initializationRef.current) {
      return
    }

    // Only proceed if user is authenticated and token is valid
    if (authStatus === 'success' && user && pushToken && pushTokenIsValid) {
      // Use a small delay to avoid race conditions during OAuth login
      const timer = setTimeout(() => {
        // Double-check conditions before attaching (state might have changed)
        const currentState = useUnifiedNotificationStore.getState()
        if (!currentState.userAttached && currentState.pushToken && currentState.pushTokenIsValid) {
          attachUserToToken().catch((error) => {
            console.error('Failed to attach user to token in retry effect:', error)
          })
        }
      }, 1000) // 1 second delay for OAuth timing issues

      return () => clearTimeout(timer)
    }
  }, [pushToken, pushTokenIsValid, authStatus, user, userAttached, attachUserToToken])

  // Listen to app state changes and check permissions when app comes to foreground
  useEffect(() => {
    const checkPermissionsOnAppStateChange = async (nextAppState: AppStateStatus) => {
      // When app comes to foreground from background/inactive

      if (appStateRef.current.match(/inactive|background/) && nextAppState === 'active') {
        try {
          // Check FCM permissions
          const permissionStatus = await unifiedNotificationService.checkFCMPermissions()

          const isGranted = permissionStatus === 'granted' || permissionStatus === 1 // 1 is AuthorizationStatus.AUTHORIZED
          setFcmPermissionGranted(isGranted)
        } catch {
          setFcmPermissionGranted(false)
        }
      }

      appStateRef.current = nextAppState
    }

    const subscription = AppState.addEventListener('change', checkPermissionsOnAppStateChange)

    return () => {
      subscription.remove()
    }
  }, [setFcmPermissionGranted])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (tokenCreationTimeoutRef.current) {
        clearTimeout(tokenCreationTimeoutRef.current)
      }
      if (expoNotificationListenerRef.current) {
        expoNotificationListenerRef.current.remove()
        expoNotificationListenerRef.current = null
      }
    }
  }, [])
  if (!rootNavigationState?.key) return null
  return <>{children}</>
}
