import { KeyboardAvoidingView, Platform } from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import { MedicalDictionarySearchHistory } from '../../components/MedicalDictionarySearchHistory/MedicalDictionarySearchHistory'

export default function MedicalDictionarySearchHistoryScreen() {
  return (
    <SafeAreaView className="flex-1 bg-white" edges={['left', 'right', 'bottom']}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        className="flex-1"
        enabled={true}
      >
        <MedicalDictionarySearchHistory />
      </KeyboardAvoidingView>
    </SafeAreaView>
  )
}
