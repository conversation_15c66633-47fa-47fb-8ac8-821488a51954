import GalleryIcon from '@/assets/icons/gallery-icon.svg'

import { CustomRenderHTML } from '@/components/CustomRenderHTML/CustomRenderHTML'
import { AudioIcon } from '@/components/Icons/AudioIcon'
import { Text } from '@/components/ui/Text/Text'
import { BLURHASH_CODE } from '@/constants/global.constant'
import { LocaleEnum } from '@/enums/locale.enum'
import { useKeywordAudio } from '@/hooks/common/useKeywordAudio'

import MessageQuestionIcon from '@/assets/icons/message-question-icon.svg'
import { useAppLanguage } from '@/hooks/common/useAppLanguage'
import { StyledExpoImage } from '@/libs/styled'
import { primary } from '@/styles/_colors'
import { LocalizeField } from '@/types/global.type'
import { Keyword } from '@/types/keyword.type'
import { Media } from '@/types/media.type'
import { LinearGradient } from 'expo-linear-gradient'
import React, { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { ActivityIndicator, ScrollView, TouchableOpacity, View } from 'react-native'
import Svg, { Path } from 'react-native-svg'
interface KeywordDetailsContentProps {
  keyword: Keyword
}

export const KeywordDetailsContent = ({ keyword }: KeywordDetailsContentProps) => {
  const { description, relatedImages, examples } = keyword || {}

  return (
    <View className="flex flex-col gap-y-3 px-4 py-3">
      {/* Translations */}
      <TranslationsSection keyword={keyword} />

      {/* Examples */}
      {examples && <ExamplesSection examples={examples} />}
      {/* Description */}
      {description && <DescriptionSection description={description as unknown as string} />}

      {/* Related Images */}
      {relatedImages?.length ? <RelatedImagesSection relatedImages={relatedImages} /> : null}
    </View>
  )
}

// Section Components
interface TranslationsSectionProps {
  keyword: Keyword
}

const TranslationsSection = ({ keyword }: TranslationsSectionProps) => {
  const { name, hiragana } = keyword || {}
  const { handlePlayAudio, isAudioLoading, isPlaying } = useKeywordAudio({ keyword })
  const localizedName = name as unknown as LocalizeField<string>

  return (
    <View className="flex flex-col gap-y-3 rounded-lg bg-custom-background-form p-3">
      <View className="flex-row items-start justify-between gap-3 ">
        <View className="flex-1 flex-col gap-2 ">
          <View className="flex-row items-center justify-between gap-3">
            <Text size="heading7" variant="primary" className="line-clamp-1 block break-words">
              {localizedName[LocaleEnum.JA]}
            </Text>
          </View>
          {hiragana && (
            <Text size="body7" variant="subdued">
              /{hiragana}/
            </Text>
          )}
          <Text size="body4" variant="default">
            {localizedName[LocaleEnum.VI]}
          </Text>
        </View>
        <View className="mt-1 shrink-0">
          {isAudioLoading ? (
            <ActivityIndicator size="small" color={primary[500]} />
          ) : (
            <TouchableOpacity
              className="h-5 w-5 shrink-0 items-center justify-center p-0"
              onPress={() => {
                handlePlayAudio()
              }}
            >
              <AudioIcon width={24} height={24} isPlaying={isPlaying} />
            </TouchableOpacity>
          )}
        </View>
      </View>
    </View>
  )
}

interface ExamplesSectionProps {
  examples: Keyword['examples']
}

const ExamplesSection = ({ examples }: ExamplesSectionProps) => {
  const { primaryLanguage } = useAppLanguage()

  if (!examples?.length) return null
  return (
    <View className="flex flex-col gap-y-2 rounded-lg bg-white ">
      <View className="flex-col  gap-y-2">
        {examples?.map((example) => {
          const { jaSentence, translations } = example
          return (
            <View key={example.id} className="flex flex-col gap-y-2">
              <View className="flex-row items-center gap-x-1">
                <Svg width={16} height={16} viewBox="0 0 16 16" fill="none" className="shrink-0">
                  <Path
                    d="M9.77695 7.16042L8.68251 6.06598L6.89918 4.28264C6.5214 3.91042 5.87695 4.17709 5.87695 4.71042V8.17153V11.2882C5.87695 11.8215 6.5214 12.0882 6.89918 11.7104L9.77695 8.83264C10.2381 8.37709 10.2381 7.62153 9.77695 7.16042Z"
                    fill="#1157C8"
                  />
                </Svg>
                <View className="flex-1">
                  <Text size="body6" variant="default" className="whitespace-pre-wrap break-words">
                    {jaSentence}
                  </Text>
                </View>
              </View>
              <View>
                <Text size="body7" variant="subdued" className="whitespace-pre-wrap break-words">
                  {translations?.[primaryLanguage as LocaleEnum]}
                </Text>
              </View>
            </View>
          )
        })}
      </View>
    </View>
  )
}
interface DescriptionSectionProps {
  description: string
}

const DescriptionSection = ({ description }: DescriptionSectionProps) => {
  const { t } = useTranslation()
  const [isDescriptionExpanded, setIsDescriptionExpanded] = useState(false)
  const [contentHeight, setContentHeight] = useState(0)
  const maxHeight = 400

  // TODO: Remove this after the API is fixed
  if (description === '<div class=\"payload-richtext\"></div>') return null
  return (
    <View className="flex flex-col gap-y-2 rounded-lg bg-white py-3">
      <View className="flex-row items-center gap-x-2">
        <MessageQuestionIcon width={24} height={24} />
        <Text size="body3" variant="default">
          {t('MES-553')}
        </Text>
      </View>
      <View className="relative overflow-hidden">
        <View style={{ maxHeight: isDescriptionExpanded ? undefined : maxHeight }}>
          <View
            onLayout={(event) => {
              const { height } = event.nativeEvent.layout
              setContentHeight(height)
            }}
          >
            <CustomRenderHTML htmlContent={description} />
          </View>
        </View>

        {!isDescriptionExpanded && contentHeight > maxHeight && (
          <LinearGradient
            colors={['rgba(255,255,255,0)', 'rgba(255,255,255,0.9)', 'rgba(255,255,255,1)']}
            style={{
              position: 'absolute',
              bottom: 0,
              left: 0,
              right: 0,
              height: 60,
              zIndex: 5,
            }}
          />
        )}
      </View>

      {contentHeight > maxHeight && (
        <TouchableOpacity
          onPress={() => setIsDescriptionExpanded(!isDescriptionExpanded)}
          className="sell-start rounded-full py-1"
        >
          <Text size="link3" variant="primary" className="text-center">
            {isDescriptionExpanded ? t('MES-595') : t('MES-594')}
          </Text>
        </TouchableOpacity>
      )}
    </View>
  )
}

interface RelatedImagesSectionProps {
  relatedImages: Keyword['relatedImages']
}

const RelatedImagesSection = ({ relatedImages }: RelatedImagesSectionProps) => {
  const { t } = useTranslation()

  return (
    <View className="flex flex-col gap-y-4">
      <View className="flex-row items-center gap-x-2">
        <GalleryIcon width={24} height={24} />
        <Text size="body3" variant="default">
          {t('MES-573')}
        </Text>
      </View>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={{ gap: 12 }}
        className="bg-white "
        pagingEnabled
        snapToInterval={240 + 12}
        decelerationRate={'fast'}
      >
        {relatedImages?.map((image, index) => {
          const media = image as Media
          const imageUrl = media?.thumbnailURL || media?.url || ''

          if (!imageUrl) return null
          return (
            <StyledExpoImage
              source={{
                uri: imageUrl,
              }}
              key={index}
              contentFit="cover"
              className="aspect-video w-[240px] rounded-lg"
              placeholder={BLURHASH_CODE}
            />
          )
        })}
      </ScrollView>
    </View>
  )
}
